from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from tasks.etc.task_chat_session import (
    SupervisorTask_ChangeSession,
    create_task_manage_chat_sessions,
    new_chat_session_tool,
    change_chat_session_tool,
    list_chat_sessions_tool
)
from managers.manager_supervisors import SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import ZairaUser
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID, uuid4
from pydantic import ValidationError

# Import the module to access the underlying tools
from tasks.etc import task_chat_session

class TestChatSessionFunctions:
    """Test class for chat session functions"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_session_guid = uuid4()
        self.test_state = SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input="test input",
            additional_input={},
            messages=[]
        )
    
    @pytest.mark.asyncio
    async def test_new_chat_session_success(self):
        """Test successful creation of new chat session"""
        mock_user = MagicMock()
        mock_user.sessionGUID = self.test_session_guid
        mock_user.chat_history = {}
        
        with patch('tasks.etc.task_chat_session.ZairaUserManager.find_user') as mock_find_user:
            mock_find_user.return_value = mock_user
            
            # Call the tool's async method directly
            result = await new_chat_session_tool._arun(self.test_state)
            
            assert "New chat history session has been started with GUID" in result
            assert mock_user.sessionGUID is not None
            assert isinstance(mock_user.sessionGUID, UUID)
    
    @pytest.mark.asyncio
    async def test_new_chat_session_user_not_found(self):
        """Test new chat session when user not found"""
        with patch('tasks.etc.task_chat_session.ZairaUserManager.find_user') as mock_find_user:
            mock_find_user.return_value = None
            
            result = await new_chat_session_tool._arun(self.test_state)
            
            assert result == "User not found"
    
    @pytest.mark.asyncio
    async def test_change_chat_session_success(self):
        """Test successful chat session change"""
        session_guid_str = str(self.test_session_guid)
        mock_user = MagicMock()
        mock_user.sessionGUID = uuid4()
        mock_user.chat_history = {self.test_session_guid: []}
        
        with patch('tasks.etc.task_chat_session.ZairaUserManager.find_user') as mock_find_user:
            mock_find_user.return_value = mock_user
            
            result = await change_chat_session_tool._arun(session_guid_str, self.test_state)
            
            assert "Chat session changed to" in result
            assert mock_user.sessionGUID == self.test_session_guid
    
    @pytest.mark.asyncio
    async def test_change_chat_session_invalid_guid(self):
        """Test change chat session with invalid GUID format"""
        invalid_guid = "invalid-guid-format"
        mock_user = MagicMock()
        
        with patch('tasks.etc.task_chat_session.ZairaUserManager.find_user') as mock_find_user:
            mock_find_user.return_value = mock_user
            
            result = await change_chat_session_tool._arun(invalid_guid, self.test_state)
            
            assert "Invalid session GUID format" in result
    
    @pytest.mark.asyncio
    async def test_change_chat_session_not_found(self):
        """Test change chat session when session doesn't exist"""
        nonexistent_guid = str(uuid4())
        mock_user = MagicMock()
        mock_user.chat_history = {}
        
        with patch('tasks.etc.task_chat_session.ZairaUserManager.find_user') as mock_find_user:
            mock_find_user.return_value = mock_user
            
            result = await change_chat_session_tool._arun(nonexistent_guid, self.test_state)
            
            assert "not found" in result
    
    @pytest.mark.asyncio
    async def test_list_chat_sessions_success(self):
        """Test successful listing of chat sessions"""
        session1 = uuid4()
        session2 = uuid4()
        mock_user = MagicMock()
        mock_user.sessionGUID = session1
        mock_user.chat_history = {session1: [], session2: []}
        
        with patch('tasks.etc.task_chat_session.ZairaUserManager.find_user') as mock_find_user:
            mock_find_user.return_value = mock_user
            
            result = await list_chat_sessions_tool._arun(self.test_state)
            
            assert "Current session:" in result
            assert "All sessions:" in result
            assert str(session1) in result
            assert str(session2) in result
    
    @pytest.mark.asyncio
    async def test_list_chat_sessions_empty(self):
        """Test listing chat sessions when none exist"""
        mock_user = MagicMock()
        mock_user.chat_history = {}
        
        with patch('tasks.etc.task_chat_session.ZairaUserManager.find_user') as mock_find_user:
            mock_find_user.return_value = mock_user
            
            result = await list_chat_sessions_tool._arun(self.test_state)
            
            assert result == "No chat sessions found"

class TestSupervisorTaskChangeSession:
    """Test class for SupervisorTask_ChangeSession"""
    
    def test_class_creation(self):
        """Test SupervisorTask_ChangeSession can be instantiated"""
        task = SupervisorTask_ChangeSession(
            name="test_change_session",
            tools=[new_chat_session_tool, change_chat_session_tool, list_chat_sessions_tool],
            prompt_id="Test_Change_Chat_Session"
        )
        
        assert task.name == "test_change_session"
        assert len(task.get_tools()) == 3
        assert task.prompt_id == "Test_Change_Chat_Session"
    
    @pytest.mark.asyncio
    async def test_llm_call_delegates_to_parent(self):
        """Test that llm_call delegates to parent class"""
        task = SupervisorTask_ChangeSession(
            name="test_change_session",
            tools=[new_chat_session_tool, change_chat_session_tool, list_chat_sessions_tool],
            prompt_id="Test_Change_Chat_Session"
        )
        
        test_state = SupervisorTaskState(
            user_guid=str(uuid4()),
            original_input="test input",
            additional_input={},
            messages=[]
        )
        
        with patch.object(task.__class__.__bases__[0], 'llm_call', new_callable=AsyncMock) as mock_parent_call:
            await task.llm_call(test_state)
            mock_parent_call.assert_called_once_with(test_state)

class TestCreateTaskFunction:
    """Test class for create_task_manage_chat_sessions function"""
    
    @pytest.mark.asyncio
    async def test_create_task_manage_chat_sessions(self):
        """Test task creation and registration"""
        with patch('tasks.etc.task_chat_session.SupervisorManager.get_instance') as mock_manager:
            mock_manager.return_value.register_task.return_value = MagicMock()
            
            result = await create_task_manage_chat_sessions()
            
            mock_manager.return_value.register_task.assert_called_once()
            args = mock_manager.return_value.register_task.call_args[0]
            task_instance = args[0]
            
            assert isinstance(task_instance, SupervisorTask_ChangeSession)
            assert task_instance.name == "manage_sessions_task"
            assert len(task_instance.get_tools()) == 3
            assert task_instance.prompt_id == "Task_Manage_Chat_Sessions"

class TestUUIDHandling:
    """Test class for UUID handling edge cases"""
    
    def test_uuid_string_conversion(self):
        """Test UUID to string conversion works correctly"""
        test_uuid = uuid4()
        uuid_str = str(test_uuid)
        
        assert isinstance(uuid_str, str)
        assert UUID(uuid_str) == test_uuid
    
    def test_invalid_uuid_string(self):
        """Test handling of invalid UUID strings"""
        invalid_uuids = [
            "not-a-uuid",
            "12345",
            "",
            "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
        ]
        
        for invalid_uuid in invalid_uuids:
            with pytest.raises(ValueError):
                UUID(invalid_uuid)
    
    def test_none_uuid_handling(self):
        """Test handling of None UUID"""
        with pytest.raises(TypeError):
            UUID(None)