{"python.defaultInterpreterPath": "../../.venv/Scripts/python.exe", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.nosetestsEnabled": false, "python.testing.pytestArgs": ["tests/", "-v", "-s"], "python.testing.cwd": "${workspaceFolder}", "python.testing.autoTestDiscoverOnSavePattern": "**/*.py", "files.associations": {"*.bat": "batch"}, "batch-runner.enableRightClickContextMenu": true, "batch-runner.showTerminal": true, "batch-runner.clearTerminal": false}