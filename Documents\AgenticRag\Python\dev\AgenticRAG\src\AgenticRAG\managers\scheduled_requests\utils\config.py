from imports import *
from typing import Dict, Any
from pydantic import BaseModel, Field
from userprofiles.ZairaUser import PERMISSION_LEVELS

class UserQuotaConfig(BaseModel):
    """Configuration for user quotas based on permission level"""
    max_concurrent_tasks: int = Field(..., description="Maximum concurrent scheduled tasks")
    daily_task_limit: int = Field(..., description="Maximum tasks per day")
    memory_limit_mb: int = Field(..., description="Memory limit in MB")
    thread_pool_size: int = Field(..., description="Thread pool size for user")
    max_task_duration_hours: int = Field(default=24, description="Maximum task duration in hours")
    max_recurring_tasks: int = Field(..., description="Maximum recurring tasks")
    
    class Config:
        validate_assignment = True

class RateLimitConfig(BaseModel):
    """Configuration for rate limiting"""
    requests_per_minute: int = Field(..., description="Requests per minute limit")
    requests_per_hour: int = Field(..., description="Requests per hour limit")
    burst_limit: int = Field(..., description="Burst request limit")
    
    class Config:
        validate_assignment = True

class ScheduledRequestsConfig:
    """Central configuration for scheduled requests system"""
    
    # Default quota configurations by permission level
    QUOTA_CONFIGS: Dict[PERMISSION_LEVELS, UserQuotaConfig] = {
        PERMISSION_LEVELS.ADMIN: UserQuotaConfig(
            max_concurrent_tasks=100,
            daily_task_limit=1000,
            memory_limit_mb=1000,
            thread_pool_size=20,
            max_task_duration_hours=168,  # 1 week
            max_recurring_tasks=50
        ),
        PERMISSION_LEVELS.USER: UserQuotaConfig(
            max_concurrent_tasks=10,
            daily_task_limit=50,
            memory_limit_mb=200,
            thread_pool_size=5,
            max_task_duration_hours=24,
            max_recurring_tasks=5
        ),
        PERMISSION_LEVELS.GUEST: UserQuotaConfig(
            max_concurrent_tasks=3,
            daily_task_limit=10,
            memory_limit_mb=50,
            thread_pool_size=2,
            max_task_duration_hours=12,
            max_recurring_tasks=1
        )
    }
    
    # Rate limiting configurations by permission level
    RATE_LIMIT_CONFIGS: Dict[PERMISSION_LEVELS, RateLimitConfig] = {
        PERMISSION_LEVELS.ADMIN: RateLimitConfig(
            requests_per_minute=100,
            requests_per_hour=1000,
            burst_limit=200
        ),
        PERMISSION_LEVELS.USER: RateLimitConfig(
            requests_per_minute=10,
            requests_per_hour=100,
            burst_limit=20
        ),
        PERMISSION_LEVELS.GUEST: RateLimitConfig(
            requests_per_minute=2,
            requests_per_hour=20,
            burst_limit=5
        )
    }
    
    # System-wide configurations
    DATABASE_POOL_SIZE: int = 20
    CLEANUP_INTERVAL_HOURS: int = 24
    METRICS_RETENTION_DAYS: int = 30
    MANAGER_CLEANUP_INTERVAL_MINUTES: int = 30
    INACTIVE_MANAGER_TIMEOUT_MINUTES: int = 60
    
    # Security configurations
    AUDIT_LOG_RETENTION_DAYS: int = 90
    SESSION_TIMEOUT_MINUTES: int = 30
    MAX_FAILED_ATTEMPTS: int = 5
    LOCKOUT_DURATION_MINUTES: int = 15
    
    # Performance configurations
    BATCH_SIZE: int = 100
    MAX_CONCURRENT_MIGRATIONS: int = 5
    HEALTH_CHECK_INTERVAL_SECONDS: int = 30
    
    @classmethod
    def get_user_quota_config(cls, permission_level: PERMISSION_LEVELS) -> UserQuotaConfig:
        """Get quota configuration for user permission level"""
        return cls.QUOTA_CONFIGS.get(permission_level, cls.QUOTA_CONFIGS[PERMISSION_LEVELS.GUEST])
    
    @classmethod
    def get_rate_limit_config(cls, permission_level: PERMISSION_LEVELS) -> RateLimitConfig:
        """Get rate limit configuration for user permission level"""
        return cls.RATE_LIMIT_CONFIGS.get(permission_level, cls.RATE_LIMIT_CONFIGS[PERMISSION_LEVELS.GUEST])
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate configuration consistency"""
        try:
            for permission_level in PERMISSION_LEVELS:
                quota_config = cls.get_user_quota_config(permission_level)
                rate_limit_config = cls.get_rate_limit_config(permission_level)
                
                # Validate quota config
                if quota_config.max_concurrent_tasks <= 0:
                    raise ValueError(f"Invalid max_concurrent_tasks for {permission_level}")
                
                # Validate rate limit config
                if rate_limit_config.requests_per_minute <= 0:
                    raise ValueError(f"Invalid requests_per_minute for {permission_level}")
                    
            return True
        except Exception as e:
            LogFire.log("ERROR", f"Configuration validation failed: {str(e)}")
            return False