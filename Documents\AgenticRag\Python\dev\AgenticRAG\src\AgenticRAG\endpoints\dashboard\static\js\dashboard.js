// Dashboard Main JavaScript Functions - Template-based Version
let currentView = 'user-list';
let selectedUserGuid = null;
let templateCache = new Map();

// Template loading system
const TemplateLoader = {
    cache: new Map(),
    
    // Clear template cache
    clearCache() {
        this.cache.clear();
        console.log('Template cache cleared');
    },
    
    async loadTemplate(templateName, context = {}) {
        // Check cache first
        const cacheKey = templateName + JSON.stringify(context);
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        try {
            // Build query string for template context
            const queryString = new URLSearchParams(context).toString();
            // Add cache-busting timestamp to ensure fresh templates during development
            const timestamp = new Date().getTime();
            const separator = queryString ? '&' : '?';
            const url = `/dashboard/templates/${templateName}${queryString ? '?' + queryString : ''}${separator}_t=${timestamp}`;
            
            const response = await safeFetch(url);
            const html = await response.text();
            
            // Cache the result
            this.cache.set(cacheKey, html);
            return html;
        } catch (error) {
            console.error(`Failed to load template ${templateName}:`, error);
            return `<div class="error">Failed to load template: ${templateName}</div>`;
        }
    },
    
    renderTemplate(templateHtml, data) {
        // Simple template variable replacement
        let rendered = templateHtml;
        for (const [key, value] of Object.entries(data)) {
            const regex = new RegExp(`\\{${key}\\}`, 'g');
            rendered = rendered.replace(regex, value || '');
        }
        return rendered;
    },
    
    async renderTemplateWithData(templateName, data) {
        const template = await this.loadTemplate(templateName);
        return this.renderTemplate(template, data);
    }
};

// Browser compatibility check and fallbacks
const BrowserCompat = {
    // Check for modern JavaScript features
    features: {
        fetch: typeof fetch !== 'undefined',
        localStorage: (function() {
            try {
                return 'localStorage' in window && window.localStorage !== null;
            } catch (e) {
                return false;
            }
        })(),
        arrow_functions: (function() {
            try {
                eval('() => {}');
                return true;
            } catch (e) {
                return false;
            }
        })(),
        template_literals: (function() {
            try {
                eval('`template`');
                return true;
            } catch (e) {
                return false;
            }
        })(),
        const_let: (function() {
            try {
                eval('const test = 1; let test2 = 2;');
                return true;
            } catch (e) {
                return false;
            }
        })()
    },
    
    // Show compatibility warning
    async showCompatibilityWarning() {
        const template = await TemplateLoader.loadTemplate('browser_warning.html');
        const warningDiv = document.createElement('div');
        warningDiv.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: #dc3545;
            color: white;
            padding: 10px;
            text-align: center;
            z-index: 9999;
            font-family: Arial, sans-serif;
        `;
        warningDiv.innerHTML = template || `
            <strong>Browser Compatibility Warning:</strong> 
            Your browser may not support all dashboard features. 
            Please update to a modern browser for the best experience.
            <button onclick="this.parentElement.style.display='none'" style="background: none; border: 1px solid white; color: white; margin-left: 10px; padding: 2px 8px; cursor: pointer;">
                Dismiss
            </button>
        `;
        document.body.insertBefore(warningDiv, document.body.firstChild);
    },
    
    // Fetch fallback using XMLHttpRequest
    fetchFallback: function(url, options = {}) {
        return new Promise(function(resolve, reject) {
            var xhr = new XMLHttpRequest();
            xhr.open(options.method || 'GET', url);
            
            if (options.headers) {
                for (var key in options.headers) {
                    xhr.setRequestHeader(key, options.headers[key]);
                }
            }
            
            xhr.onload = function() {
                var response = {
                    status: xhr.status,
                    text: function() {
                        return Promise.resolve(xhr.responseText);
                    },
                    json: function() {
                        return Promise.resolve(JSON.parse(xhr.responseText));
                    }
                };
                resolve(response);
            };
            
            xhr.onerror = function() {
                reject(new Error('Network error'));
            };
            
            xhr.send(options.body);
        });
    },
    
    // Safe localStorage with fallback
    storage: {
        setItem: function(key, value) {
            try {
                if (BrowserCompat.features.localStorage) {
                    localStorage.setItem(key, value);
                } else {
                    // Fallback to in-memory storage
                    BrowserCompat._memoryStorage = BrowserCompat._memoryStorage || {};
                    BrowserCompat._memoryStorage[key] = value;
                }
            } catch (e) {
                console.warn('Could not save to storage:', e);
            }
        },
        
        getItem: function(key) {
            try {
                if (BrowserCompat.features.localStorage) {
                    return localStorage.getItem(key);
                } else {
                    return BrowserCompat._memoryStorage ? BrowserCompat._memoryStorage[key] : null;
                }
            } catch (e) {
                console.warn('Could not read from storage:', e);
                return null;
            }
        },
        
        removeItem: function(key) {
            try {
                if (BrowserCompat.features.localStorage) {
                    localStorage.removeItem(key);
                } else if (BrowserCompat._memoryStorage) {
                    delete BrowserCompat._memoryStorage[key];
                }
            } catch (e) {
                console.warn('Could not remove from storage:', e);
            }
        }
    },
    
    // Check compatibility and initialize fallbacks
    init: function() {
        var hasIssues = false;
        
        if (!this.features.fetch) {
            console.warn('Fetch API not supported, using XMLHttpRequest fallback');
            hasIssues = true;
        }
        
        if (!this.features.localStorage) {
            console.warn('localStorage not supported, using memory fallback');
            hasIssues = true;
        }
        
        if (!this.features.arrow_functions || !this.features.template_literals || !this.features.const_let) {
            console.warn('Modern JavaScript features not supported');
            hasIssues = true;
        }
        
        if (hasIssues) {
            this.showCompatibilityWarning();
        }
        
        return !hasIssues;
    }
};

// Safe fetch function that uses fallback
function safeFetch(url, options) {
    if (BrowserCompat.features.fetch) {
        return fetch(url, options);
    } else {
        return BrowserCompat.fetchFallback(url, options);
    }
}

// Session timeout handling
async function handleSessionTimeout(data) {
    // Save current state before redirecting using safe storage
    BrowserCompat.storage.setItem('dashboard_state', JSON.stringify({
        currentView: currentView,
        selectedUserGuid: selectedUserGuid,
        timestamp: new Date().toISOString()
    }));
    
    // Show session expired message using template
    const template = await TemplateLoader.renderTemplateWithData('session_expired.html', {
        message: data.error || 'Your session has expired. Please refresh the page to continue.'
    });
    
    const body = document.body;
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        color: white;
        font-family: Arial, sans-serif;
    `;
    
    overlay.innerHTML = template;
    body.appendChild(overlay);
}

// Check and restore dashboard state after refresh
function restoreDashboardState() {
    try {
        const savedState = BrowserCompat.storage.getItem('dashboard_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            const timeDiff = new Date() - new Date(state.timestamp);
            
            // Only restore if state is less than 5 minutes old
            if (timeDiff < 300000) {
                currentView = state.currentView;
                selectedUserGuid = state.selectedUserGuid;
                
                // Switch to saved view
                if (currentView && currentView !== 'user-list') {
                    showTab(currentView);
                }
                
                // Restore selected user if available
                if (selectedUserGuid) {
                    window.selectedUserGuid = selectedUserGuid;
                }
            }
            
            // Clear old state
            BrowserCompat.storage.removeItem('dashboard_state');
        }
    } catch (error) {
        console.log('Could not restore dashboard state:', error);
    }
}

// Tab functionality with integrated log viewer support and state management
function showTab(tabName) {
    // Prevent recursion by adding a flag
    if (showTab._inProgress) {
        console.log('[DEBUG] showTab: Preventing recursive call for', tabName);
        return;
    }
    
    // Prevent rapid successive tab switches
    const now = Date.now();
    if (showTab._lastSwitch && (now - showTab._lastSwitch) < 100) {
        console.log('[DEBUG] showTab: Debouncing rapid tab switch to', tabName);
        return;
    }
    
    showTab._inProgress = true;
    showTab._lastSwitch = now;
    
    try {
        console.log('[DEBUG] showTab: Switching to tab', tabName);
        
        // Stop chat history polling when leaving chat-history tab
        if (currentView === 'chat-history' && tabName !== 'chat-history') {
            stopChatHistoryPolling();
        }
        
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(tab => tab.classList.remove('active'));
        
        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(btn => btn.classList.remove('active'));
        
        // Show selected tab content
        const targetTab = document.getElementById(tabName);
        if (targetTab) {
            targetTab.classList.add('active');
        } else {
            console.error('[ERROR] showTab: Tab element not found:', tabName);
        }
        
        // Add active class to clicked button
        if (typeof event !== 'undefined' && event && event.target) {
            event.target.classList.add('active');
        }
        
        // Update current view for refresh purposes
        currentView = tabName;
        
        // Initialize features based on the active tab
        if (tabName === 'chat-history' || tabName === 'chat') {
            // Initialize log viewer when switching to chat tab
            setTimeout(() => {
                try {
                    console.log('[DEBUG] showTab: Re-initializing permissions and log viewer for chat tab');
                    
                    // Re-initialize user permissions to ensure proper UI state
                    if (typeof initializeUserPermissions === 'function') {
                        initializeUserPermissions();
                    }
                    
                    // Initialize log viewer for SYSTEM users
                    if (typeof initializeLiveLogViewer === 'function') {
                        initializeLiveLogViewer();
                    }
                } catch (error) {
                    console.error('[ERROR] showTab: Error initializing chat tab features:', error);
                }
            }, 100);
        } else if (tabName === 'user-requests') {
            // Initialize scheduled requests functionality
            setTimeout(() => {
                try {
                    console.log('[DEBUG] showTab: Initializing scheduled requests tab');
                    
                    // Ensure the search controls are properly initialized
                    const guidInput = document.getElementById('userGuidInput');
                    if (guidInput) {
                        console.log('[DEBUG] showTab: Scheduled requests search controls ready');
                    }
                    
                    // Clear any existing polling that might interfere
                    if (typeof stopChatHistoryPolling === 'function') {
                        stopChatHistoryPolling();
                    }
                    
                } catch (error) {
                    console.error('[ERROR] showTab: Error initializing scheduled requests tab:', error);
                }
            }, 100);
        } else if (tabName === 'user-list') {
            // Initialize user list functionality
            setTimeout(() => {
                try {
                    console.log('[DEBUG] showTab: Initializing user list tab');
                    
                    // Clear any active polling from other tabs
                    if (typeof stopChatHistoryPolling === 'function') {
                        stopChatHistoryPolling();
                    }
                    
                } catch (error) {
                    console.error('[ERROR] showTab: Error initializing user list tab:', error);
                }
            }, 100);
        }
        
    } catch (error) {
        console.error('[ERROR] showTab: Exception during tab switching:', error);
    } finally {
        // Always clear the flag
        showTab._inProgress = false;
    }
}

// User search functionality
function filterUsers() {
    const searchTerm = document.getElementById('userSearchInput').value.toLowerCase();
    const userCards = document.querySelectorAll('.user-card');
    
    userCards.forEach(card => {
        const usernameElement = card.querySelector('.username');
        if (usernameElement) {
            const username = usernameElement.textContent.toLowerCase();
            if (username.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }
    });
}

// Load user list
async function loadUserList() {
    const container = document.getElementById('userListContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading users...</div>';
    
    try {
        const response = await safeFetch('/dashboard/api/user-list');
        const data = await response.json();
        
        // Handle session timeout
        if (response.status === 401 || data.error_type === 'session_timeout') {
            handleSessionTimeout(data);
            return;
        }
        
        // Handle rate limiting
        if (response.status === 429 || data.error_type === 'rate_limit') {
            const retryAfter = data.retry_after || 30;
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: 'rate-limit-error',
                title: 'Rate Limit Exceeded',
                message: data.error || 'Too many requests. Please wait before refreshing.',
                details: `Please wait ${retryAfter} seconds before trying again.`,
                retry_button: `<button onclick="setTimeout(loadUserList, ${retryAfter * 1000})" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Auto-retry in ${retryAfter}s
                </button>`
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        if (data.error) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: '',
                title: 'Error',
                message: data.error,
                details: '',
                retry_button: ''
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        const users = data.users || [];
        if (users.length === 0) {
            container.innerHTML = '<div class="loading">No users found</div>';
            return;
        }
        
        // Render user cards using direct HTML generation
        let html = '';
        users.forEach(user => {
            const username = user.username || 'Unknown';
            const rank = user.rank || 'USER';
            const userGuid = user.user_guid;
            
            // Direct HTML generation instead of async template loading
            html += `<div class="user-card" onclick="selectUser('${userGuid}', this)">
                <div style="font-weight: 600; margin-bottom: 0.25rem;">
                    <span class="username">${username}</span>
                    <span style="color: #60a5fa; font-size: 0.85rem; margin-left: 0.5rem;">${rank}</span>
                </div>
                <div style="font-size: 0.85rem; color: #94a3b8;">
                    GUID: ${userGuid}
                </div>
                <div style="margin-top: 0.75rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                    <button onclick="event.stopPropagation(); showTab('user-requests'); document.getElementById('userGuidInput').value='${userGuid}'; loadUserRequests('${userGuid}');" 
                            title="View scheduled requests for this user"
                            onmouseover="this.style.background='#2563eb'"
                            onmouseout="this.style.background='#3b82f6'"
                            style="padding: 0.25rem 0.5rem; background: #3b82f6; color: white; border: none; border-radius: 4px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                        📋 Scheduled Requests
                    </button>
                    <button onclick="event.stopPropagation(); showTab('chat-history'); document.getElementById('chatUserGuidInput').value='${userGuid}'; loadUserChatHistory('${userGuid}');" 
                            title="View chat history for this user"
                            onmouseover="this.style.background='#d97706'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(245, 158, 11, 0.4)'"
                            onmouseout="this.style.background='#f59e0b'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(245, 158, 11, 0.3)'"
                            style="padding: 0.25rem 0.75rem; background: #f59e0b; color: white; border: none; border-radius: 4px; font-size: 0.75rem; font-weight: 600; cursor: pointer; transition: all 0.2s; box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);">
                        💬 Chat History
                    </button>
                </div>
            </div>`;
        });
        
        // Add memory exhaustion warning if applicable
        if (data.is_truncated) {
            const warningTemplate = await TemplateLoader.renderTemplateWithData('warning_box.html', {
                warning_class: 'memory-limit-warning',
                title: 'Large Dataset - Results Limited',
                message: data.truncated_message,
                details: `Showing ${data.total_count} of ${data.actual_total} total users. Use search to find specific users.`,
                action_button: ''
            });
            html += warningTemplate;
        }
        
        container.innerHTML = html;
    } catch (error) {
        // Handle network errors that might indicate rate limiting
        if (error.message.includes('429') || error.message.toLowerCase().includes('rate limit')) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: 'rate-limit-error',
                title: 'Rate Limit Exceeded',
                message: 'Network request was rate limited. Please wait before trying again.',
                details: '',
                retry_button: `<button onclick="setTimeout(loadUserList, 30000)" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Retry in 30s
                </button>`
            });
            container.innerHTML = errorTemplate;
        } else {
            container.innerHTML = `<div class="error">Failed to load users: ${error.message}</div>`;
        }
    }
}

// Select user
function selectUser(userGuid, element) {
    // Update visual selection
    document.querySelectorAll('.user-card').forEach(card => {
        card.classList.remove('selected');
    });
    element.classList.add('selected');
    
    // Store selected user
    window.selectedUserGuid = userGuid;
    selectedUserGuid = userGuid;
    
    // Load user's requests and chat history
    loadUserRequests(userGuid);
    loadUserChatHistory(userGuid);
}

// Load user requests
async function loadUserRequests(userGuid) {
    const container = document.getElementById('requestsContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading scheduled requests...</div>';
    
    try {
        const response = await fetch(`/dashboard/api/user-requests?user_guid=${encodeURIComponent(userGuid)}`);
        const data = await response.json();
        
        if (data.error) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: '',
                title: 'Error',
                message: data.error,
                details: '',
                retry_button: ''
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        const requests = data.requests || [];
        if (requests.length === 0) {
            container.innerHTML = '<div class="loading">No scheduled requests found for this user</div>';
            return;
        }
        
        let html = '<h3>Scheduled Requests</h3>';
        html += '<div style="display: grid; gap: 1rem;">';
        
        // Track requests with missing/incomplete data
        let incompleteRequests = 0;
        
        requests.forEach(request => {
            const statusColor = request.status === 'completed' ? '#10b981' : 
                              request.status === 'failed' ? '#ef4444' : '#f59e0b';
            
            // Check for missing critical data
            const hasIncompleteData = !request.scheduled_guid || !request.status || (!request.description && !request.target_prompt);
            if (hasIncompleteData) {
                incompleteRequests++;
            }
            
            const description = request.description || request.target_prompt || request.schedule_prompt || 'No description available';
            const incompleteFlag = hasIncompleteData ? ' <span style="color: #f59e0b; font-size: 0.8rem;">[Incomplete Data]</span>' : '';
            const scheduledGuid = request.scheduled_guid || 'Unknown';
            const createdAt = request.created_at ? new Date(request.created_at).toLocaleString() : 'Unknown';
            const nextRun = request.next_run ? new Date(request.next_run).toLocaleString() : 'N/A';
            const scheduleInfo = request.schedule_type ? `Schedule: ${request.schedule_type}` : '';
            const statusText = (request.status || 'UNKNOWN').toUpperCase();
            const chatSessionGuid = request.chat_session_guid || 'Unknown';
            const cardStyle = hasIncompleteData ? 'opacity: 0.7; border-left: 3px solid #f59e0b;' : '';
            
            // Direct HTML generation instead of async template loading
            html += `<div class="user-card" style="${cardStyle}">
                <div style="display: flex; justify-content: space-between; align-items: start;">
                    <div style="flex: 1;">
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">
                            ${description}
                            ${incompleteFlag}
                        </div>
                        <div style="font-size: 0.85rem; color: #94a3b8;">
                            ID: ${scheduledGuid}<br>
                            Created: ${createdAt}<br>
                            Next Run: ${nextRun}<br>
                            ${scheduleInfo}
                        </div>
                        <div style="margin-top: 0.75rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button onclick="showCallTrace('${scheduledGuid}')" 
                                    title="View execution call trace for this request"
                                    onmouseover="this.style.background='#2563eb'"
                                    onmouseout="this.style.background='#3b82f6'"
                                    style="padding: 0.25rem 0.5rem; background: #3b82f6; color: white; border: none; border-radius: 4px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                                Call Trace
                            </button>
                            <button onclick="showDebugMessages('${scheduledGuid}')" 
                                    title="View debug messages for this request"
                                    onmouseover="this.style.background='#059669'"
                                    onmouseout="this.style.background='#10b981'"
                                    style="padding: 0.25rem 0.5rem; background: #10b981; color: white; border: none; border-radius: 4px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                                Debug Messages
                            </button>
                            <button onclick="navigateToChatSession('${chatSessionGuid}', '${userGuid}')" 
                                    title="Navigate to chat history for this request"
                                    onmouseover="this.style.background='#d97706'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(245, 158, 11, 0.4)'"
                                    onmouseout="this.style.background='#f59e0b'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(245, 158, 11, 0.3)'"
                                    style="padding: 0.25rem 0.75rem; background: #f59e0b; color: white; border: none; border-radius: 4px; font-size: 0.75rem; font-weight: 600; cursor: pointer; transition: all 0.2s; box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);">
                                📋 Chat History
                            </button>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <span style="display: inline-block; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem; font-weight: 600; background: ${statusColor}20; color: ${statusColor};">
                            ${statusText}
                        </span>
                    </div>
                </div>
            </div>`;
        });
        
        html += '</div>';
        
        // Show warning for incomplete data
        if (incompleteRequests > 0) {
            const warningTemplate = await TemplateLoader.renderTemplateWithData('warning_box.html', {
                warning_class: 'partial-data-warning',
                title: 'Partial Request Data',
                message: `${incompleteRequests} out of ${requests.length} requests have incomplete data due to loading issues.`,
                details: 'Some details may be missing. Try refreshing to load complete data.',
                action_button: `<button onclick="loadUserRequests('${userGuid}')" style="margin-top: 0.5rem; padding: 4px 8px; background: #f59e0b; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 0.8rem;">
                    Reload Requests
                </button>`
            });
            html += warningTemplate;
        }
        
        container.innerHTML = html;
    } catch (error) {
        container.innerHTML = `<div class="error">Failed to load requests: ${error.message}</div>`;
    }
}

// Load user chat history with expandable sessions
async function loadUserChatHistory(userGuid) {
    const container = document.getElementById('chatHistoryContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading chat history...</div>';
    
    try {
        const response = await fetch(`/dashboard/api/user-chat-history?user_guid=${encodeURIComponent(userGuid)}`);
        const data = await response.json();
        
        if (data.error) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: '',
                title: 'Error',
                message: data.error,
                details: '',
                retry_button: ''
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        const sessions = data.sessions_list || [];
        if (sessions.length === 0) {
            container.innerHTML = '<div class="loading">No chat history found for this user</div>';
            return;
        }
        
        let html = '<h3>Chat History</h3>';
        html += '<div style="display: grid; gap: 1rem;">';
        
        sessions.forEach((sessionId, index) => {
            const messages = data.chat_sessions[sessionId] || [];
            const metadata = data.sessions_metadata ? data.sessions_metadata[sessionId] : null;
            const lastMessage = messages[messages.length - 1];
            
            // Use metadata if available, otherwise fallback to calculated values
            const sessionTitle = metadata?.title || `Session ${sessionId.substring(0, 8)}`;
            const messageCount = metadata?.message_count !== undefined ? metadata.message_count : messages.length;
            const lastActivity = metadata?.last_activity ? 
                new Date(metadata.last_activity).toLocaleString() : 
                (lastMessage ? new Date(lastMessage.timestamp).toLocaleString() : 'No activity');
            
            // Expandable session card
            const isExpanded = index === 0; // Expand first session by default
            html += `<div class="chat-session-card" data-session-id="${sessionId}">
                <div class="session-header" onclick="toggleChatSession('${sessionId}')">
                    <div class="session-info">
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">
                            <span class="expand-icon ${isExpanded ? 'expanded' : ''}">\u25BC</span>
                            ${sessionTitle}
                        </div>
                        <div style="font-size: 0.85rem; color: #94a3b8; margin-bottom: 0.25rem;">
                            Session ID: ${sessionId}
                        </div>
                        <div style="font-size: 0.85rem; color: #94a3b8;">
                            Messages: ${messageCount}<br>
                            Last Activity: ${lastActivity}
                        </div>
                    </div>
                </div>
                <div class="session-messages" style="display: ${isExpanded ? 'block' : 'none'};">
                    ${renderSessionMessages(messages)}
                </div>
            </div>`;
        });
        
        html += '</div>';
        
        // Add memory limitation warning if applicable
        if (data.is_truncated) {
            const warningTemplate = await TemplateLoader.renderTemplateWithData('warning_box.html', {
                warning_class: 'memory-limit-warning',
                title: 'Chat History Limited',
                message: data.truncated_message,
                details: `Memory limits applied: Showing ${data.truncated_info?.sessions_shown || 'recent'} sessions with ${data.truncated_info?.total_messages_shown || 'recent'} messages.`,
                action_button: ''
            });
            html += warningTemplate;
        }
        
        container.innerHTML = html;
        
        // Start polling for updates if not already running
        startChatHistoryPolling(userGuid);
        
    } catch (error) {
        container.innerHTML = `<div class="error">Failed to load chat history: ${error.message}</div>`;
    }
}

// Render individual messages within a session
function renderSessionMessages(messages) {
    if (!messages || messages.length === 0) {
        return '<div class="chat-message">No messages in this session</div>';
    }
    
    let html = '';
    let activeInProgressIds = new Set(); // Track active In-Progress indicators
    
    messages.forEach((message, index) => {
        const role = message.role || 'user';
        const content = message.content || '';
        const timestamp = message.timestamp ? new Date(message.timestamp).toLocaleString() : 'Unknown time';
        const messageId = `msg-${message.session_id}-${index}`;
        
        // Handle In-Progress messages (check role, content, and message_type)
        const messageType = message.message_type || '';
        const isInProgress = (role.includes('in_progress') || role.includes('in progress')) &&
                            !content.includes('[DEBUG') &&
                            !content.includes('[REAL_DEBUG_CAPTURE]') &&
                            !content.includes('Captured') &&
                            !content.includes('INIT') &&
                            !content.includes('RETRIEVE') &&
                            !content.includes('TASK') &&
                            !content.includes('OUTPUT') &&
                            !content.includes('USER') &&
                            !content.includes('ERROR') &&
                            !content.includes('DEBUG') &&
                            !content.includes('DEBUG_TRACE') &&
                            (content.toLowerCase().includes('processing your request') ||
                             content.toLowerCase().includes('working on') ||
                             content.toLowerCase().includes('analyzing') ||
                             messageType.includes('progress'));
        
        if (isInProgress) {
            activeInProgressIds.add(messageId);
            html += `<div class="chat-message in-progress" id="${messageId}" data-role="${role}">
                <div class="message-header">
                    <span class="message-role">${role.toUpperCase()}</span>
                    <span class="message-timestamp">${timestamp}</span>
                </div>
                <div class="chat-content">
                    <div class="progress-indicator">
                        <div class="progress-spinner"></div>
                        <span>${content || 'Processing your request...'}</span>
                    </div>
                </div>
            </div>`;
        }
        // Handle Assistant messages
        else if (role.includes('assistant')) {
            // Check if this assistant message should hide any in-progress indicators
            activeInProgressIds.forEach(inProgressId => {
                // This assistant message replaces the in-progress state
                setTimeout(() => {
                    const inProgressElement = document.getElementById(inProgressId);
                    if (inProgressElement) {
                        inProgressElement.style.display = 'none';
                    }
                }, 100);
            });
            activeInProgressIds.clear(); // Clear in-progress tracking
            
            html += `<div class="chat-message chat-role-assistant" data-role="${role}">
                <div class="message-header">
                    <span class="message-role">ASSISTANT</span>
                    <span class="message-timestamp">${timestamp}</span>
                    ${message.tokens_used ? `<span class="chat-tokens">${message.tokens_used} tokens</span>` : ''}
                </div>
                <div class="chat-content">${escapeHtml(content)}</div>
                ${renderCallTrace(message.call_trace)}
                ${renderDebugMessages(message.debug_messages)}
            </div>`;
        }
        // Handle User messages
        else if (role.includes('user')) {
            html += `<div class="chat-message chat-role-user" data-role="${role}">
                <div class="message-header">
                    <span class="message-role">USER</span>
                    <span class="message-timestamp">${timestamp}</span>
                </div>
                <div class="chat-content">${escapeHtml(content)}</div>
            </div>`;
        }
        // Handle Debug messages (standalone)
        else if (role.includes('debug')) {
            html += `<div class="chat-message chat-role-debug" data-role="${role}">
                <div class="message-header">
                    <span class="message-role">DEBUG</span>
                    <span class="message-timestamp">${timestamp}</span>
                    ${message.tokens_used ? `<span class="chat-tokens">${message.tokens_used} tokens</span>` : ''}
                </div>
                <div class="chat-content debug-content">${escapeHtml(content)}</div>
            </div>`;
        }
        // Handle System/Other messages
        else {
            html += `<div class="chat-message chat-role-system" data-role="${role}">
                <div class="message-header">
                    <span class="message-role">${role.toUpperCase()}</span>
                    <span class="message-timestamp">${timestamp}</span>
                </div>
                <div class="chat-content">${escapeHtml(content)}</div>
            </div>`;
        }
    });
    
    return html;
}

// Render call trace for assistant messages
function renderCallTrace(callTrace) {
    if (!callTrace || callTrace.length === 0) return '';
    
    return `<div class="call-trace-section">
        <button class="expand-button" onclick="toggleCallTrace(this)">
            <span class="expand-icon">▶</span> Call Trace (${callTrace.length} steps)
        </button>
        <div class="call-trace-content">
            <ol class="call-trace-list">
                ${callTrace.map(step => `<li class="call-trace-step">${escapeHtml(step)}</li>`).join('')}
            </ol>
        </div>
    </div>`;
}

// Render debug messages grouped under assistant messages
function renderDebugMessages(debugMessages) {
    if (!debugMessages || debugMessages.length === 0) return '';
    
    return `<div class="debug-messages-section">
        <button class="expand-button" onclick="toggleDebugMessages(this)">
            <span class="expand-icon">▶</span> Debug & Logging Messages (${debugMessages.length})
        </button>
        <div class="debug-messages-content">
            ${debugMessages.map(debug => `
                <div class="debug-message-item">
                    <div class="debug-message-header">
                        <span class="debug-role">${debug.role.toUpperCase()}</span>
                        <span class="debug-timestamp">${debug.timestamp ? new Date(debug.timestamp).toLocaleString() : 'Unknown'}</span>
                        ${debug.tokens_used ? `<span class="debug-tokens">${debug.tokens_used} tokens</span>` : ''}
                    </div>
                    <div class="debug-message-content">${escapeHtml(debug.content)}</div>
                </div>
            `).join('')}
        </div>
    </div>`;
}

// Toggle chat session expansion
function toggleChatSession(sessionId) {
    const sessionCard = document.querySelector(`[data-session-id="${sessionId}"]`);
    if (!sessionCard) return;
    
    const messagesDiv = sessionCard.querySelector('.session-messages');
    const expandIcon = sessionCard.querySelector('.expand-icon');
    
    if (messagesDiv.style.display === 'none') {
        messagesDiv.style.display = 'block';
        expandIcon.classList.add('expanded');
    } else {
        messagesDiv.style.display = 'none';
        expandIcon.classList.remove('expanded');
    }
}

// Toggle call trace expansion with dynamic height management
function toggleCallTrace(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('.expand-icon');
    const chatContainer = document.getElementById('chatContainer');
    const chatHistoryContainer = document.getElementById('chatHistoryContainer');
    const requestsContainer = document.getElementById('requestsContainer');
    
    const isExpanded = content.classList.contains('expanded');
    
    if (!isExpanded) {
        // Expanding
        content.classList.add('expanded');
        icon.textContent = '▼';
        
        // Add expanded class to containers for dynamic height
        if (chatContainer) chatContainer.classList.add('has-expanded-debug');
        if (chatHistoryContainer) chatHistoryContainer.classList.add('has-expanded-debug');
        if (requestsContainer) requestsContainer.classList.add('has-expanded-debug');
        
        // Apply dynamic height adjustments
        setTimeout(() => HeightManager.adjustContainerHeights(), 50);
    } else {
        // Collapsing
        content.classList.remove('expanded');
        icon.textContent = '▶';
        
        // Check if any other debug sections are expanded
        const hasOtherExpanded = document.querySelectorAll('.debug-messages-content.expanded, .call-trace-content.expanded').length > 0;
        if (!hasOtherExpanded) {
            if (chatContainer) chatContainer.classList.remove('has-expanded-debug');
            if (chatHistoryContainer) chatHistoryContainer.classList.remove('has-expanded-debug');
            if (requestsContainer) requestsContainer.classList.remove('has-expanded-debug');
            
            // Reset to default heights
            HeightManager.resetToDefaultHeights();
        } else {
            // Recalculate heights for remaining expanded sections
            setTimeout(() => HeightManager.adjustContainerHeights(), 50);
        }
    }
}

// Toggle debug messages expansion with dynamic height management
function toggleDebugMessages(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('.expand-icon');
    const chatContainer = document.getElementById('chatContainer');
    const chatHistoryContainer = document.getElementById('chatHistoryContainer');
    const requestsContainer = document.getElementById('requestsContainer');
    
    const isExpanded = content.classList.contains('expanded');
    
    if (!isExpanded) {
        // Expanding
        content.classList.add('expanded');
        icon.textContent = '▼';
        
        // Add expanded class to containers for dynamic height
        if (chatContainer) chatContainer.classList.add('has-expanded-debug');
        if (chatHistoryContainer) chatHistoryContainer.classList.add('has-expanded-debug');
        if (requestsContainer) requestsContainer.classList.add('has-expanded-debug');
        
        // Apply dynamic height adjustments
        setTimeout(() => HeightManager.adjustContainerHeights(), 50);
        
        // Scroll to make sure content is visible
        setTimeout(() => {
            content.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }, 100);
    } else {
        // Collapsing
        content.classList.remove('expanded');
        icon.textContent = '▶';
        
        // Check if any other debug sections are expanded
        const hasOtherExpanded = document.querySelectorAll('.debug-messages-content.expanded, .call-trace-content.expanded').length > 0;
        if (!hasOtherExpanded) {
            if (chatContainer) chatContainer.classList.remove('has-expanded-debug');
            if (chatHistoryContainer) chatHistoryContainer.classList.remove('has-expanded-debug');
            if (requestsContainer) requestsContainer.classList.remove('has-expanded-debug');
            
            // Reset to default heights
            HeightManager.resetToDefaultHeights();
        } else {
            // Recalculate heights for remaining expanded sections
            setTimeout(() => HeightManager.adjustContainerHeights(), 50);
        }
    }
}

// Height management utilities for debug sections
const HeightManager = {
    // Detect which tab is currently active
    getActiveTab: function() {
        const activeTabContent = document.querySelector('.tab-content.active');
        if (activeTabContent) {
            return activeTabContent.id;
        }
        return null;
    },
    
    // Get the appropriate container based on active tab
    getActiveContainer: function() {
        const activeTab = this.getActiveTab();
        
        switch (activeTab) {
            case 'user-requests':
                return document.getElementById('requestsContainer');
            case 'chat-history':
                return document.getElementById('chatHistoryContainer');
            default:
                // Fallback to any available container
                return document.getElementById('requestsContainer') || 
                       document.getElementById('chatHistoryContainer') || 
                       document.getElementById('chatContainer');
        }
    },
    
    // Calculate optimal height based on expanded content
    calculateOptimalHeight: function(element) {
        if (!element) return null;
        
        const expandedSections = element.querySelectorAll('.debug-messages-content.expanded, .call-trace-content.expanded');
        let totalContentHeight = 0;
        
        expandedSections.forEach(section => {
            // Get the actual scrollHeight of the content
            const contentHeight = section.scrollHeight;
            totalContentHeight += contentHeight;
        });
        
        return {
            expandedCount: expandedSections.length,
            totalContentHeight: totalContentHeight,
            suggestedMinHeight: Math.min(2400 + totalContentHeight * 2.0, window.innerHeight * 0.99)
        };
    },
    
    // Adjust container heights dynamically
    adjustContainerHeights: function() {
        const chatContainer = document.getElementById('chatContainer');
        const chatHistoryContainer = document.getElementById('chatHistoryContainer');
        const requestsContainer = document.getElementById('requestsContainer');
        
        if (chatContainer || chatHistoryContainer || requestsContainer) {
            const hasExpanded = document.querySelectorAll('.debug-messages-content.expanded, .call-trace-content.expanded').length > 0;
            
            if (hasExpanded) {
                // Use the active container for metrics calculation
                const activeContainer = this.getActiveContainer();
                const metrics = this.calculateOptimalHeight(activeContainer);
                
                if (metrics && metrics.expandedCount > 0) {
                    const viewportHeight = window.innerHeight;
                    const headerHeight = 30; // Minimal header height for nearly full screen
                    const availableHeight = viewportHeight - headerHeight;
                    
                    // Maximum generous height calculation - nearly full screen
                    const optimalHeight = Math.min(metrics.suggestedMinHeight, availableHeight * 0.99);
                    
                    // Apply height adjustments based on active tab
                    const activeTab = this.getActiveTab();
                    
                    if (activeTab === 'user-requests' && requestsContainer) {
                        // Priority to requests container when on requests tab - nearly full screen
                        requestsContainer.style.maxHeight = `${Math.max(optimalHeight, availableHeight * 0.99)}px`;
                        requestsContainer.style.minHeight = '95vh';
                    } else if (activeTab === 'chat-history' && chatHistoryContainer) {
                        // Priority to chat history when on chat tab - nearly full screen
                        chatHistoryContainer.style.maxHeight = `${Math.max(optimalHeight, availableHeight * 0.99)}px`;
                        chatHistoryContainer.style.minHeight = '95vh';
                    } else {
                        // Apply to all containers as fallback - maximum heights
                        if (chatContainer) {
                            chatContainer.style.maxHeight = `${Math.max(optimalHeight, 2000)}px`;
                            chatContainer.style.minHeight = '2000px';
                        }
                        
                        if (chatHistoryContainer) {
                            chatHistoryContainer.style.maxHeight = `${Math.max(optimalHeight, availableHeight * 0.99)}px`;
                            chatHistoryContainer.style.minHeight = '95vh';
                        }
                        
                        if (requestsContainer) {
                            requestsContainer.style.maxHeight = `${Math.max(optimalHeight, availableHeight * 0.99)}px`;
                            requestsContainer.style.minHeight = '95vh';
                        }
                    }
                }
            }
        }
    },
    
    // Reset heights to default
    resetToDefaultHeights: function() {
        const chatContainer = document.getElementById('chatContainer');
        const chatHistoryContainer = document.getElementById('chatHistoryContainer');
        const requestsContainer = document.getElementById('requestsContainer');
        
        if (chatContainer) {
            chatContainer.style.maxHeight = '';
            chatContainer.style.minHeight = '';
        }
        
        if (chatHistoryContainer) {
            chatHistoryContainer.style.maxHeight = '';
            chatHistoryContainer.style.minHeight = '';
        }
        
        if (requestsContainer) {
            requestsContainer.style.maxHeight = '';
            requestsContainer.style.minHeight = '';
        }
    }
};

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Chat history polling variables
let chatPollingInterval = null;
let currentPollingUserGuid = null;

// Start polling for chat history updates
function startChatHistoryPolling(userGuid) {
    // Stop any existing polling
    stopChatHistoryPolling();
    
    currentPollingUserGuid = userGuid;
    
    // Poll every 5 seconds for updates
    chatPollingInterval = setInterval(() => {
        refreshChatHistoryQuietly(userGuid);
    }, 5000);
}

// Stop chat history polling
function stopChatHistoryPolling() {
    if (chatPollingInterval) {
        clearInterval(chatPollingInterval);
        chatPollingInterval = null;
        currentPollingUserGuid = null;
    }
}

// Quietly refresh chat history without disrupting UI
async function refreshChatHistoryQuietly(userGuid) {
    try {
        const response = await fetch(`/dashboard/api/user-chat-history?user_guid=${encodeURIComponent(userGuid)}`);
        const newData = await response.json();
        
        if (newData.error) return; // Ignore errors during polling
        
        // Check if there are new messages by comparing message counts
        const container = document.getElementById('chatHistoryContainer');
        if (!container) return;
        
        const currentSessions = container.querySelectorAll('.chat-session-card');
        const newSessions = newData.sessions_list || [];
        
        // Simple check: if session count changed or message counts changed, refresh
        let needsRefresh = currentSessions.length !== newSessions.length;
        
        if (!needsRefresh) {
            // Check message counts in each session
            newSessions.forEach(sessionId => {
                const sessionCard = container.querySelector(`[data-session-id="${sessionId}"]`);
                if (sessionCard) {
                    const newMessages = newData.chat_sessions[sessionId] || [];
                    const currentMessages = sessionCard.querySelectorAll('.chat-message').length;
                    if (newMessages.length !== currentMessages) {
                        needsRefresh = true;
                    }
                } else {
                    needsRefresh = true; // New session appeared
                }
            });
        }
        
        if (needsRefresh) {
            // Remember which sessions were expanded
            const expandedSessions = new Set();
            currentSessions.forEach(sessionCard => {
                const sessionId = sessionCard.getAttribute('data-session-id');
                const messagesDiv = sessionCard.querySelector('.session-messages');
                if (messagesDiv && messagesDiv.style.display !== 'none') {
                    expandedSessions.add(sessionId);
                }
            });
            
            // Reload the chat history
            await loadUserChatHistory(userGuid);
            
            // Restore expanded state
            expandedSessions.forEach(sessionId => {
                const sessionCard = container.querySelector(`[data-session-id="${sessionId}"]`);
                if (sessionCard) {
                    const messagesDiv = sessionCard.querySelector('.session-messages');
                    const expandIcon = sessionCard.querySelector('.expand-icon');
                    if (messagesDiv && expandIcon) {
                        messagesDiv.style.display = 'block';
                        expandIcon.classList.add('expanded');
                    }
                }
            });
        }
    } catch (error) {
        // Silently ignore polling errors
        console.debug('Chat history polling error:', error);
    }
}

// Auto-refresh functionality
let autoRefreshInterval = null;
let autoRefreshEnabled = false;

function toggleAutoRefresh() {
    const toggleBtn = document.getElementById('refreshToggle');
    const timerElement = document.getElementById('refreshTimer');
    
    if (!toggleBtn || !timerElement) return;
    
    autoRefreshEnabled = !autoRefreshEnabled;
    
    if (autoRefreshEnabled) {
        toggleBtn.textContent = 'Disable';
        startAutoRefresh();
    } else {
        toggleBtn.textContent = 'Enable';
        stopAutoRefresh();
        timerElement.textContent = 'Auto-refresh: Disabled';
    }
}

function startAutoRefresh() {
    let countdown = 30;
    const timerElement = document.getElementById('refreshTimer');
    
    // Update timer display
    function updateTimer() {
        if (timerElement) {
            timerElement.textContent = `Auto-refresh: ${countdown}s`;
        }
        countdown--;
        
        if (countdown < 0) {
            // Refresh the page
            window.location.reload();
        }
    }
    
    // Update immediately and then every second
    updateTimer();
    autoRefreshInterval = setInterval(updateTimer, 1000);
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// System overview functionality
async function loadSystemOverview() {
    const container = document.getElementById('requestsContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading system overview...</div>';
    
    try {
        const response = await fetch('/dashboard/api/dashboard-overview');
        const data = await response.json();
        
        // Handle rate limiting
        if (response.status === 429 || data.error_type === 'rate_limit') {
            const retryAfter = data.retry_after || 30;
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: 'rate-limit-error',
                title: 'System Overview - Rate Limited',
                message: data.error || 'Too many requests. Please wait before refreshing.',
                details: 'Showing cached data if available.',
                retry_button: `<button onclick="setTimeout(loadSystemOverview, ${retryAfter * 1000})" style="margin: 10px 0; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Auto-retry in ${retryAfter}s
                </button>`
            });
            
            let html = errorTemplate;
            
            // Show fallback data if available
            if (data.fallback_data) {
                html += '<div class="metrics-grid" style="opacity: 0.6;">';
                const fallback = data.fallback_data;
                
                if (fallback.system_health) {
                    const metricCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                        card_style: '',
                        title: 'System Status',
                        value: fallback.system_health.overall_status || 'Rate Limited',
                        value_style: 'color: #f59e0b;',
                        subtitle: 'Cached status'
                    });
                    html += metricCard;
                }
                
                if (fallback.factory_metrics) {
                    const metricCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                        card_style: '',
                        title: 'Active Managers',
                        value: fallback.factory_metrics.active_managers || 0,
                        value_style: '',
                        subtitle: 'Last known count'
                    });
                    html += metricCard;
                }
                
                html += '</div>';
            }
            
            container.innerHTML = html;
            return;
        }
        
        if (data.error) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: '',
                title: 'Error',
                message: data.error,
                details: '',
                retry_button: ''
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        let html = '<h3>System Overview</h3>';
        
        // Track what data is missing for partial loading warning
        const missingData = [];
        let hasAnyData = false;
        
        html += '<div class="metrics-grid">';
        
        // System health metrics
        if (data.system_health) {
            hasAnyData = true;
            
            const dbCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Database Status',
                value: data.system_health.database_status || 'Unknown',
                value_style: '',
                subtitle: 'PostgreSQL connection'
            });
            html += dbCard;
            
            const vectorCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Vector DB Status',
                value: data.system_health.vector_database_status || 'Unknown',
                value_style: '',
                subtitle: 'Qdrant connection'
            });
            html += vectorCard;
            
            const overallCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Overall Status',
                value: data.system_health.overall_status || 'Unknown',
                value_style: '',
                subtitle: 'System health'
            });
            html += overallCard;
        } else {
            missingData.push('System Health');
            // Show placeholder for missing system health data
            const placeholderCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: 'opacity: 0.5; border: 2px dashed #f59e0b;',
                title: 'Database Status',
                value: 'Unavailable',
                value_style: 'color: #f59e0b;',
                subtitle: 'Data loading failed'
            });
            html += placeholderCard;
        }
        
        // Performance metrics
        if (data.performance_metrics) {
            hasAnyData = true;
            
            const tasksCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Active Tasks',
                value: data.performance_metrics.active_tasks || 0,
                value_style: '',
                subtitle: 'Currently running'
            });
            html += tasksCard;
            
            const requestsCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Total Requests',
                value: data.performance_metrics.total_requests || 0,
                value_style: '',
                subtitle: 'All-time requests'
            });
            html += requestsCard;
            
            const loadCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'System Load',
                value: data.performance_metrics.system_load || 'Normal',
                value_style: '',
                subtitle: 'Performance status'
            });
            html += loadCard;
        } else {
            missingData.push('Performance Metrics');
            // Show placeholder for missing performance data
            const placeholderCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: 'opacity: 0.5; border: 2px dashed #f59e0b;',
                title: 'Active Tasks',
                value: '?',
                value_style: 'color: #f59e0b;',
                subtitle: 'Data loading failed'
            });
            html += placeholderCard;
        }
        
        // Factory metrics (if available)
        if (data.factory_metrics) {
            hasAnyData = true;
            const managersCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Active Managers',
                value: data.factory_metrics.active_managers || 0,
                value_style: '',
                subtitle: 'User-specific managers'
            });
            html += managersCard;
        } else {
            missingData.push('Factory Metrics');
        }
        
        html += '</div>';
        
        // Show partial data warning if some data is missing
        if (missingData.length > 0 && hasAnyData) {
            const warningTemplate = await TemplateLoader.renderTemplateWithData('warning_box.html', {
                warning_class: 'partial-data-warning',
                title: 'Partial Data Loaded',
                message: `Some data sections could not be loaded: ${missingData.join(', ')}`,
                details: 'This may be due to temporary service issues. Data will refresh automatically.',
                action_button: `<button onclick="loadSystemOverview()" style="margin-top: 0.5rem; padding: 4px 8px; background: #f59e0b; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 0.8rem;">
                    Retry Loading
                </button>`
            });
            html += warningTemplate;
        }
        
        container.innerHTML = html;
    } catch (error) {
        // Handle network errors that might indicate rate limiting
        if (error.message.includes('429') || error.message.toLowerCase().includes('rate limit')) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: 'rate-limit-error',
                title: 'System Overview - Network Error',
                message: 'Network request was rate limited. Please wait before trying again.',
                details: '',
                retry_button: `<button onclick="setTimeout(loadSystemOverview, 30000)" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Retry in 30s
                </button>`
            });
            container.innerHTML = errorTemplate;
        } else {
            container.innerHTML = `<div class="error">Failed to load system overview: ${error.message}</div>`;
        }
    }
}

// Manual load functions for individual tabs
function loadUserRequestsManual() {
    const userGuidInput = document.getElementById('userGuidInput');
    if (!userGuidInput) return;
    
    const userGuid = userGuidInput.value.trim();
    if (!userGuid) {
        alert('Please enter a user GUID');
        return;
    }
    
    loadUserRequests(userGuid);
}

function loadChatHistory() {
    const chatUserGuidInput = document.getElementById('chatUserGuidInput');
    if (!chatUserGuidInput) return;
    
    const userGuid = chatUserGuidInput.value.trim();
    if (!userGuid) {
        alert('Please enter a user GUID');
        return;
    }
    
    loadUserChatHistory(userGuid);
}

// Navigate to Chat History tab for specific chat session
function navigateToChatSession(chatSessionGuid, userGuid) {
    // Handle completely missing data
    if (!userGuid || userGuid === 'Unknown') {
        showNotification('User information not available for this request. Cannot navigate to chat history.', 'error');
        return;
    }
    
    // Switch to chat-history tab
    showTab('chat-history');
    
    // Update the active tab button
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    const chatHistoryButton = document.querySelector('.tab-button[onclick="showTab(\'chat-history\')"]');
    if (chatHistoryButton) {
        chatHistoryButton.classList.add('active');
    }
    
    // Pre-fill the user GUID input
    const chatUserGuidInput = document.getElementById('chatUserGuidInput');
    if (chatUserGuidInput) {
        chatUserGuidInput.value = userGuid;
    }
    
    // Load the chat history automatically
    loadUserChatHistory(userGuid);
    
    // Handle specific session highlighting if session GUID is available
    if (chatSessionGuid && chatSessionGuid !== 'Unknown') {
        // Store the target session GUID for highlighting after load
        window.targetChatSessionGuid = chatSessionGuid;
        
        // Add a small delay and try to highlight the specific session
        setTimeout(() => {
            highlightChatSession(chatSessionGuid);
        }, 1000);
        
        // Show success message with specific session
        setTimeout(() => {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                background: #10b981; color: white; padding: 0.75rem 1rem;
                border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-size: 0.9rem; max-width: 300px;
            `;
            notification.textContent = `Navigated to chat history. Looking for session ${chatSessionGuid.substring(0, 8)}...`;
            document.body.appendChild(notification);
            
            // Remove notification after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }, 500);
    } else {
        // Show info message for general chat history
        setTimeout(() => {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                background: #f59e0b; color: white; padding: 0.75rem 1rem;
                border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-size: 0.9rem; max-width: 300px;
            `;
            notification.textContent = `Navigated to chat history for user. Specific session not available.`;
            document.body.appendChild(notification);
            
            // Remove notification after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }, 500);
    }
}

// Highlight specific chat session after loading
function highlightChatSession(chatSessionGuid) {
    // Look for the specific chat session in the loaded content
    const chatContainer = document.getElementById('chatContainer');
    if (!chatContainer) return;
    
    // Find all chat session elements (looking for session ID in the content)
    const sessionElements = chatContainer.querySelectorAll('[data-session-id], .chat-session');
    sessionElements.forEach(element => {
        const sessionId = element.getAttribute('data-session-id') || 
                         element.textContent.includes(chatSessionGuid);
        
        if (sessionId === chatSessionGuid || element.textContent.includes(chatSessionGuid)) {
            // Highlight the matching session
            element.style.border = '2px solid #f59e0b';
            element.style.backgroundColor = '#fef3c7';
            
            // Scroll to the element
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // Remove highlight after a few seconds
            setTimeout(() => {
                element.style.border = '';
                element.style.backgroundColor = '';
            }, 5000);
        }
    });
}

// Placeholder functions for call trace and debug messages (if not already implemented)
function showCallTrace(scheduledGuid) {
    alert(`Call Trace for request ${scheduledGuid} - Feature to be implemented`);
}

function showDebugMessages(scheduledGuid) {
    alert(`Debug Messages for request ${scheduledGuid} - Feature to be implemented`);
}

// Initialize user permissions and menu visibility
async function initializeUserPermissions() {
    try {
        // Get current user session info
        const userInfo = window.currentUser || null;
        
        // Debug logging
        console.log('[DEBUG] initializeUserPermissions called');
        console.log('[DEBUG] window.currentUser:', userInfo);
        
        // For SYSTEM users, add the class to enable system-only menu items
        if (userInfo && userInfo.is_system_user === true) {
            document.body.classList.add('user-is-system');
            console.log('[OK] User has SYSTEM permissions - showing all menu items');
            console.log('[DEBUG] Added user-is-system class to body');
            
            // Also log the specific properties for debugging
            console.log('[DEBUG] User properties:', {
                username: userInfo.username,
                is_system_user: userInfo.is_system_user,
                rank: userInfo.rank,
                user_guid: userInfo.user_guid
            });
            
        } else {
            document.body.classList.remove('user-is-system');
            console.log('[INFO] User has standard permissions - hiding SYSTEM-only menu items');
            if (userInfo) {
                console.log('[DEBUG] Non-SYSTEM user properties:', {
                    username: userInfo.username,
                    is_system_user: userInfo.is_system_user,
                    rank: userInfo.rank
                });
            }
        }
        
        // Wait a moment then double-check the body class
        setTimeout(() => {
            const hasSystemClass = document.body.classList.contains('user-is-system');
            console.log('[DEBUG] Body has user-is-system class:', hasSystemClass);
        }, 100);
        
    } catch (error) {
        console.error('Failed to initialize user permissions:', error);
        // Default to non-system user if error occurs
        document.body.classList.remove('user-is-system');
    }
}

// Initialize dashboard on page load
function initializeDashboard() {
    // Initialize browser compatibility checks first
    BrowserCompat.init();
    
    // Clear template cache on initialization to ensure fresh templates
    TemplateLoader.clearCache();
    
    // Initialize user permissions and menu visibility
    initializeUserPermissions();
    
    // Set up any initial state
    console.log('Dashboard initialized');
    
    // Restore previous state if available
    restoreDashboardState();
    
    // Add event listeners for manual input buttons if they exist
    const loadRequestsBtn = document.querySelector('button[onclick="loadUserRequests()"]');
    if (loadRequestsBtn) {
        loadRequestsBtn.onclick = loadUserRequestsManual;
    }
    
    const loadChatBtn = document.querySelector('button[onclick="loadChatHistory()"]');
    if (loadChatBtn) {
        loadChatBtn.onclick = loadChatHistory;
    }
    
    // Add window resize handler for dynamic height adjustments
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            const hasExpanded = document.querySelectorAll('.debug-messages-content.expanded, .call-trace-content.expanded').length > 0;
            if (hasExpanded) {
                HeightManager.adjustContainerHeights();
            }
        }, 250);
    });
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeDashboard);
} else {
    initializeDashboard();
}

// Expose functions globally
window.showTab = showTab;
window.filterUsers = filterUsers;
window.loadUserList = loadUserList;
window.selectUser = selectUser;
window.loadUserRequests = loadUserRequestsManual;
window.loadUserChatHistory = loadUserChatHistory;
window.loadChatHistory = loadChatHistory;
window.loadSystemOverview = loadSystemOverview;
window.toggleAutoRefresh = toggleAutoRefresh;
window.navigateToChatSession = navigateToChatSession;
window.highlightChatSession = highlightChatSession;
window.showCallTrace = showCallTrace;
window.showDebugMessages = showDebugMessages;
window.TemplateLoader = TemplateLoader;
window.clearTemplateCache = () => TemplateLoader.clearCache();

// Live Log Viewer functionality
class LiveLogViewer {
    constructor() {
        this.isInitialized = false;
        this.isPaused = false;
        this.lastPosition = 0;
        this.updateInterval = 1000; // Default 1 second
        this.intervalId = null;
        this.filteredContent = '';
        this.allContent = '';
        this.lineCount = 0;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.logSource = 'debug'; // Default to debug trace logs
        this.rawMode = false; // Default to normal filtering mode
    }

    async initialize() {
        if (this.isInitialized) return;
        
        try {
            // Check if log viewer elements exist
            const logContent = document.getElementById('logContent');
            const statusIcon = document.getElementById('logStatusIcon');
            const statusText = document.getElementById('logStatusText');
            
            if (!logContent || !statusIcon || !statusText) {
                console.log('Log viewer elements not found, skipping initialization');
                return;
            }
            
            this.updateStatus('connecting', 'Connecting...');
            
            // Get initial log status and debug mode
            const statusResponse = await safeFetch('/dashboard/api/log-status');
            const statusData = await statusResponse.json();
            
            // Check for debug mode and show test_real button if applicable
            await this.checkDebugModeAndShowButton();
            
            if (statusData.exists) {
                // Load initial log content
                await this.loadInitialContent();
                this.startPolling();
                this.updateStatus('connected', 'Connected');
                this.isInitialized = true;
                
                // Initialize filter status
                setTimeout(() => {
                    const excludeFrontendDebug = document.getElementById('excludeFrontendDebug')?.checked || false;
                    const excludeScrubbedAuth = document.getElementById('excludeScrubbedAuth')?.checked || false;
                    const eventCodesFilter = document.getElementById('eventCodesFilter')?.value || '';
                    updateFilterStatus(excludeFrontendDebug, excludeScrubbedAuth, eventCodesFilter);
                }, 100);
                
                console.log('Live log viewer initialized successfully');
            } else {
                this.updateStatus('error', 'Log file not found');
                logContent.innerHTML = '<div style="color: #fbbf24; text-align: center; margin-top: 2rem;">⚠️ Log file not available</div>';
            }
            
        } catch (error) {
            console.error('Failed to initialize live log viewer:', error);
            this.updateStatus('error', 'Connection failed');
            const logContent = document.getElementById('logContent');
            if (logContent) {
                logContent.innerHTML = '<div style="color: #ef4444; text-align: center; margin-top: 2rem;">❌ Failed to connect to log stream</div>';
            }
        }
    }
    
    async checkDebugModeAndShowButton() {
        try {
            // Check if we're in debug mode by testing the server check endpoint
            const testResponse = await safeFetch('/dashboard/api/check-test-server?port=40999');
            const testData = await testResponse.json();
            
            const testRealBtn = document.getElementById('testRealServerBtn');
            if (testRealBtn && testData.debug_mode === true) {
                // Show the button if debug mode is enabled
                testRealBtn.style.display = 'inline-block';
                console.log('Debug mode detected, test_real button enabled');
            }
        } catch (error) {
            console.log('Debug mode check failed or not in debug mode:', error);
            // Button remains hidden
        }
    }
    
    async loadInitialContent() {
        try {
            // Load last 100 lines initially with filters
            const apiEndpoint = this.getApiEndpoint();
            const params = this.buildApiParams(0, 100);
            console.log(`[DEBUG] loadInitialContent: Making request to ${apiEndpoint} with params:`, params);
            const response = await safeFetch(`${apiEndpoint}?${params}`);
            
            console.log('[DEBUG] loadInitialContent: Response status:', response.status);
            console.log('[DEBUG] loadInitialContent: Response headers:', [...response.headers.entries()]);
            
            if (!response.ok) {
                // Handle authentication errors specifically
                if (response.status === 401) {
                    console.error('[ERROR] loadInitialContent: Authentication required (401)');
                    const errorText = await response.text();
                    console.error('[ERROR] loadInitialContent: Error response:', errorText);
                    throw new Error(`Authentication required: ${errorText}`);
                } else if (response.status === 403) {
                    console.error('[ERROR] loadInitialContent: Insufficient permissions (403)');
                    const errorText = await response.text();
                    console.error('[ERROR] loadInitialContent: Error response:', errorText);
                    throw new Error(`Access denied - SYSTEM user required: ${errorText}`);
                } else {
                    console.error('[ERROR] loadInitialContent: HTTP error:', response.status);
                    const errorText = await response.text();
                    console.error('[ERROR] loadInitialContent: Error response:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            }
            
            const data = await response.json();
            console.log('[DEBUG] loadInitialContent: Response data:', data);
            
            if (data.content) {
                // Filter garbage detection messages from initial content
                this.allContent = this.filterGarbageDetectionMessages(data.content);
                this.lastPosition = data.new_position;
                
                const contentLength = this.allContent.length;
                const lineCount = this.allContent.split('\n').length;
                
                // Show loading feedback for large files
                if (contentLength > 50000) {
                    const logContent = document.getElementById('logContent');
                    if (logContent) {
                        logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">📄 Processing full debug_trace.log file (' + Math.round(contentLength/1024) + ' KB, ' + lineCount + ' lines)...</div>';
                    }
                    
                    // Use setTimeout to allow UI to update before processing
                    setTimeout(() => {
                        this.updateDisplay();
                        this.updateStats(data);
                    }, 100);
                } else {
                    this.updateDisplay();
                    this.updateStats(data);
                }
                
                console.log('[DEBUG] loadInitialContent: Successfully loaded', contentLength, 'characters (' + lineCount + ' lines)');
                
                // Debug: Show first 200 characters of loaded content
                const preview = this.allContent.substring(0, 200).replace(/\n/g, '\\n');
                console.log('[DEBUG] Content preview (first 200 chars):', preview);
            } else {
                console.log('[DEBUG] loadInitialContent: No content in response');
            }
            
        } catch (error) {
            console.error('[ERROR] loadInitialContent: Failed to load initial log content:', error);
            
            // Update UI to show the specific error
            const logContent = document.getElementById('logContent');
            if (logContent) {
                if (error.message.includes('Authentication required')) {
                    logContent.innerHTML = '<div style="color: #f59e0b; text-align: center; margin-top: 2rem;">⚠️ Authentication required. Please log in as SYSTEM user.</div>';
                } else if (error.message.includes('Access denied')) {
                    logContent.innerHTML = '<div style="color: #f59e0b; text-align: center; margin-top: 2rem;">⚠️ Access denied. SYSTEM user permissions required.</div>';
                } else {
                    logContent.innerHTML = `<div style="color: #ef4444; text-align: center; margin-top: 2rem;">❌ Connection failed: ${error.message}</div>`;
                }
            }
            
            throw error;
        }
    }
    
    startPolling() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        
        this.intervalId = setInterval(() => {
            if (!this.isPaused) {
                this.pollForUpdates();
            }
        }, this.updateInterval);
    }
    
    async pollForUpdates() {
        try {
            const apiEndpoint = this.getApiEndpoint();
            const params = this.buildApiParams(this.lastPosition, 50);
            console.log(`[DEBUG] pollForUpdates: Polling ${apiEndpoint}, position: ${this.lastPosition}`);
            const response = await safeFetch(`${apiEndpoint}?${params}`);
            
            if (!response.ok) {
                console.error('[ERROR] pollForUpdates: HTTP error:', response.status);
                
                if (response.status === 401 || response.status === 403) {
                    // Authentication/permission error - stop polling and show error
                    const errorText = await response.text();
                    console.error('[ERROR] pollForUpdates: Auth error:', errorText);
                    this.updateStatus('error', 'Authentication failed');
                    this.stopPolling();
                    
                    const logContent = document.getElementById('logContent');
                    if (logContent) {
                        logContent.innerHTML = `<div style="color: #f59e0b; text-align: center; margin-top: 2rem;">⚠️ Session expired or insufficient permissions. Please refresh and log in again.</div>`;
                    }
                    return;
                }
                
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            console.log('[DEBUG] pollForUpdates: Response data length:', data.content ? data.content.length : 0);
            
            if (data.content && data.content.trim()) {
                // Filter out external garbage detection messages before processing
                const filteredContent = this.filterGarbageDetectionMessages(data.content);
                
                if (filteredContent && filteredContent.trim()) {
                    // New content available after filtering
                    this.allContent += filteredContent;
                    this.lastPosition = data.new_position;
                    this.updateDisplay();
                    this.updateStats(data);
                    this.retryCount = 0; // Reset retry count on success
                    console.log('[DEBUG] pollForUpdates: Added filtered content, total length:', this.allContent.length);
                } else {
                    console.log('[DEBUG] pollForUpdates: All content was filtered out (garbage detection messages)');
                    // Update position but don't add content
                    this.lastPosition = data.new_position;
                }
            }
            
            // Update connection status
            this.updateStatus('connected', 'Connected');
            
        } catch (error) {
            console.error('[ERROR] pollForUpdates: Failed to poll log updates:', error);
            this.retryCount++;
            
            if (this.retryCount >= this.maxRetries) {
                console.error('[ERROR] pollForUpdates: Max retries reached, stopping polling');
                this.updateStatus('error', 'Connection lost');
                this.stopPolling();
            } else {
                console.warn('[WARN] pollForUpdates: Retrying...', this.retryCount, '/', this.maxRetries);
                this.updateStatus('warning', `Retrying... (${this.retryCount}/${this.maxRetries})`);
            }
        }
    }
    
    /**
     * Filter out external garbage detection messages that interfere with log display
     * These are generated by external monitoring systems and not part of the application logs
     */
    filterGarbageDetectionMessages(content) {
        if (!content) return content;
        
        const garbagePatterns = [
            /\[WARNING\] Garbage content detected.*$/gm,
            /\[DEBUG\] Garbage patterns found:.*$/gm,
            /\[DEBUG\] First \d+ chars:.*$/gm,
            /\[DEBUG\] Reading from:.*garbage_detected.*$/gm,
            /\[DEBUG\] Before seek.*$/gm,
            /\[DEBUG\] After seek.*$/gm,
            /\[DEBUG\] After read.*$/gm,
            /\[DEBUG\] Initial load from.*$/gm,
            /\[ERROR\] File content doesn't start as expected!.*$/gm,
            /\[DEBUG\] First \d+ raw bytes:.*$/gm,
            /\[DEBUG\] Re-reading first \d+ chars from absolute position.*$/gm,
            /\[DEBUG\] Re-read complete file:.*$/gm,
            /\[DEBUG\] Filter options:.*$/gm,
            /\[DEBUG\] No filters active.*$/gm
        ];
        
        let filtered = content;
        let originalLength = content.length;
        
        // Apply all garbage detection filters
        for (const pattern of garbagePatterns) {
            filtered = filtered.replace(pattern, '');
        }
        
        // Clean up multiple consecutive newlines left by filtering
        filtered = filtered.replace(/\n{3,}/g, '\n\n');
        
        if (originalLength !== filtered.length) {
            console.log(`[DEBUG] filterGarbageDetectionMessages: Filtered ${originalLength - filtered.length} characters of garbage content`);
        }
        
        return filtered;
    }
    
    updateDisplay() {
        const logContent = document.getElementById('logContent');
        const searchInput = document.getElementById('logSearchInput');
        if (!logContent) return;
        
        // Apply search filter if active
        const searchTerm = searchInput?.value.toLowerCase() || '';
        if (searchTerm) {
            const lines = this.allContent.split('\n');
            const filteredLines = lines.filter(line => 
                line.toLowerCase().includes(searchTerm)
            );
            this.filteredContent = filteredLines.join('\n');
        } else {
            this.filteredContent = this.allContent;
        }
        
        // Limit display to last 1000 lines for performance
        const lines = this.filteredContent.split('\n');
        if (lines.length > 1000) {
            this.filteredContent = lines.slice(-1000).join('\n');
        }
        
        // Apply syntax highlighting and update display
        const content = this.filteredContent.trim();
        if (content) {
            const highlightedContent = this.applyLogSyntaxHighlighting(content);
            logContent.innerHTML = `<pre style="margin: 0; white-space: pre-wrap; word-break: break-word;">${highlightedContent}</pre>`;
        } else {
            logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">No matching log entries</div>';
        }
        
        // Auto-scroll if enabled
        const autoScroll = document.getElementById('logAutoScroll');
        if (autoScroll && autoScroll.checked) {
            setTimeout(() => {
                logContent.scrollTop = logContent.scrollHeight;
            }, 50);
        }
        
        // Update line count
        this.lineCount = lines.length;
    }
    
    applyLogSyntaxHighlighting(content) {
        // Apply syntax highlighting to log content
        const lines = content.split('\n');
        const highlightedLines = lines.map(line => {
            if (!line.trim()) return line;
            
            let highlightedLine = escapeHtml(line);
            
            // Highlight timestamps
            highlightedLine = highlightedLine.replace(
                /(\[?\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}[^\]]*\]?)/g,
                '<span style="color: #64748b; font-weight: normal;">$1</span>'
            );
            
            // Highlight event codes with different colors
            const eventColors = {
                'INIT': '#10b981',      // Green
                'ERROR': '#ef4444',     // Red
                'WARNING': '#f59e0b',   // Orange
                'DEBUG': '#8b5cf6',     // Purple
                'TASK': '#3b82f6',      // Blue
                'USER': '#06b6d4',      // Cyan
                'OUTPUT': '#84cc16',    // Lime
                'REQUEST': '#f97316',   // Orange
                'METRICS': '#6366f1',   // Indigo
                'EVENT': '#ec4899',     // Pink
                'IMAP': '#14b8a6',      // Teal
                'INFO': '#22c55e'       // Green
            };
            
            Object.entries(eventColors).forEach(([event, color]) => {
                const regex = new RegExp(`(\\[${event}[^\\]]*\\])`, 'g');
                highlightedLine = highlightedLine.replace(regex, 
                    `<span style="color: ${color}; font-weight: bold;">$1</span>`
                );
            });
            
            // Highlight user GUIDs
            highlightedLine = highlightedLine.replace(
                /([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/gi,
                '<span style="color: #a78bfa; font-weight: 500;">$1</span>'
            );
            
            // Highlight IP addresses
            highlightedLine = highlightedLine.replace(
                /(\b(?:\d{1,3}\.){3}\d{1,3}\b)/g,
                '<span style="color: #fbbf24;">$1</span>'
            );
            
            // Highlight URLs and endpoints
            highlightedLine = highlightedLine.replace(
                /(https?:\/\/[^\s]+|\/[^\s]*[^\s.,;])/g,
                '<span style="color: #60a5fa; text-decoration: underline;">$1</span>'
            );
            
            // Highlight file paths (Windows and Unix)
            highlightedLine = highlightedLine.replace(
                /([A-Z]:\\[^:\s]+(?:\.[a-z]+)?|\/[^\s:]+(?:\.[a-z]+)?)/g,
                '<span style="color: #34d399;">$1</span>'
            );
            
            return highlightedLine;
        });
        
        return highlightedLines.join('\n');
    }
    
    updateStats(data) {
        const lineCountElement = document.getElementById('logLineCount');
        const sizeInfoElement = document.getElementById('logSizeInfo');
        const lastUpdateElement = document.getElementById('logLastUpdate');
        const logSourceInfo = document.getElementById('logSourceInfo');
        const logFilteredCount = document.getElementById('logFilteredCount');
        
        if (lineCountElement) {
            lineCountElement.textContent = this.lineCount.toLocaleString();
        }
        
        if (sizeInfoElement && data.file_size) {
            const sizeKB = Math.round(data.file_size / 1024);
            sizeInfoElement.textContent = `${sizeKB} KB`;
        }
        
        if (lastUpdateElement) {
            const now = new Date();
            lastUpdateElement.textContent = now.toLocaleTimeString();
        }
        
        // Update source information
        if (logSourceInfo) {
            const sourceNames = {
                'debug': 'Debug Trace',
                'console': 'Console Output', 
                'system': 'System Events'
            };
            logSourceInfo.textContent = sourceNames[this.logSource] || 'Debug Trace';
        }
        
        // Update filtered count
        if (logFilteredCount) {
            const totalLines = this.allContent.split('\n').length;
            const filteredLines = this.filteredContent.split('\n').length;
            logFilteredCount.textContent = filteredLines.toLocaleString();
            
            // Show percentage if filtered
            if (totalLines > 0 && filteredLines !== totalLines) {
                const percentage = Math.round((filteredLines / totalLines) * 100);
                logFilteredCount.textContent = `${filteredLines.toLocaleString()} (${percentage}%)`;
                logFilteredCount.style.color = '#f59e0b'; // Orange for filtered
            } else {
                logFilteredCount.style.color = '#94a3b8'; // Normal gray
            }
        }
    }
    
    updateStatus(type, message) {
        const statusIcon = document.getElementById('logStatusIcon');
        const statusText = document.getElementById('logStatusText');
        
        if (!statusIcon || !statusText) return;
        
        statusText.textContent = message;
        
        switch (type) {
            case 'connected':
                statusIcon.style.background = '#10b981';
                break;
            case 'connecting':
                statusIcon.style.background = '#f59e0b';
                break;
            case 'warning':
                statusIcon.style.background = '#f59e0b';
                break;
            case 'error':
                statusIcon.style.background = '#ef4444';
                break;
            default:
                statusIcon.style.background = '#6b7280';
        }
    }
    
    toggle() {
        this.isPaused = !this.isPaused;
        const pauseBtn = document.getElementById('logPauseBtn');
        
        if (this.isPaused) {
            if (pauseBtn) pauseBtn.innerHTML = '▶️ Resume';
            this.updateStatus('warning', 'Paused');
        } else {
            if (pauseBtn) pauseBtn.innerHTML = '⏸️ Pause';
            this.updateStatus('connected', 'Connected');
        }
    }
    
    clear() {
        this.allContent = '';
        this.filteredContent = '';
        this.lineCount = 0;
        const logContent = document.getElementById('logContent');
        if (logContent) {
            logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">Log display cleared</div>';
        }
        this.updateStats({ file_size: 0 });
    }
    
    copy() {
        if (!this.filteredContent.trim()) {
            alert('No log content to copy');
            return;
        }
        
        navigator.clipboard.writeText(this.filteredContent).then(() => {
            // Visual feedback
            const copyBtn = document.querySelector('button[onclick="copyLogContent()"]');
            if (copyBtn) {
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '✅ Copied';
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                }, 2000);
            }
        }).catch(err => {
            console.error('Failed to copy log content:', err);
            alert('Failed to copy log content to clipboard');
        });
    }
    
    updateInterval(newInterval) {
        this.updateInterval = parseInt(newInterval);
        if (this.isInitialized && !this.isPaused) {
            this.startPolling(); // Restart with new interval
        }
    }
    
    filter(searchTerm) {
        // Re-apply display with current search term
        this.updateDisplay();
    }
    
    getApiEndpoint() {
        // Return the appropriate API endpoint based on log source
        switch (this.logSource) {
            case 'console':
                return '/dashboard/api/console-output';
            case 'system':
                return '/dashboard/api/live-logs'; // Use same as debug for now with system filter
            case 'debug':
            default:
                return '/dashboard/api/live-logs';
        }
    }
    
    buildApiParams(lastPosition, maxLines) {
        // Build URL parameters based on log source and filters
        let params = '';
        
        if (this.logSource === 'console') {
            // Console output API uses different parameters
            params += `last_lines=${maxLines}`;
            params += `&format=raw`;
        } else {
            // Live logs API parameters
            params += `last_position=${lastPosition}`;
            params += `&max_lines=${maxLines}`;
            params += `&format=raw`;
            
            // Add filtering parameters for debug/system logs
            const filterParams = this.buildFilterParams();
            params += filterParams;
        }
        
        return params;
    }
    
    buildFilterParams() {
        // Build URL parameters for server-side filtering (only for live-logs endpoint)
        let params = '';
        
        // If raw mode is enabled, bypass all filtering
        if (this.rawMode) {
            console.log('[DEBUG] Raw mode active - bypassing all server-side filtering');
            return '';
        }
        
        // Get current filter options (either from this.filterOptions or from UI)
        const filterOptions = this.filterOptions || {};
        
        // Read from UI if filter options not set
        const excludeFrontendDebug = filterOptions.exclude_frontend_debug !== undefined ? 
            filterOptions.exclude_frontend_debug : 
            (document.getElementById('excludeFrontendDebug')?.checked || false);
            
        const excludeScrubbedAuth = filterOptions.exclude_scrubbed !== undefined ?
            filterOptions.exclude_scrubbed :
            (document.getElementById('excludeScrubbedAuth')?.checked || false);
            
        const eventCodes = filterOptions.event_codes !== undefined ?
            filterOptions.event_codes :
            (document.getElementById('eventCodesFilter')?.value || '');
            
        const searchTerm = filterOptions.search_term !== undefined ?
            filterOptions.search_term :
            (document.getElementById('logSearchInput')?.value || '');
        
        // Apply special filtering for system logs
        if (this.logSource === 'system') {
            // Force specific event codes for system logs
            params += '&event_codes=INIT,USER,METRICS,EVENT';
            params += '&exclude_frontend_debug=true';
            params += '&exclude_scrubbed=true';
        } else {
            // Normal filtering for debug logs
            if (excludeFrontendDebug) params += '&exclude_frontend_debug=true';
            if (excludeScrubbedAuth) params += '&exclude_scrubbed=true';
            if (eventCodes.trim()) params += `&event_codes=${encodeURIComponent(eventCodes)}`;
        }
        
        if (searchTerm.trim()) params += `&search_term=${encodeURIComponent(searchTerm)}`;
        
        return params;
    }
    
    async fetchLogs() {
        // Force reload with current filters
        if (this.isPaused) {
            this.isPaused = false;
            const pauseBtn = document.getElementById('logPauseBtn');
            if (pauseBtn) {
                pauseBtn.textContent = '⏸️ Pause';
                pauseBtn.title = 'Pause real-time updates';
            }
        }
        
        await this.pollForUpdates();
        
        // Restart polling if not already running
        if (!this.intervalId && this.isInitialized) {
            this.startPolling();
        }
    }
    
    stopPolling() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    destroy() {
        this.stopPolling();
        this.isInitialized = false;
    }
}

// Global instance
let liveLogViewer = new LiveLogViewer();

// Global functions for template button handlers
function initializeLiveLogViewer() {
    liveLogViewer.initialize();
    
    // Initialize Raw Mode button
    setTimeout(() => {
        updateRawModeButton();
    }, 100);
}

// Debug helper function for console access
window.debugSession = async function() {
    try {
        console.log('[DEBUG] Checking session status...');
        const response = await safeFetch('/dashboard/api/session-debug');
        const data = await response.json();
        
        console.log('[DEBUG] Session Debug Response:', data);
        console.log('[DEBUG] Session Summary:');
        console.log('  - Has session data:', data.session_status.has_session_data);
        console.log('  - Authenticated:', data.session_status.authenticated);
        console.log('  - Is SYSTEM user:', data.session_status.is_system_user);
        console.log('  - Username:', data.session_status.username);
        console.log('  - Has session cookie:', data.cookie_status.has_session_cookie);
        console.log('  - Cookie length:', data.cookie_status.session_cookie_length);
        
        if (!data.session_status.authenticated) {
            console.warn('[WARNING] User is not authenticated - need to log in');
        }
        if (!data.session_status.is_system_user) {
            console.warn('[WARNING] User is not SYSTEM user - need SYSTEM permissions for live logs');
        }
        
        return data;
    } catch (error) {
        console.error('[ERROR] debugSession failed:', error);
        return null;
    }
};

function toggleLogViewer() {
    liveLogViewer.toggle();
}

function clearLogViewer() {
    if (confirm('Clear log display? (This will not affect the actual log file)')) {
        liveLogViewer.clear();
    }
}

function copyLogContent() {
    liveLogViewer.copy();
}

// Raw Mode functionality - shows exact debug_trace.log content without any filtering
let rawModeEnabled = false;

function toggleRawMode() {
    rawModeEnabled = !rawModeEnabled;
    updateRawModeButton();
    
    if (rawModeEnabled) {
        console.log('[DEBUG] Raw mode ENABLED - showing unfiltered debug_trace.log tail content');
        // Force refresh with raw mode starting from current tail
        liveLogViewer.lastPosition = 0;  // This triggers tail reading logic
        liveLogViewer.rawMode = true;
        liveLogViewer.allContent = '';   // Clear existing content
        
        // Show loading message
        const logContent = document.getElementById('logContent');
        if (logContent) {
            logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">🔄 Loading raw debug_trace.log content...</div>';
        }
        
        liveLogViewer.fetchLogs();
    } else {
        console.log('[DEBUG] Raw mode DISABLED - using normal filtering from current tail');
        // Reset to normal mode but also start from current tail
        liveLogViewer.lastPosition = 0;  // This triggers tail reading logic
        liveLogViewer.rawMode = false;
        liveLogViewer.allContent = '';   // Clear existing content
        
        // Show loading message
        const logContent = document.getElementById('logContent');
        if (logContent) {
            logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">🔄 Loading filtered log content...</div>';
        }
        
        liveLogViewer.fetchLogs();
    }
}

function updateRawModeButton() {
    const button = document.getElementById('rawModeToggle');
    if (button) {
        if (rawModeEnabled) {
            button.style.background = '#059669'; // Darker green when active
            button.innerHTML = '✅ Raw Mode';
            button.title = 'Raw mode ON - showing exact debug_trace.log content (click to disable)';
        } else {
            button.style.background = '#10b981'; // Normal green when inactive
            button.innerHTML = '📄 Raw Mode';
            button.title = 'Raw mode OFF - applying filters (click to show exact debug_trace.log content)';
        }
    }
}

function jumpToLatestLogs() {
    console.log('[DEBUG] Jump to Latest clicked - resetting to current tail');
    // Reset position to force reading from current end of file
    liveLogViewer.lastPosition = 0;
    liveLogViewer.allContent = '';
    
    // Clear current display 
    const logContent = document.getElementById('logContent');
    if (logContent) {
        logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">🔄 Jumping to latest logs...</div>';
    }
    
    // Force immediate refresh from tail
    liveLogViewer.fetchLogs();
}

function updateLogInterval(interval) {
    liveLogViewer.updateInterval(interval);
}

function filterLogContent() {
    const searchInput = document.getElementById('logSearchInput');
    if (searchInput) {
        liveLogViewer.filter(searchInput.value);
    }
}

// Apply server-side log filtering
function applyLogFilters() {
    if (!liveLogViewer) {
        console.warn('Live log viewer not initialized');
        return;
    }
    
    const excludeFrontendDebug = document.getElementById('excludeFrontendDebug')?.checked || false;
    const excludeScrubbedAuth = document.getElementById('excludeScrubbedAuth')?.checked || false;
    const eventCodesFilter = document.getElementById('eventCodesFilter')?.value || '';
    const searchTerm = document.getElementById('logSearchInput')?.value || '';
    
    // Update filter status display
    updateFilterStatus(excludeFrontendDebug, excludeScrubbedAuth, eventCodesFilter);
    
    // Reset position to reload with filters
    liveLogViewer.lastPosition = 0;
    liveLogViewer.filterOptions = {
        exclude_frontend_debug: excludeFrontendDebug,
        exclude_scrubbed: excludeScrubbedAuth,
        event_codes: eventCodesFilter,
        search_term: searchTerm
    };
    
    // Clear current content and reload with filters
    const logContent = document.getElementById('logContent');
    if (logContent) {
        logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">🔄 Applying filters...</div>';
    }
    
    // Trigger immediate update with filters
    liveLogViewer.fetchLogs();
}

function updateFilterStatus(excludeFrontendDebug, excludeScrubbedAuth, eventCodesFilter) {
    const filterStatus = document.getElementById('filterStatus');
    if (!filterStatus) return;
    
    const activeFilters = [];
    if (excludeFrontendDebug) activeFilters.push('Hide FRONTEND_DEBUG');
    if (excludeScrubbedAuth) activeFilters.push('Hide scrubbed auth');
    if (eventCodesFilter.trim()) activeFilters.push(`Event codes: ${eventCodesFilter}`);
    
    if (activeFilters.length === 0) {
        filterStatus.textContent = 'Status: All filters disabled';
        filterStatus.style.color = '#64748b';
    } else {
        filterStatus.textContent = `Active: ${activeFilters.join(', ')}`;
        filterStatus.style.color = '#10b981';
    }
}

// Switch between log sources (debug, console, system)
function switchLogSource(source) {
    if (!liveLogViewer) {
        console.warn('Live log viewer not initialized');
        return;
    }
    
    console.log(`[DEBUG] Switching to log source: ${source}`);
    
    // Update tab appearance
    const tabs = document.querySelectorAll('.log-source-tab');
    tabs.forEach(tab => {
        tab.style.background = '#374151';
        tab.style.color = '#94a3b8';
        tab.classList.remove('active');
    });
    
    const activeTab = document.getElementById(`logSource${source.charAt(0).toUpperCase() + source.slice(1)}`);
    if (activeTab) {
        activeTab.style.background = '#3b82f6';
        activeTab.style.color = 'white';
        activeTab.classList.add('active');
    }
    
    // Update the live log viewer source
    liveLogViewer.logSource = source;
    liveLogViewer.lastPosition = 0;  // Reset position for new source
    
    // Clear current content and show loading
    const logContent = document.getElementById('logContent');
    if (logContent) {
        logContent.innerHTML = `<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">🔄 Loading ${source} logs...</div>`;
    }
    
    // Update status to show source change
    liveLogViewer.updateStatus('connecting', `Switching to ${source} logs...`);
    
    // Update source info in stats immediately
    const logSourceInfo = document.getElementById('logSourceInfo');
    if (logSourceInfo) {
        const sourceNames = {
            'debug': 'Debug Trace',
            'console': 'Console Output',
            'system': 'System Events'
        };
        logSourceInfo.textContent = sourceNames[source] || 'Debug Trace';
    }
    
    // Trigger reload with new source
    liveLogViewer.fetchLogs();
}

// Note: showTab function with log viewer support is now integrated above (lines 303-374)
// This duplicate definition has been removed to prevent recursion issues

// Export global functions
window.initializeLiveLogViewer = initializeLiveLogViewer;
window.toggleLogViewer = toggleLogViewer;
window.clearLogViewer = clearLogViewer;
window.copyLogContent = copyLogContent;
window.updateLogInterval = updateLogInterval;
window.filterLogContent = filterLogContent;
window.LiveLogViewer = LiveLogViewer;

// Generic Notification System
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.initialized = false;
    }
    
    initialize() {
        if (this.initialized) return;
        
        // Create notification container
        this.container = document.createElement('div');
        this.container.id = 'notificationContainer';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
            pointer-events: none;
        `;
        document.body.appendChild(this.container);
        this.initialized = true;
    }
    
    show(message, type = 'info', duration = 5000) {
        this.initialize();
        
        const notification = document.createElement('div');
        const notificationId = 'notification_' + Date.now() + Math.random();
        notification.id = notificationId;
        
        // Set styles based on type
        const typeStyles = {
            info: {
                background: 'rgba(59, 130, 246, 0.9)',
                border: '1px solid rgba(59, 130, 246, 0.3)',
                color: '#ffffff',
                icon: 'ℹ️'
            },
            success: {
                background: 'rgba(16, 185, 129, 0.9)', 
                border: '1px solid rgba(16, 185, 129, 0.3)',
                color: '#ffffff',
                icon: '✅'
            },
            warning: {
                background: 'rgba(245, 158, 11, 0.9)',
                border: '1px solid rgba(245, 158, 11, 0.3)', 
                color: '#ffffff',
                icon: '⚠️'
            },
            error: {
                background: 'rgba(239, 68, 68, 0.9)',
                border: '1px solid rgba(239, 68, 68, 0.3)',
                color: '#ffffff', 
                icon: '❌'
            }
        };
        
        const style = typeStyles[type] || typeStyles.info;
        
        notification.style.cssText = `
            background: ${style.background};
            border: ${style.border};
            color: ${style.color};
            padding: 12px 16px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
            animation: slideIn 0.3s ease forwards;
        `;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 8px;">
                <span style="font-size: 16px; flex-shrink: 0;">${style.icon}</span>
                <div style="flex: 1;">
                    <div style="font-weight: 600; margin-bottom: 2px;">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                    <div>${message}</div>
                </div>
                <button onclick="removeNotification('${notificationId}')" 
                        style="background: none; border: none; color: inherit; font-size: 18px; cursor: pointer; padding: 0; margin-left: 8px; opacity: 0.7; transition: opacity 0.2s;"
                        onmouseover="this.style.opacity='1'" 
                        onmouseout="this.style.opacity='0.7'">×</button>
            </div>
        `;
        
        // Click to dismiss
        notification.addEventListener('click', () => {
            this.remove(notificationId);
        });
        
        // Add slide-in animation
        const style_element = document.createElement('style');
        if (!document.getElementById('notification-animations')) {
            style_element.id = 'notification-animations';
            style_element.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                @keyframes slideOut {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style_element);
        }
        
        this.container.appendChild(notification);
        this.notifications.push(notificationId);
        
        // Auto-remove after duration (if duration > 0)
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notificationId);
            }, duration);
        }
        
        return notificationId;
    }
    
    remove(notificationId) {
        const notification = document.getElementById(notificationId);
        if (notification) {
            notification.style.animation = 'slideOut 0.3s ease forwards';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                this.notifications = this.notifications.filter(id => id !== notificationId);
            }, 300);
        }
    }
    
    clear() {
        this.notifications.forEach(id => this.remove(id));
    }
}

// Global notification system instance
const notificationSystem = new NotificationSystem();

// Global notification functions
function showNotification(message, type = 'info', duration = 5000) {
    return notificationSystem.show(message, type, duration);
}

function removeNotification(notificationId) {
    notificationSystem.remove(notificationId);
}

function clearAllNotifications() {
    notificationSystem.clear();
}

// Export notification system
window.showNotification = showNotification;
window.removeNotification = removeNotification;
window.clearAllNotifications = clearAllNotifications;
window.NotificationSystem = NotificationSystem;

// Test_Real Server Detection and Redirection
async function checkAndRedirectTestReal() {
    try {
        // Detect current port from URL
        const currentPort = window.location.port;
        let targetPort;
        
        // Determine target port based on current location
        if (currentPort === '40999') {
            targetPort = '41000';  // Switch to 41000
        } else if (currentPort === '41000') {
            targetPort = '40999';  // Switch back to 40999
        } else {
            targetPort = '40999';  // Default to 40999 for other locations
        }
        
        console.log(`Checking test_real server availability on port ${targetPort}`);
        showNotification(`Checking test_real server on port ${targetPort}...`, 'info', 2000);
        
        // Check if target server is available
        const response = await safeFetch(`/dashboard/api/check-test-server?port=${targetPort}`);
        const data = await response.json();
        
        if (data.available) {
            const targetUrl = `http://localhost:${targetPort}/login`;
            showNotification(`Redirecting to test_real server on port ${targetPort}`, 'success', 2000);
            
            // Redirect after a short delay for user feedback
            setTimeout(() => {
                window.location.href = targetUrl;
            }, 1000);
        } else {
            // Server not available - show error notification
            showNotification('test_real server is not running', 'error', 10000);
            console.log('Test_real server not available:', data);
        }
        
    } catch (error) {
        console.error('Failed to check test_real server:', error);
        showNotification('test_real server is not running', 'error', 10000);
    }
}

// Header styling for localhost:40999 detection
function checkAndApplyHeaderStyling() {
    // Check if we're on localhost:40999
    if (window.location.hostname === 'localhost' && window.location.port === '40999') {
        const header = document.querySelector('.header');
        if (header) {
            header.classList.add('test-server-mode');
            console.log('Applied red header styling for localhost:40999');
        }
    }
}

// Initialize header styling on page load
document.addEventListener('DOMContentLoaded', function() {
    checkAndApplyHeaderStyling();
});

// Also check when the page becomes visible (in case of navigation)
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        checkAndApplyHeaderStyling();
    }
});

// Export test_real functions
window.checkAndRedirectTestReal = checkAndRedirectTestReal;
window.checkAndApplyHeaderStyling = checkAndApplyHeaderStyling;