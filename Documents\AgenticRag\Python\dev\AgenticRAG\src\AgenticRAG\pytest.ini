[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
addopts = --tb=short -v
markers =
    unit: Unit tests for individual components
    integration: Integration tests for multiple components
    performance: Performance and load testing
    health: System health checks
    slow: Tests that take more than 5 seconds
    asyncio: Mark test as async
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning