#!/usr/bin/env python3
"""
Real Email System Integration Tests - PRODUCTION SYSTEM ONLY

This module tests the complete email system using the IDENTICAL production
system configuration. NO test environment isolation - this connects directly
to the production database, IMAP, SMTP, and all other services.

Test requirements:
- IMAP idle scheduled request exists for SYSTEM user
- SMTP email <NAME_EMAIL>
- Verification that mybot_generic.send_broadcast is reached after email arrives
"""

from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

import pytest
import asyncio
import os
import time
import json
from typing import Dict, Any, List
from uuid import uuid4
from datetime import datetime, timedelta
import functools
from pathlib import Path

# Test debug capture
from tests.test_real.test_debug_capture import setup_test_debug_capture, cleanup_test_debug_capture

# Import LogFire for logging
from managers.manager_logfire import LogFire


def with_test_logging(func):
    """Decorator to automatically enable test logging for async test methods"""
    @functools.wraps(func)
    async def wrapper(self, *args, **kwargs):
        # Start logging with the test function name
        setup_test_debug_capture(func.__name__)
        try:
            # Run the actual test
            result = await func(self, *args, **kwargs)
            return result
        except Exception as e:
            # Log the exception but ALWAYS re-raise it
            import traceback
            LogFire.log("DEBUG", f"\n[ERROR] Test failed with exception: {e}")
            LogFire.log("DEBUG", f"Traceback:\n{traceback.format_exc()}")
            raise  # CRITICAL: Always re-raise to ensure pytest sees the failure
        finally:
            # Always stop logging
            cleanup_test_debug_capture()
    return wrapper



def skip_if_no_smtp():
    """Skip test if SMTP is not configured"""
    return lambda func: func


def requires_openai_api():
    """Skip test if OpenAI API key is not configured"""
    # Import config.py to ensure API key is loaded
    try:
        import config
        api_key_configured = os.environ.get('OPENAI_API_KEY')
    except ImportError:
        api_key_configured = os.environ.get('OPENAI_API_KEY')
    
    return pytest.mark.skipif(
        not api_key_configured,
        reason="OpenAI API key not configured - ensure config.py is loaded"
    )


from tests.test_real.base_real_test import BaseRealTest

class TestRealEmail(BaseRealTest):
    """
    Test suite for real email system functionality using PRODUCTION system.
    
    This class uses the actual production mainFunc() via BaseRealTest to ensure
    100% identical behavior to production - no more "test_real isn't real" issues!
    """

    @with_test_logging
    @requires_openai_api()
    @skip_if_no_smtp()
    @pytest.mark.asyncio
    async def test_real_email(self):
        """Test real email functionality: IMAP idle scheduled request, SMTP sending, and mybot_generic.send_broadcast verification"""
        LogFire.log("DEBUG", f"[{self.test_name}] ===== STARTING TEST_REAL_EMAIL =====")
        LogFire.log("DEBUG", f"[{self.test_name}] Test will verify: IMAP idle -> Email sending -> Broadcast detection")
        
        try:
            # Initialize production system
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: Initializing production system...")
            setup_success = await self.setup_real_system()
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: Setup result = {setup_success}")
            
            if not setup_success:
                LogFire.log("DEBUG", f"[{self.test_name}] ERROR: Production system setup failed!")
                pytest.fail("Failed to initialize real production system")
            
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: [OK] Production system initialized successfully")
        
            # Get user and bot
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: Getting production test user and bot...")
            user = self.production_test_user
            bot = self.production_testing_bot
            user.email = "<EMAIL>"
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: [OK] User: {user.username}, Bot: {getattr(bot, 'name', 'Testing')}, Email: {user.email}")
        
            # Import modules needed for test
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: Importing required managers...")
            from managers.manager_system_user import SystemUserManager
            from managers.scheduled_requests import ScheduledRequestPersistenceManager
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: [OK] Managers imported successfully")
            
            # Test configuration
            test_email_subject = f"Test Email {str(uuid4())[:8]}"
            test_start_time = datetime.now()
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: [OK] Test config: Subject='{test_email_subject}', Start={test_start_time}")
        
            # Step 1: Verify IMAP IDLE scheduled request exists
            LogFire.log("DEBUG", f"[{self.test_name}] ===== STEP 1: IMAP IDLE VERIFICATION =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Getting SystemUserManager instance...")
            system_user_manager = SystemUserManager.get_instance()
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: [OK] SystemUserManager obtained")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Getting SYSTEM user...")
            system_user = await system_user_manager.get_system_user()
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: System user result: {system_user is not None}")
            
            if not system_user:
                LogFire.log("DEBUG", f"[{self.test_name}] ERROR: SYSTEM user not found!")
                pytest.fail("SYSTEM user not found")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: [OK] SYSTEM user found: {system_user.username}")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Getting ScheduledRequestPersistenceManager...")
            persistence_manager = ScheduledRequestPersistenceManager.get_instance()
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: [OK] PersistenceManager obtained")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Querying active requests for SYSTEM user GUID: {system_user_manager.SYSTEM_USER_GUID}")
            existing_requests = await persistence_manager.get_active_requests(str(system_user_manager.SYSTEM_USER_GUID))
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Found {len(existing_requests)} active requests")
            
            # Log all existing requests for debugging
            for i, request in enumerate(existing_requests):
                target_prompt = request.get('target_prompt', 'No target_prompt')
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Request {i+1}: target_prompt='{target_prompt}'")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Checking for IMAP-related requests...")
            imap_task_exists = any(
                task.get('target_prompt') and "imap" in task.get('target_prompt', '').lower()
                for task in existing_requests
            )
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: IMAP task exists: {imap_task_exists}")
            
            if not imap_task_exists:
                LogFire.log("DEBUG", f"[{self.test_name}] ERROR: No IMAP idle scheduled request found!")
                LogFire.log("DEBUG", f"[{self.test_name}] ERROR: Available requests: {[r.get('target_prompt', 'N/A') for r in existing_requests]}")
                pytest.fail("IMAP idle scheduled request must exist")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: [OK] IMAP idle request exists and is active")
        
            # Step 1.5: Wait for IMAP to fully activate
            LogFire.log("DEBUG", f"[{self.test_name}] ===== STEP 1.5: IMAP ACTIVATION WAIT =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5: About to wait 30 seconds for IMAP to fully activate...")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5: During this wait, background IMAP tasks should be running")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5: Look for 'imap_idle_activate' logs in the output")
            
            # Wait in smaller chunks with progress logging
            for second in range(30):
                await asyncio.sleep(1)
                if second % 5 == 0 or second == 29:  # Log every 5 seconds and at the end
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5: IMAP wait progress: {second+1}/30 seconds elapsed")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5: [OK] IMAP activation wait complete - IMAP should now be active")
        
            # Step 2: Clear broadcast history and send email
            LogFire.log("DEBUG", f"[{self.test_name}] ===== STEP 2: EMAIL SENDING =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Clearing broadcast history...")
            
            # Clear any existing broadcast history before sending email
            bot.clear_broadcast_history()
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: [OK] Broadcast history cleared")
            
            query = f"send a test <NAME_EMAIL> with subject '{test_email_subject}'"
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Preparing email query: '{query}'")
        
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: About to call user.on_message() for email sending...")
            task_result = None
            try:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Calling user.on_message()...")
                task_result = await user.on_message(
                    complete_message=query,
                    calling_bot=bot,
                    attachments=[],
                    original_message=None
                )
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: user.on_message() completed successfully")
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Task result type: {type(task_result)}, Value: {task_result}")
            except Exception as e:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: ERROR in user.on_message(): {e}")
                import traceback
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Full traceback: {traceback.format_exc()}")
            
            email_sent = task_result is not None
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Email sending result - Success: {email_sent}")
            
            if not email_sent:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: WARNING: Email task was not created successfully")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: [OK] Email task created successfully")
        
            # Step 3: Wait for broadcast using direct Testing bot check
            LogFire.log("DEBUG", f"[{self.test_name}] ===== STEP 3: BROADCAST DETECTION =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Setting up broadcast detection using Testing bot...")
        
            # Create direct broadcast assertion function
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Creating broadcast assertion function...")
            async def direct_broadcast_assertion():
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Checking for broadcasts since {test_start_time}...")
                broadcasts = bot.get_broadcasts_since(test_start_time)
                has_broadcast = bot.has_broadcast_since(test_start_time)
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Found {len(broadcasts)} broadcasts, has_broadcast={has_broadcast}")
                return {
                    "passed": has_broadcast,
                    "message": f"Broadcasts found: {len(broadcasts)} since {test_start_time}",
                    "data": {
                        "broadcasts": broadcasts,
                        "broadcast_count": len(broadcasts),
                        "latest_broadcast": bot.get_latest_broadcast()
                    }
                }
        
            # Wait for broadcast using direct bot polling (30-second intervals)
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Starting broadcast polling (8 minutes timeout, 30-second intervals)...")
            broadcast_result = await self.wait_for_assertions(
                assertion_func=direct_broadcast_assertion,
                timeout=480,  # 8 minutes
                poll_interval=30,  # 30 seconds for dashboard monitoring
                assertion_name="direct_broadcast_detection"
            )
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Broadcast polling completed")
        
            broadcast_reached = broadcast_result["success"]
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Broadcast detection result: {broadcast_reached}")
            
            if broadcast_reached:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: [OK] Broadcast detected after {broadcast_result['elapsed_time']:.1f}s ({broadcast_result['poll_count']} polls)")
                # Log details about the detected broadcasts
                final_data = broadcast_result.get('final_data', {})
                broadcast_count = final_data.get('broadcast_count', 0)
                latest_broadcast = final_data.get('latest_broadcast', {})
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Total broadcasts received: {broadcast_count}")
                if latest_broadcast:
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Latest broadcast: {latest_broadcast.get('truncated_text', 'N/A')}")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: WARNING: Broadcast not detected within timeout")
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Final message: {broadcast_result['final_message']}")
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Total broadcasts in history: {bot.get_broadcast_count()}")
        
            # Summary
            LogFire.log("DEBUG", f"[{self.test_name}] ===== FINAL TEST SUMMARY =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1 - IMAP exists: [OK]")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5 - IMAP activation wait: [OK]")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2 - Email sent: {'[OK]' if email_sent else '[ERROR]'}")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3 - Broadcast reached: {'[OK]' if broadcast_reached else '[ERROR]'}")
            LogFire.log("DEBUG", f"[{self.test_name}] Final broadcast count: {bot.get_broadcast_count()}")
        
            # Assertions
            LogFire.log("DEBUG", f"[{self.test_name}] ===== RUNNING FINAL ASSERTIONS =====")
            
            LogFire.log("DEBUG", f"[{self.test_name}] Assertion 1: IMAP task exists = {imap_task_exists}")
            assert imap_task_exists, "IMAP idle scheduled request must exist"
            
            LogFire.log("DEBUG", f"[{self.test_name}] Assertion 2: Email sent = {email_sent}")
            assert email_sent, "Email sending must succeed"
            
            LogFire.log("DEBUG", f"[{self.test_name}] Assertion 3: Broadcast reached = {broadcast_reached}")
            assert broadcast_reached, "Broadcast detection must succeed"
            
            LogFire.log("DEBUG", f"[{self.test_name}] ===== TEST COMPLETED SUCCESSFULLY =====")
            
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] ===== CRITICAL ERROR IN TEST =====")
            LogFire.log("DEBUG", f"[{self.test_name}] Exception: {type(e).__name__}: {e}")
            import traceback
            LogFire.log("DEBUG", f"[{self.test_name}] Full traceback: {traceback.format_exc()}")
            LogFire.log("DEBUG", f"[{self.test_name}] Test failed due to exception - re-raising...")
            raise  # Re-raise to ensure pytest sees the failure
