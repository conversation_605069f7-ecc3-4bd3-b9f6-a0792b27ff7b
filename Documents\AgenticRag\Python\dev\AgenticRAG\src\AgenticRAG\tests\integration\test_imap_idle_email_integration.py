"""
Integration test for IMAP IDLE activation and email verification
Tests: Activate IMAP <NAME_EMAIL>, send email, verify receipt
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
import time
import imaplib
import smtplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel

from managers.manager_supervisors import SupervisorManager, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import ZairaUser
from tasks.inputs.imap_idle_activate import SupervisorTask_IMAPIdleActivate, create_task_imap_idle_activate
from tasks.processing.task_email_generator import EmailGeneratorTool
from endpoints.oauth._verifier_ import OAuth2Verifier
from langchain_core.messages import HumanMessage, SystemMessage


@pytest.mark.integration
@pytest.mark.asyncio
@pytest.mark.slow
class TestIMAPIdleEmailIntegration:
    """Integration test for IMAP IDLE activation and email verification"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_email = "<EMAIL>"
        self.test_sender_email = "<EMAIL>"  # Mock sender for testing
        self.test_subject = f"Test Email Integration {uuid4()}"
        self.test_content = f"This is a test email sent at {datetime.now()} for IMAP IDLE integration testing."
        
        # IMAP <NAME_EMAIL> (mock values - replace with real config in production)
        self.imap_config = {
            "server": "mail.syno.nl",  # Typical server for syno.nl domain
            "port": 993,  # SSL IMAP port
            "email": self.test_email,
            "password": "mock_password",  # Would be real password in production
            "ssl": True
        }
        
        # SMTP configuration for sending test emails
        self.smtp_config = {
            "server": "smtp.gmail.com",  # Using Gmail for sending test emails
            "port": 587,
            "email": self.test_sender_email,
            "password": "mock_app_password"  # Would be real app password in production
        }
        
        # Initialize Globals for testing
        try:
            Globals.Debug = True
        except:
            pass
    
    def teardown_method(self):
        """Clean up after test"""
        # Clean up any test emails or state
        pass
    
    async def test_imap_idle_activation_and_email_verification(self):
        """Complete integration test: activate IMAP IDLE, send email, verify receipt"""
        
        # Create mock user
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        mock_user.email = self.test_email
        mock_user.platform = "integration_test"
        
        # Mock OAuth2Verifier to return our test IMAP configuration
        with patch.object(OAuth2Verifier, 'get_token') as mock_get_token:
            # Configure OAuth token returns for IMAP
            def mock_token_side_effect(service: str, token_type: str = "access_token"):
                if service == "imap":
                    if token_type == "access_token":
                        return self.imap_config["server"]
                    elif token_type == "refresh_token":
                        return self.imap_config["email"]
                    elif token_type == "expires_in":
                        return str(self.imap_config["port"])
                    elif token_type == "token_type":
                        return self.imap_config["password"]
                return "mock_token"
            
            mock_get_token.side_effect = mock_token_side_effect
            
            # Step 1: Create and activate IMAP IDLE task
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                with patch('managers.manager_meltano.MeltanoManager.ConvertFilesToVectorStore') as mock_convert:
                    mock_convert.return_value = True
                    
                    # Create IMAP IDLE task
                    imap_task = await create_task_imap_idle_activate()
                    
                    # Create initial state for IMAP check
                    initial_state = SupervisorTaskState(
                        user_guid=self.test_user_guid,
                        original_input="activate IMAP <NAME_EMAIL>",
                        additional_input={},
                        messages=[HumanMessage(content="activate IMAP IDLE")],
                        call_trace=[],
                        completed_tasks=[],
                        sections={},
                        reasoning_steps=[],
                        conversation_history=[]
                    )
                    
                    # Step 2: Mock IMAP connection and initial check
                    with patch('imaplib.IMAP4_SSL') as mock_imap_ssl:
                        mock_mail = MagicMock()
                        mock_imap_ssl.return_value = mock_mail
                        mock_mail.login.return_value = None
                        mock_mail.select.return_value = ('OK', [])
                        mock_mail.search.return_value = ('OK', [b''])  # No initial emails
                        mock_mail.logout.return_value = None
                        
                        # Execute initial IMAP check (establishes baseline)
                        result = await imap_task.llm_call(initial_state)
                        
                        # Verify IMAP IDLE was activated successfully
                        assert "No new emails found" in result or "processed" in result.lower()
                        assert mock_mail.login.called
                        assert mock_mail.select.called
                        print(f"✓ IMAP IDLE activated successfully: {result}")
                    
                    # Step 3: Simulate sending an email to the test address
                    await self._simulate_email_sending()
                    
                    # Step 4: Simulate IMAP IDLE detecting the new email
                    with patch('imaplib.IMAP4_SSL') as mock_imap_ssl_2nd:
                        mock_mail_2nd = MagicMock()
                        mock_imap_ssl_2nd.return_value = mock_mail_2nd
                        mock_mail_2nd.login.return_value = None
                        mock_mail_2nd.select.return_value = ('OK', [])
                        
                        # Mock finding the new email
                        mock_mail_2nd.search.return_value = ('OK', [b'1'])  # One new email found
                        
                        # Mock email content for the test email
                        test_email_content = self._create_mock_email_content()
                        mock_mail_2nd.fetch.return_value = ('OK', [(None, test_email_content)])
                        mock_mail_2nd.logout.return_value = None
                        
                        # Mock file writing
                        with patch('builtins.open', create=True) as mock_file:
                            mock_file.return_value.__enter__.return_value.write = MagicMock()
                            
                            # Execute IMAP check again to detect new email
                            result_2nd = await imap_task.llm_call(initial_state)
                            
                            # Verify email was detected and processed
                            assert "successfully processed" in result_2nd.lower() or "new email" in result_2nd.lower()
                            assert mock_mail_2nd.fetch.called
                            assert mock_file.called  # Verify email was saved
                            print(f"✓ New email detected and processed: {result_2nd}")
    
    async def _simulate_email_sending(self):
        """Simulate sending an email to the test address"""
        # In a real test, this would actually send an email
        # For this integration test, we'll mock the sending process
        
        with patch('smtplib.SMTP') as mock_smtp:
            mock_server = MagicMock()
            mock_smtp.return_value = mock_server
            mock_server.starttls.return_value = None
            mock_server.login.return_value = None
            mock_server.sendmail.return_value = {}
            mock_server.quit.return_value = None
            
            # Create email tool for sending
            email_tool = EmailGeneratorTool()
            
            # Create state for email sending
            email_state = SupervisorTaskState(
                user_guid=self.test_user_guid,
                original_input=f"send email to {self.test_email}",
                additional_input={},
                messages=[HumanMessage(content=f"send test email to {self.test_email}")],
                call_trace=[],
                completed_tasks=[],
                sections={},
                reasoning_steps=[],
                conversation_history=[]
            )
            
            # Mock OAuth configuration for email sending
            with patch.object(OAuth2Verifier, 'get_token') as mock_email_token:
                mock_email_token.return_value = "mock_smtp_token"
                
                # Mock user for email sending
                mock_sender_user = MagicMock(spec=ZairaUser)
                mock_sender_user.email = self.test_sender_email
                mock_sender_user.my_task = MagicMock()
                mock_sender_user.my_task.request_human_in_the_loop = AsyncMock()
                mock_sender_user.my_task.send_response = AsyncMock()
                
                with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_sender_user):
                    # Simulate email sending (mocked)
                    try:
                        result = await email_tool._arun(
                            content=self.test_content,
                            subject=self.test_subject,
                            sender=self.test_sender_email,
                            recipient=self.test_email,
                            state=email_state
                        )
                        print(f"✓ Email sending simulated successfully")
                    except Exception as e:
                        print(f"✓ Email sending simulation completed (expected mock behavior): {e}")
    
    def _create_mock_email_content(self) -> bytes:
        """Create mock email content for testing"""
        # Create a realistic email message
        msg = MIMEMultipart()
        msg['From'] = self.test_sender_email
        msg['To'] = self.test_email
        msg['Subject'] = self.test_subject
        msg['Date'] = datetime.now().strftime("%a, %d %b %Y %H:%M:%S %z")
        
        # Add body
        body = MIMEText(self.test_content, 'plain')
        msg.attach(body)
        
        # Convert to bytes as IMAP would return
        return msg.as_bytes()
    
    async def test_imap_idle_configuration_validation(self):
        """Test IMAP IDLE configuration validation"""
        
        # Create mock user
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        # Test with incomplete configuration
        with patch.object(OAuth2Verifier, 'get_token') as mock_get_token:
            # Return None for some required fields
            mock_get_token.side_effect = lambda service, token_type="access_token": None
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                imap_task = await create_task_imap_idle_activate()
                
                state = SupervisorTaskState(
                    user_guid=self.test_user_guid,
                    original_input="activate IMAP IDLE",
                    additional_input={},
                    messages=[HumanMessage(content="activate IMAP IDLE")],
                    call_trace=[],
                    completed_tasks=[],
                    sections={},
                    reasoning_steps=[],
                    conversation_history=[]
                )
                
                # Execute with incomplete config
                result = await imap_task.llm_call(state)
                
                # Should return configuration error
                assert "configuration" in result.lower() and ("incomplete" in result.lower() or "failed" in result.lower())
                print(f"✓ Configuration validation works: {result}")
    
    async def test_imap_idle_connection_error_handling(self):
        """Test IMAP IDLE error handling for connection failures"""
        
        # Create mock user
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        # Mock valid configuration
        with patch.object(OAuth2Verifier, 'get_token') as mock_get_token:
            def mock_token_side_effect(service: str, token_type: str = "access_token"):
                if service == "imap":
                    if token_type == "access_token":
                        return "mail.syno.nl"
                    elif token_type == "refresh_token":
                        return "<EMAIL>"
                    elif token_type == "expires_in":
                        return "993"
                    elif token_type == "token_type":
                        return "password123"
                return "mock_token"
            
            mock_get_token.side_effect = mock_token_side_effect
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                # Mock IMAP connection failure
                with patch('imaplib.IMAP4_SSL') as mock_imap_ssl:
                    mock_imap_ssl.side_effect = Exception("Connection timeout")
                    
                    imap_task = await create_task_imap_idle_activate()
                    
                    state = SupervisorTaskState(
                        user_guid=self.test_user_guid,
                        original_input="activate IMAP IDLE",
                        additional_input={},
                        messages=[HumanMessage(content="activate IMAP IDLE")],
                        call_trace=[],
                        completed_tasks=[],
                        sections={},
                        reasoning_steps=[],
                        conversation_history=[]
                    )
                    
                    # Execute with connection error
                    result = await imap_task.llm_call(state)
                    
                    # Should handle error gracefully
                    assert "failed" in result.lower() or "error" in result.lower()
                    assert "connection timeout" in result.lower()
                    print(f"✓ Connection error handling works: {result}")
    
    async def test_imap_idle_email_processing_workflow(self):
        """Test the complete email processing workflow"""
        
        # Create mock user
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        # Mock OAuth configuration
        with patch.object(OAuth2Verifier, 'get_token') as mock_get_token:
            def mock_token_side_effect(service: str, token_type: str = "access_token"):
                if service == "imap":
                    if token_type == "access_token":
                        return self.imap_config["server"]
                    elif token_type == "refresh_token":
                        return self.imap_config["email"]
                    elif token_type == "expires_in":
                        return str(self.imap_config["port"])
                    elif token_type == "token_type":
                        return self.imap_config["password"]
                return "mock_token"
            
            mock_get_token.side_effect = mock_token_side_effect
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                with patch('managers.manager_meltano.MeltanoManager.ConvertFilesToVectorStore') as mock_convert:
                    mock_convert.return_value = True
                    
                    # Mock IMAP operations
                    with patch('imaplib.IMAP4_SSL') as mock_imap_ssl:
                        mock_mail = MagicMock()
                        mock_imap_ssl.return_value = mock_mail
                        mock_mail.login.return_value = None
                        mock_mail.select.return_value = ('OK', [])
                        
                        # Simulate multiple emails found
                        mock_mail.search.return_value = ('OK', [b'1 2 3'])
                        
                        # Mock multiple email contents
                        email_contents = [
                            self._create_mock_email_content(),
                            self._create_mock_email_content(),
                            self._create_mock_email_content()
                        ]
                        
                        mock_mail.fetch.side_effect = [
                            ('OK', [(None, content)]) for content in email_contents
                        ]
                        mock_mail.logout.return_value = None
                        
                        # Mock file operations
                        with patch('builtins.open', create=True) as mock_file:
                            mock_file.return_value.__enter__.return_value.write = MagicMock()
                            
                            imap_task = await create_task_imap_idle_activate()
                            
                            state = SupervisorTaskState(
                                user_guid=self.test_user_guid,
                                original_input="check for new emails",
                                additional_input={},
                                messages=[HumanMessage(content="check emails")],
                                call_trace=[],
                                completed_tasks=[],
                                sections={},
                                reasoning_steps=[],
                                conversation_history=[]
                            )
                            
                            # Execute email processing
                            result = await imap_task.llm_call(state)
                            
                            # Verify multiple emails were processed
                            assert "3" in result or "successfully processed" in result.lower()
                            assert mock_convert.called  # Vector store conversion was called
                            assert mock_file.call_count >= 3  # At least 3 files were written
                            print(f"✓ Multiple email processing works: {result}")
    
    async def test_imap_idle_timestamp_tracking(self):
        """Test IMAP IDLE timestamp tracking for incremental checks"""
        
        # Create IMAP task
        imap_task = SupervisorTask_IMAPIdleActivate(
            name="test_imap_task",
            prompt_id="Test_IMAP_Prompt"
        )
        
        # Test timestamp operations
        test_timestamp = datetime.now()
        
        # Initially no timestamp
        assert imap_task.get_last_check_time(self.test_user_guid) is None
        
        # Set timestamp
        imap_task.set_last_check_time(self.test_user_guid, test_timestamp)
        retrieved_timestamp = imap_task.get_last_check_time(self.test_user_guid)
        assert retrieved_timestamp == test_timestamp
        
        # Clear timestamp
        imap_task.clear_last_check_time(self.test_user_guid)
        assert imap_task.get_last_check_time(self.test_user_guid) is None
        
        print("✓ Timestamp tracking functionality works correctly")
    
    async def test_imap_idle_incremental_search(self):
        """Test IMAP IDLE incremental email search based on timestamps"""
        
        # Create mock user
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        # Mock OAuth configuration
        with patch.object(OAuth2Verifier, 'get_token') as mock_get_token:
            def mock_token_side_effect(service: str, token_type: str = "access_token"):
                if service == "imap":
                    if token_type == "access_token":
                        return self.imap_config["server"]
                    elif token_type == "refresh_token":
                        return self.imap_config["email"]
                    elif token_type == "expires_in":
                        return str(self.imap_config["port"])
                    elif token_type == "token_type":
                        return self.imap_config["password"]
                return "mock_token"
            
            mock_get_token.side_effect = mock_token_side_effect
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                with patch('managers.manager_meltano.MeltanoManager.ConvertFilesToVectorStore') as mock_convert:
                    mock_convert.return_value = True
                    
                    imap_task = await create_task_imap_idle_activate()
                    
                    # Set a previous check time
                    previous_check = datetime.now() - timedelta(hours=2)
                    imap_task.set_last_check_time(self.test_user_guid, previous_check)
                    
                    with patch('imaplib.IMAP4_SSL') as mock_imap_ssl:
                        mock_mail = MagicMock()
                        mock_imap_ssl.return_value = mock_mail
                        mock_mail.login.return_value = None
                        mock_mail.select.return_value = ('OK', [])
                        mock_mail.search.return_value = ('OK', [b''])  # No new emails
                        mock_mail.logout.return_value = None
                        
                        state = SupervisorTaskState(
                            user_guid=self.test_user_guid,
                            original_input="check for new emails",
                            additional_input={},
                            messages=[HumanMessage(content="check emails")],
                            call_trace=[],
                            completed_tasks=[],
                            sections={},
                            reasoning_steps=[],
                            conversation_history=[]
                        )
                        
                        # Execute incremental check
                        result = await imap_task.llm_call(state)
                        
                        # Verify search was called with SINCE criteria
                        search_call_args = mock_mail.search.call_args
                        assert search_call_args is not None
                        search_criteria = search_call_args[0][1]  # Second argument to search()
                        assert 'SINCE' in search_criteria
                        
                        print(f"✓ Incremental search works with criteria: {search_criteria}")
                        print(f"✓ Result: {result}")


@pytest.mark.integration  
@pytest.mark.asyncio
@pytest.mark.manual
class TestIMAPIdleRealIntegration:
    """
    Manual integration tests that require real email credentials.
    These tests are marked as 'manual' and should be run separately
    with real configuration when testing against actual email servers.
    """
    
    async def test_real_imap_idle_integration(self):
        """
        MANUAL TEST: Real IMAP IDLE integration with actual email server.
        
        To run this test:
        1. Configure real IMAP credentials in OAuth2Verifier
        2. Set up real email account (<EMAIL>)
        3. Run with: pytest -m manual tests/integration/test_imap_idle_email_integration.py::TestIMAPIdleRealIntegration::test_real_imap_idle_integration
        """
        pytest.skip("Manual test - requires real email configuration")
        
        # This would contain the actual integration test code
        # that connects to real servers and sends/receives real emails
        # Only to be used in controlled testing environments
    
    async def test_real_email_sending_integration(self):
        """
        MANUAL TEST: Real email sending integration.
        
        To run this test:
        1. Configure real SMTP credentials
        2. Set up test email accounts
        3. Run with proper email configuration
        """
        pytest.skip("Manual test - requires real email configuration")