#!/usr/bin/env python3
"""
Pytest wrapper for test_real_execution_suite batch file execution.
This allows VS Code's "Run Tests" to discover and execute the batch file.
"""

import pytest
import asyncio
from pathlib import Path
import sys
import os

# Add the project root to the path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.test_real.base_real_test import BaseRealTest

class TestBatchExecutionSuite:
    """Python wrapper test for test_real_execution_suite batch execution"""
    
    @pytest.mark.asyncio
    async def test_batch_execution_suite_execution(self):
        """Execute test_real_execution_suite via batch file with VS Code integration"""
        # Use the token-efficient batch execution system
        result = await BaseRealTest.run_test_real("execution_suite", timeout=600)
        
        # Assert the test completed successfully
        assert result["success"], f"test_real_execution_suite failed: {result.get('brief_summary', 'Unknown error')}"
        
        # Log success for VS Code
        print(f"Batch execution completed in {result['execution_time']:.1f}s")
        print(f"Summary: {result['brief_summary']}")
        if result.get('log_directory'):
            print(f"Full logs available in: {result['log_directory']}")

    def test_batch_file_exists(self):
        """Verify the batch file exists and is executable"""
        batch_path = Path(__file__).parent.parent / "bats" / "run_test_real_execution_suite.bat"
        assert batch_path.exists(), f"Batch file not found: {batch_path}"
        assert batch_path.is_file(), f"Path is not a file: {batch_path}"
        
        # Check batch file content
        content = batch_path.read_text().strip()
        assert "pytest.exe" in content, "Batch file should contain pytest command"
        assert "test_real_execution_suite.py" in content, "Batch file should reference test_real_execution_suite.py"