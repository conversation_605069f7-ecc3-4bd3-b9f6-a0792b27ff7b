<body>
    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-avatar">
                <span class="avatar-initials">{user_initials}</span>
            </div>
            <h1 class="profile-name">{display_name}</h1>
            <p class="profile-subtitle">{job_title}</p>
            <div class="status-badge {status_class}">{status_text}</div>
        </div>
        
        <!-- Profile Statistics -->
        <div class="profile-stats">
            <div class="stat-card">
                <div class="stat-value">{total_requests}</div>
                <div class="stat-label">Total Requests</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{active_sessions}</div>
                <div class="stat-label">Active Sessions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{permission_level}</div>
                <div class="stat-label">Permission Level</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{last_activity}</div>
                <div class="stat-label">Last Activity</div>
            </div>
        </div>
        
        <!-- Messages -->
        {messages_html}
        
        <!-- Profile Form -->
        <div class="profile-form">
            <form id="profileForm" method="POST" action="/profile/update">
                <!-- Personal Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <svg class="section-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                        Personal Information
                    </h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="first_name">First Name</label>
                            <input type="text" id="first_name" name="first_name" class="form-input" 
                                   value="{first_name}" placeholder="Enter your first name" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="last_name">Last Name</label>
                            <input type="text" id="last_name" name="last_name" class="form-input" 
                                   value="{last_name}" placeholder="Enter your last name" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="email">Email Address</label>
                        <input type="email" id="email" name="email" class="form-input" 
                               value="{email}" placeholder="Enter your email address" required>
                    </div>
                </div>
                
                <!-- Professional Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <svg class="section-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                        Professional Information
                    </h3>
                    
                    <div class="form-group">
                        <label class="form-label" for="job_title">Job Title</label>
                        <input type="text" id="job_title" name="job_title" class="form-input" 
                               value="{job_title}" placeholder="Enter your job title">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="company">Company/Organization</label>
                        <input type="text" id="company" name="company" class="form-input" 
                               value="{company}" placeholder="Enter your company or organization">
                    </div>
                </div>
                
                <!-- AI Assistant Settings Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <svg class="section-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                        </svg>
                        AI Assistant Settings
                    </h3>
                    
                    <div class="form-group">
                        <label class="form-label" for="personal_prompt">Personal AI Prompt</label>
                        <textarea id="personal_prompt" name="personal_prompt" class="form-input form-textarea" 
                                  placeholder="Enter your personal prompt or instructions for the AI assistant. This will help customize responses to your preferences and working style.">{personal_prompt}</textarea>
                        <small style="color: var(--dashboard-text-secondary); font-size: 0.9rem; margin-top: var(--spacing-xs); display: block;">
                            This prompt will be used to personalize AI responses. Include your communication preferences, domain expertise, or specific instructions.
                        </small>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="preferred_language">Preferred Language</label>
                            <select id="preferred_language" name="preferred_language" class="form-input">
                                <option value="en" {en_selected}>English</option>
                                <option value="nl" {nl_selected}>Dutch</option>
                                <option value="fr" {fr_selected}>French</option>
                                <option value="de" {de_selected}>German</option>
                                <option value="es" {es_selected}>Spanish</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="timezone">Timezone</label>
                            <select id="timezone" name="timezone" class="form-input">
                                <option value="UTC" {utc_selected}>UTC</option>
                                <option value="Europe/Amsterdam" {amsterdam_selected}>Europe/Amsterdam</option>
                                <option value="America/New_York" {ny_selected}>America/New_York</option>
                                <option value="America/Los_Angeles" {la_selected}>America/Los_Angeles</option>
                                <option value="Asia/Tokyo" {tokyo_selected}>Asia/Tokyo</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                        </svg>
                        Reset
                    </button>
                    <button type="submit" class="btn btn-primary" id="saveButton">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        Save Profile
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>Updating your profile...</p>
        </div>
    </div>

    <script>
        // Profile form functionality
        document.getElementById('profileForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loadingOverlay = document.getElementById('loadingOverlay');
            const saveButton = document.getElementById('saveButton');
            
            // Show loading state
            loadingOverlay.style.display = 'flex';
            saveButton.disabled = true;
            
            try {
                const formData = new FormData(this);
                const response = await fetch('/profile/update', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('Profile updated successfully!', 'success');
                    // Update header display name if changed
                    updateHeaderInfo();
                } else {
                    showMessage(result.error || 'Failed to update profile', 'error');
                }
                
            } catch (error) {
                console.error('Error updating profile:', error);
                showMessage('Network error occurred while updating profile', 'error');
            } finally {
                // Hide loading state
                loadingOverlay.style.display = 'none';
                saveButton.disabled = false;
            }
        });
        
        function resetForm() {
            if (confirm('Are you sure you want to reset all changes?')) {
                document.getElementById('profileForm').reset();
                removeMessages();
            }
        }
        
        function showMessage(text, type) {
            removeMessages();
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${type}`;
            messageDiv.innerHTML = `
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                    ${type === 'success' ? 
                        '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>' :
                        '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>'
                    }
                </svg>
                ${text}
            `;
            
            const form = document.querySelector('.profile-form');
            form.parentNode.insertBefore(messageDiv, form);
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.remove();
                    }
                }, 5000);
            }
        }
        
        function removeMessages() {
            const existingMessages = document.querySelectorAll('.message');
            existingMessages.forEach(msg => msg.remove());
        }
        
        function updateHeaderInfo() {
            const firstName = document.getElementById('first_name').value;
            const lastName = document.getElementById('last_name').value;
            const jobTitle = document.getElementById('job_title').value;
            
            const displayName = `${firstName} ${lastName}`.trim() || 'User Profile';
            
            // Update header elements
            const profileNameEl = document.querySelector('.profile-name');
            const profileSubtitleEl = document.querySelector('.profile-subtitle');
            const avatarInitialsEl = document.querySelector('.avatar-initials');
            
            if (profileNameEl) profileNameEl.textContent = displayName;
            if (profileSubtitleEl) profileSubtitleEl.textContent = jobTitle;
            if (avatarInitialsEl) {
                const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
                avatarInitialsEl.textContent = initials || 'U';
            }
        }
        
        // Initialize form validation
        document.addEventListener('DOMContentLoaded', function() {
            const requiredFields = document.querySelectorAll('input[required]');
            
            requiredFields.forEach(field => {
                field.addEventListener('blur', function() {
                    if (!this.value.trim()) {
                        this.style.borderColor = 'var(--status-error)';
                    } else {
                        this.style.borderColor = 'var(--dashboard-glass-border)';
                    }
                });
            });
            
            // Email validation
            const emailField = document.getElementById('email');
            emailField.addEventListener('blur', function() {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (this.value && !emailRegex.test(this.value)) {
                    this.style.borderColor = 'var(--status-error)';
                } else if (this.value) {
                    this.style.borderColor = 'var(--status-success)';
                }
            });
        });
    </script>