<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AskZaira Profile</title>
    <style>
        /* Import Whitelabel Styles */
        {whitelabel_css}
        
        /* Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--brand-background);
            background-image: 
                radial-gradient(at 0% 0%, #1e3a8a 0%, transparent 50%),
                radial-gradient(at 100% 100%, #1e40af 0%, transparent 50%);
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--dashboard-text-primary);
        }
        
        /* Profile Container */
        .profile-container {
            max-width: 800px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }
        
        /* Profile Header */
        .profile-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: var(--dashboard-glass);
            backdrop-filter: blur(10px);
            border: 3px solid var(--brand-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-lg);
            font-size: 3rem;
            color: var(--brand-primary);
            transition: all 0.3s ease;
        }
        
        .profile-avatar:hover {
            transform: scale(1.05);
            border-color: var(--brand-accent);
        }
        
        .profile-name {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dashboard-text-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .profile-subtitle {
            font-size: 1.1rem;
            color: var(--dashboard-text-secondary);
            opacity: 0.8;
        }
        
        /* Profile Form */
        .profile-form {
            background: var(--dashboard-glass);
            backdrop-filter: blur(10px);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-glass);
            margin-bottom: var(--spacing-lg);
        }
        
        .form-section {
            margin-bottom: var(--spacing-xl);
        }
        
        .form-section:last-child {
            margin-bottom: 0;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dashboard-text-primary);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .section-icon {
            width: 20px;
            height: 20px;
            color: var(--brand-primary);
        }
        
        /* Form Fields */
        .form-group {
            margin-bottom: var(--spacing-lg);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: var(--dashboard-text-primary);
            margin-bottom: var(--spacing-sm);
            font-size: 0.95rem;
        }
        
        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-md);
            color: var(--dashboard-text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--brand-primary);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(var(--brand-primary-rgb), 0.1);
        }
        
        .form-input::placeholder {
            color: var(--dashboard-text-secondary);
            opacity: 0.7;
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }
        
        /* Form Row */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }
        }
        
        /* Action Buttons */
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-md);
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--dashboard-glass-border);
        }
        
        .btn {
            padding: var(--spacing-md) var(--spacing-xl);
            border: none;
            border-radius: var(--radius-md);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .btn-primary {
            background: var(--brand-primary);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--brand-accent);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(var(--brand-primary-rgb), 0.3);
        }
        
        .btn-secondary {
            background: transparent;
            color: var(--dashboard-text-secondary);
            border: 1px solid var(--dashboard-glass-border);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--dashboard-text-primary);
        }
        
        /* Status Indicators */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: var(--radius-full);
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .status-badge.active {
            background: rgba(34, 197, 94, 0.2);
            color: var(--status-success);
        }
        
        .status-badge.inactive {
            background: rgba(156, 163, 175, 0.2);
            color: var(--dashboard-text-secondary);
        }
        
        /* Profile Stats */
        .profile-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }
        
        .stat-card {
            background: var(--dashboard-glass);
            backdrop-filter: blur(10px);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--brand-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: var(--dashboard-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* Success/Error Messages */
        .message {
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .message-success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid var(--status-success);
            color: var(--status-success);
        }
        
        .message-error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--status-error);
            color: var(--status-error);
        }
        
        .message-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid var(--brand-primary);
            color: var(--brand-primary);
        }
        
        /* Loading States */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: var(--dashboard-glass);
            backdrop-filter: blur(10px);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            color: var(--dashboard-text-primary);
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--dashboard-glass-border);
            border-top: 3px solid var(--brand-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto var(--spacing-lg);
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .profile-container {
                padding: var(--spacing-md);
            }
            
            .profile-form {
                padding: var(--spacing-lg);
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .btn {
                justify-content: center;
            }
        }
    </style>
</head>