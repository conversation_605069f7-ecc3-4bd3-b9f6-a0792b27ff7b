from imports import *

from managers.manager_supervisors import <PERSON><PERSON><PERSON><PERSON><PERSON>, SupervisorSupervisor, SupervisorSupervisor_ChainOfThought, SupervisorTaskState
from tasks.processing.task_email_generator import create_supervisor_email_generator, email_generator_tool
from tasks.processing.task_email_sender import create_supervisor_email_sender, email_sender_tool, email_sender_direct_tool


async def create_supervisor_email_writer() -> SupervisorSupervisor:
    """
    Create the main email writer supervisor that coordinates both email generation and sending.
    This supervisor manages the complete email workflow:
    1. Email generation (content creation, validation, approval)
    2. Email sending (SMTP/API delivery with error handling)
    """
    class TaskCreator:
        email_generation_supervisor: SupervisorSupervisor = None
        email_sending_supervisor: SupervisorSupervisor = None

        async def create_tasks(self):
            # Create the email generation supervisor
            self.email_generation_supervisor = await create_supervisor_email_generator()
            
            # Create the email sending supervisor  
            self.email_sending_supervisor = await create_supervisor_email_sender()
        
        async def create_supervisor(self) -> SupervisorSupervisor:
            # Use Chain of Thought supervisor for better reasoning about email workflow
            return SupervisorManager.get_instance().register_supervisor(
                SupervisorSupervisor_ChainOfThought(
                    name="email_workflow_supervisor", 
                    prompt_id="Task_EmailWriter_Workflow_Supervisor"
                )
            ).add_task(task=self.email_generation_supervisor, priority=1) \
             .add_task(task=self.email_sending_supervisor, priority=2) \
             .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()


# Legacy support - create instances of the separated tools for backward compatibility
email_tool = email_generator_tool  # For backward compatibility with existing code
