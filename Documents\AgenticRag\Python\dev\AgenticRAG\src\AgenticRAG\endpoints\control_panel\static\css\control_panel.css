/* Control Panel CSS - Extracted from Dashboard_endpoint.py */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #000000;
    background-image: 
        radial-gradient(at 0% 0%, #1e3a8a 0%, transparent 50%),
        radial-gradient(at 100% 100%, #1e40af 0%, transparent 50%);
    min-height: 100vh;
    color: #f8fafc;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.header {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5);
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.header h1 {
    background: linear-gradient(135deg, #60a5fa, #a78bfa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: 700;
    display: inline-block;
    text-shadow: 0 0 30px rgba(96, 165, 250, 0.5);
}

.status-badge {
    display: inline-block;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-left: 1rem;
    text-transform: uppercase;
}

/* Dynamic status badge colors */
.status-badge[data-status="healthy"] { background: #10b981; }
.status-badge[data-status="degraded"] { background: #f59e0b; }
.status-badge[data-status="critical"] { background: #dc3545; }
.status-badge[data-status="unknown"] { background: #6c757d; }

.container {
    max-width: 1400px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.5);
    border: 1px solid rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 12px 32px rgba(59, 130, 246, 0.2);
}

.metric-title {
    font-size: 0.9rem;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #f8fafc;
    margin-bottom: 0.5rem;
}

.metric-subtitle {
    font-size: 0.85rem;
    color: #64748b;
}

.section {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.5);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.section h2 {
    color: #93c5fd;
    margin-bottom: 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.user-search {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.user-search input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    font-size: 1rem;
    background: rgba(15, 23, 42, 0.8);
    color: #f8fafc;
}

.user-search input:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.user-search button {
    padding: 0.75rem 2rem;
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

.user-search button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.4);
}

.requests-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 8px;
    overflow: hidden;
}

.requests-table th,
.requests-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    color: #e2e8f0;
}

.requests-table th {
    background: rgba(30, 41, 59, 0.8);
    font-weight: 600;
    color: #93c5fd;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.05em;
}

.status-active { color: #86efac; font-weight: 600; }
.status-paused { color: #fcd34d; font-weight: 600; }
.status-completed { color: #93c5fd; font-weight: 600; }
.status-failed { color: #fca5a5; font-weight: 600; }

.requests-table tr:hover {
    background: rgba(59, 130, 246, 0.05);
}

.btn-action {
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.system-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-item {
    padding: 0.75rem;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.stat-label {
    font-size: 0.75rem;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #f8fafc;
}

.performance-chart {
    width: 100%;
    height: 200px;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    padding: 1rem;
    margin-top: 1rem;
}

.no-requests {
    text-align: center;
    padding: 3rem;
    color: #94a3b8;
    font-style: italic;
}

.error-message {
    padding: 1rem;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    color: #fca5a5;
}

.success-message {
    padding: 1rem;
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 8px;
    color: #86efac;
}

.warning-message {
    padding: 1rem;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 8px;
    color: #fcd34d;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.btn-small {
    margin-left: 10px;
    padding: 2px 8px;
    font-size: 0.7rem;
    background: #4b5563;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-small:hover {
    background: #6b7280;
}

/* Call trace styles */
.call-trace {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.85rem;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    max-height: 150px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
}

.call-trace-item {
    margin: 0.25rem 0;
    padding-left: 1rem;
    border-left: 2px solid rgba(59, 130, 246, 0.3);
}

#autoRefreshContainer {
    display: inline-block;
    padding: 0.5rem;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 6px;
    font-size: 0.8rem;
}