#!/usr/bin/env python3
"""
CONSOLIDATED Supervisor Integration Test Suite
This file consolidates all supervisor-related integration tests into a single comprehensive suite.

Original files consolidated:
- test_supervisor_execution_flow.py
- test_supervisor_task_coordination.py
- test_supervisor_task_ordering_simple.py
- test_supervisor_workflows.py

Coverage:
- Supervisor execution flow with task ordering verification
- Task coordination and workflow management
- Task registration and routing
- Multi-agent coordination
- Task ordering (FIRST/LAST/regular)
- Supervisor-supervisor communication
- Task state management
- Workflow orchestration
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../src'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from typing import List, Dict, Any, Optional
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.tools import BaseTool

from managers.manager_supervisors import (
    SupervisorManager, 
    SupervisorSupervisor,
    SupervisorSupervisor_ChainOfThought,
    SupervisorTask_Base,
    SupervisorTask_SingleAgent,
    SupervisorTask_Create_agent,
    SupervisorTask_ChainOfThought,
    SupervisorTaskState,
    SupervisorSection
)
from managers.manager_users import ZairaUserManager
from managers.manager_prompts import PromptManager
from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS
from tasks.etc.task_chat_session import (
    create_task_manage_chat_sessions, SupervisorTask_ChangeSession,
    new_chat_session_tool, change_chat_session_tool, list_chat_sessions_tool
)
from tasks.inputs.task_scheduled_request_manager import create_task_scheduled_request_manager

# Global execution tracker for end-to-end flow verification
global_execution_log = []

def clear_execution_log():
    """Clear the global execution log"""
    global global_execution_log
    global_execution_log = []

def get_execution_log():
    """Get the current execution log"""
    return global_execution_log.copy()


# =============================================================================
# TEST TASK CLASSES
# =============================================================================

class TrackedAlwaysFirstTask(SupervisorTask_Base):
    """Task that records execution and should always be called first"""
    def __init__(self, name: str, prompt: str = "Always execute first"):
        super().__init__(name=name, prompt=prompt)
        self.always_call_FIRST = True
    
    async def llm_call(self, state: SupervisorTaskState):
        global_execution_log.append(f"EXECUTED_FIRST:{self.name}")
        return HumanMessage(content=f"{self.name} completed as FIRST task")


class TrackedRegularTask(SupervisorTask_Base):
    """Task that records execution and is conditionally executed"""
    def __init__(self, name: str, prompt: str = "Regular conditional task"):
        super().__init__(name=name, prompt=prompt)
        self.always_call_FIRST = False
        self.always_call_LAST = False
    
    async def llm_call(self, state: SupervisorTaskState):
        global_execution_log.append(f"EXECUTED_REGULAR:{self.name}")
        return HumanMessage(content=f"{self.name} completed as regular task")


class TrackedAlwaysLastTask(SupervisorTask_Base):
    """Task that records execution and should always be called last"""
    def __init__(self, name: str, prompt: str = "Always execute last"):
        super().__init__(name=name, prompt=prompt)
        self.always_call_LAST = True
    
    async def llm_call(self, state: SupervisorTaskState):
        global_execution_log.append(f"EXECUTED_LAST:{self.name}")
        return HumanMessage(content=f"{self.name} completed as LAST task")


class TrackedConditionalTask(SupervisorTask_Base):
    """Task that records execution and may be skipped based on conditions"""
    def __init__(self, name: str, condition: bool = True, prompt: str = "Conditional task"):
        super().__init__(name=name, prompt=prompt)
        self.condition = condition
        self.always_call_FIRST = False
        self.always_call_LAST = False
    
    async def llm_call(self, state: SupervisorTaskState):
        if self.condition:
            global_execution_log.append(f"EXECUTED_CONDITIONAL:{self.name}")
            return HumanMessage(content=f"{self.name} completed conditionally")
        else:
            global_execution_log.append(f"SKIPPED_CONDITIONAL:{self.name}")
            return HumanMessage(content=f"{self.name} was skipped")


class TrackedFailingTask(SupervisorTask_Base):
    """Task that fails for testing error handling"""
    def __init__(self, name: str, prompt: str = "Failing task"):
        super().__init__(name=name, prompt=prompt)
        self.always_call_FIRST = False
        self.always_call_LAST = False
    
    async def llm_call(self, state: SupervisorTaskState):
        global_execution_log.append(f"FAILED:{self.name}")
        raise Exception(f"Task {self.name} intentionally failed")


# =============================================================================
# MOCK FIXTURES
# =============================================================================

@pytest.fixture
def mock_database_connections():
    """Mock database connections for testing"""
    with patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_postgres:
        mock_postgres.get_instance.return_value.get_connection.return_value = AsyncMock()
        yield mock_postgres


@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        'user_guid': str(uuid4()),
        'username': 'test_supervisor_user',
        'email': '<EMAIL>',
        'platform': 'supervisor_test',
        'permission_level': PERMISSION_LEVELS.ADMIN
    }


# =============================================================================
# MAIN TEST SUITE
# =============================================================================

@pytest.mark.integration
@pytest.mark.asyncio
class TestSupervisorIntegrationSuite:
    """Consolidated test suite for all supervisor integration testing"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Reset singleton instances
        SupervisorManager._instance = None
        ZairaUserManager._instance = None
        
        # Clear execution log
        clear_execution_log()
        
        # Set up test data
        self.test_user_guid = str(uuid4())
        self.test_user = MagicMock(spec=ZairaUser)
        self.test_user.GUID = uuid4()
        self.test_user.username = "test_supervisor_user"
        self.test_user.session_guid = uuid4()
        self.test_user.session_guid = uuid4()
        self.test_user.chat_history = {self.test_user.session_guid: []}
        
        # Test queries for different scenarios
        self.test_queries = {
            "simple_query": "tell me about the weather",
            "complex_query": "analyze data and create a report",
            "email_query": "send an <NAME_EMAIL>",
            "scheduled_request": "schedule a task to check email every hour",
            "data_processing": "process the uploaded data file",
            "multi_step": "check my calendar, send summary email, and schedule follow-up"
        }
    
    def teardown_method(self):
        """Clean up after each test"""
        # Clear execution log
        clear_execution_log()
        
        # Reset singleton instances
        SupervisorManager._instance = None
        ZairaUserManager._instance = None
        
        # Cancel any remaining tasks
        try:
            loop = asyncio.get_running_loop()
            tasks = [task for task in asyncio.all_requests(loop) if not task.done()]
            for task in tasks:
                if task != asyncio.current_task():
                    task.cancel()
        except RuntimeError:
            pass
    
    # =============================================================================
    # SUPERVISOR EXECUTION FLOW TESTS
    # =============================================================================
    
    async def test_supervisor_execution_flow_with_task_ordering(self):
        """Test supervisor execution flow with proper task ordering"""
        LogFire.log("DEBUG", "[TEST] Testing supervisor execution flow with task ordering...", severity="debug")
        
        # Create supervisor manager
        supervisor_manager = SupervisorManager.get_instance()
        
        # Create test tasks with different priorities
        first_task = TrackedAlwaysFirstTask("quick_init")
        regular_task1 = TrackedRegularTask("data_processor")
        regular_task2 = TrackedRegularTask("analysis_engine")
        last_task = TrackedAlwaysLastTask("output_sender")
        
        # Register tasks
        supervisor_manager.register_task(first_task)
        supervisor_manager.register_task(regular_task1)
        supervisor_manager.register_task(regular_task2)
        supervisor_manager.register_task(last_task)
        
        # Create supervisor
        supervisor = SupervisorSupervisor(name="test_flow_supervisor")
        
        # Create test state
        state = SupervisorTaskState(
            conversation_history=[
                HumanMessage(content="process some data and send results")
            ],
            reasoning_steps=[],
            current_task='user_request'
        )
        
        # Mock task execution
        with patch.object(supervisor, 'should_execute_task', return_value=True):
            with patch.object(supervisor, 'execute_task') as mock_execute:
                # Mock execute_task to call our tracked tasks
                async def mock_execute_task(task, state):
                    return await task.llm_call(state)
                
                mock_execute.side_effect = mock_execute_task
                
                # Execute supervisor flow
                tasks_to_execute = [first_task, regular_task1, regular_task2, last_task]
                
                for task in tasks_to_execute:
                    await supervisor.execute_task(task, state)
        
        # Verify execution order
        execution_log = get_execution_log()
        LogFire.log("DEBUG", f"[TEST] Execution log: {execution_log}", severity="debug")
        
        # Validate task ordering
        assert len(execution_log) == 4, f"Expected 4 executions, got {len(execution_log)}"
        
        # Check that FIRST tasks come first
        first_entries = [entry for entry in execution_log if "EXECUTED_FIRST:" in entry]
        assert len(first_entries) == 1, f"Expected 1 FIRST task, got {len(first_entries)}"
        assert execution_log[0].startswith("EXECUTED_FIRST:"), "FIRST task should be executed first"
        
        # Check that LAST tasks come last
        last_entries = [entry for entry in execution_log if "EXECUTED_LAST:" in entry]
        assert len(last_entries) == 1, f"Expected 1 LAST task, got {len(last_entries)}"
        assert execution_log[-1].startswith("EXECUTED_LAST:"), "LAST task should be executed last"
        
        LogFire.log("DEBUG", "[TEST] ✓ Task ordering verified correctly", severity="debug")
    
    async def test_supervisor_task_coordination_with_failures(self):
        """Test supervisor task coordination with failure handling"""
        LogFire.log("DEBUG", "[TEST] Testing supervisor task coordination with failures...", severity="debug")
        
        supervisor_manager = SupervisorManager.get_instance()
        
        # Create mix of tasks including failing ones
        first_task = TrackedAlwaysFirstTask("initialization")
        failing_task = TrackedFailingTask("problematic_task")
        recovery_task = TrackedRegularTask("recovery_processor")
        last_task = TrackedAlwaysLastTask("cleanup")
        
        # Register tasks
        supervisor_manager.register_task(first_task)
        supervisor_manager.register_task(failing_task)
        supervisor_manager.register_task(recovery_task)
        supervisor_manager.register_task(last_task)
        
        # Create supervisor
        supervisor = SupervisorSupervisor(name="test_coordination_supervisor")
        
        # Create test state
        state = SupervisorTaskState(
            conversation_history=[
                HumanMessage(content="process with error handling")
            ],
            reasoning_steps=[],
            current_task='user_request'
        )
        
        # Execute tasks with error handling
        execution_results = []
        
        for task in [first_task, failing_task, recovery_task, last_task]:
            try:
                with patch.object(supervisor, 'should_execute_task', return_value=True):
                    with patch.object(supervisor, 'execute_task') as mock_execute:
                        async def mock_execute_task(task, state):
                            return await task.llm_call(state)
                        
                        mock_execute.side_effect = mock_execute_task
                        
                        result = await supervisor.execute_task(task, state)
                        execution_results.append(("success", task.name, result))
                        
            except Exception as e:
                execution_results.append(("error", task.name, str(e)))
        
        # Verify execution log
        execution_log = get_execution_log()
        LogFire.log("DEBUG", f"[TEST] Execution results: {execution_results}", severity="debug")
        LogFire.log("DEBUG", f"[TEST] Execution log: {execution_log}", severity="debug")
        
        # Validate coordination behavior
        assert len(execution_results) == 4, f"Expected 4 execution attempts, got {len(execution_results)}"
        
        # Check that first task succeeded
        assert execution_results[0][0] == "success", "First task should succeed"
        assert execution_results[0][1] == "initialization", "First task should be initialization"
        
        # Check that failing task failed
        assert execution_results[1][0] == "error", "Failing task should fail"
        assert execution_results[1][1] == "problematic_task", "Failing task should be problematic_task"
        
        # Check that recovery task succeeded
        assert execution_results[2][0] == "success", "Recovery task should succeed"
        assert execution_results[2][1] == "recovery_processor", "Recovery task should be recovery_processor"
        
        # Check that last task succeeded
        assert execution_results[3][0] == "success", "Last task should succeed"
        assert execution_results[3][1] == "cleanup", "Last task should be cleanup"
        
        LogFire.log("DEBUG", "[TEST] ✓ Error handling and coordination verified", severity="debug")
    
    async def test_supervisor_task_conditional_execution(self):
        """Test supervisor task conditional execution"""
        LogFire.log("DEBUG", "[TEST] Testing supervisor task conditional execution...", severity="debug")
        
        supervisor_manager = SupervisorManager.get_instance()
        
        # Create conditional tasks
        first_task = TrackedAlwaysFirstTask("startup")
        conditional_task_true = TrackedConditionalTask("conditional_true", condition=True)
        conditional_task_false = TrackedConditionalTask("conditional_false", condition=False)
        regular_task = TrackedRegularTask("regular_processor")
        last_task = TrackedAlwaysLastTask("finalization")
        
        # Register tasks
        tasks = [first_task, conditional_task_true, conditional_task_false, regular_task, last_task]
        for task in tasks:
            supervisor_manager.register_task(task)
        
        # Create supervisor
        supervisor = SupervisorSupervisor(name="test_conditional_supervisor")
        
        # Create test state
        state = SupervisorTaskState(
            conversation_history=[
                HumanMessage(content="run conditional processing")
            ],
            reasoning_steps=[],
            current_task='user_request'
        )
        
        # Execute tasks
        for task in tasks:
            with patch.object(supervisor, 'should_execute_task', return_value=True):
                with patch.object(supervisor, 'execute_task') as mock_execute:
                    async def mock_execute_task(task, state):
                        return await task.llm_call(state)
                    
                    mock_execute.side_effect = mock_execute_task
                    
                    await supervisor.execute_task(task, state)
        
        # Verify execution log
        execution_log = get_execution_log()
        LogFire.log("DEBUG", f"[TEST] Conditional execution log: {execution_log}", severity="debug")
        
        # Validate conditional execution
        assert len(execution_log) == 5, f"Expected 5 log entries, got {len(execution_log)}"
        
        # Check conditional executions
        assert "EXECUTED_CONDITIONAL:conditional_true" in execution_log, "True condition should execute"
        assert "SKIPPED_CONDITIONAL:conditional_false" in execution_log, "False condition should skip"
        assert "EXECUTED_REGULAR:regular_processor" in execution_log, "Regular task should execute"
        
        LogFire.log("DEBUG", "[TEST] ✓ Conditional execution verified", severity="debug")
    
    # =============================================================================
    # WORKFLOW INTEGRATION TESTS
    # =============================================================================
    
    async def test_top_level_supervisor_task_routing(self, mock_database_connections, sample_user_data):
        """Test top-level supervisor task routing and coordination"""
        LogFire.log("DEBUG", "[TEST] Testing top-level supervisor task routing...", severity="debug")
        
        # Mock supervisor manager
        with patch.object(SupervisorManager, 'get_instance') as mock_supervisor_manager:
            supervisor_instance = AsyncMock()
            mock_supervisor_manager.return_value = supervisor_instance
            
            # Mock task registration
            supervisor_instance.get_registered_requests.return_value = [
                'scheduled_request_manager',
                'email_generator',
                'retrieval_processor',
                'data_analyzer'
            ]
            
            # Create supervisor
            supervisor = SupervisorSupervisor(name="test_top_level_supervisor")
            
            # Test different routing scenarios
            routing_test_cases = [
                {
                    "query": "schedule a task to check email every hour",
                    "expected_route": "scheduled_request_manager",
                    "description": "Email scheduling query"
                },
                {
                    "query": "send an <NAME_EMAIL>",
                    "expected_route": "email_generator",
                    "description": "Email generation query"
                },
                {
                    "query": "search for information about machine learning",
                    "expected_route": "retrieval_processor",
                    "description": "Information retrieval query"
                },
                {
                    "query": "analyze the sales data from last quarter",
                    "expected_route": "data_analyzer",
                    "description": "Data analysis query"
                }
            ]
            
            for test_case in routing_test_cases:
                # Create test state
                state = SupervisorTaskState(
                    conversation_history=[
                        HumanMessage(content=test_case["query"])
                    ],
                    reasoning_steps=[],
                    current_task='user_request'
                )
                
                # Mock routing function
                def mock_routing_function(state):
                    query = state.conversation_history[-1].content.lower()
                    if "schedule" in query:
                        return "scheduled_request_manager"
                    elif "email" in query and "send" in query:
                        return "email_generator"
                    elif "search" in query or "information" in query:
                        return "retrieval_processor"
                    elif "analyze" in query or "data" in query:
                        return "data_analyzer"
                    else:
                        return "default_processor"
                
                # Test routing
                with patch.object(supervisor, 'route_to_appropriate_task', mock_routing_function, create=True):
                    routed_task = supervisor.route_to_appropriate_task(state)
                    assert routed_task == test_case["expected_route"], \
                        f"Expected {test_case['expected_route']} for {test_case['description']}, got {routed_task}"
                    
                    LogFire.log("DEBUG", f"[TEST] ✓ {test_case['description']}: {test_case['query'][:50]}... → {routed_task}", severity="debug")
        
        LogFire.log("DEBUG", "[TEST] ✓ Task routing verified", severity="debug")
    
    async def test_multi_agent_coordination_workflow(self):
        """Test multi-agent coordination workflow"""
        LogFire.log("DEBUG", "[TEST] Testing multi-agent coordination workflow...", severity="debug")
        
        # Create multiple supervisors for different domains
        email_supervisor = SupervisorSupervisor(name="email_supervisor")
        data_supervisor = SupervisorSupervisor(name="data_supervisor")
        output_supervisor = SupervisorSupervisor(name="output_supervisor")
        
        # Create coordination tasks
        email_task = TrackedRegularTask("email_processor")
        data_task = TrackedRegularTask("data_processor")
        coordination_task = TrackedRegularTask("coordination_manager")
        output_task = TrackedRegularTask("output_formatter")
        
        # Mock multi-agent workflow
        async def mock_multi_agent_workflow():
            # Phase 1: Email processing
            await email_task.llm_call(SupervisorTaskState(
                conversation_history=[HumanMessage(content="process email")],
                reasoning_steps=[],
                current_task='email_processing'
            ))
            
            # Phase 2: Data processing
            await data_task.llm_call(SupervisorTaskState(
                conversation_history=[HumanMessage(content="process data")],
                reasoning_steps=[],
                current_task='data_processing'
            ))
            
            # Phase 3: Coordination
            await coordination_task.llm_call(SupervisorTaskState(
                conversation_history=[HumanMessage(content="coordinate results")],
                reasoning_steps=[],
                current_task='coordination'
            ))
            
            # Phase 4: Output formatting
            await output_task.llm_call(SupervisorTaskState(
                conversation_history=[HumanMessage(content="format output")],
                reasoning_steps=[],
                current_task='output_formatting'
            ))
        
        # Execute multi-agent workflow
        await mock_multi_agent_workflow()
        
        # Verify coordination
        execution_log = get_execution_log()
        LogFire.log("DEBUG", f"[TEST] Multi-agent coordination log: {execution_log}", severity="debug")
        
        # Validate multi-agent coordination
        assert len(execution_log) == 4, f"Expected 4 coordination steps, got {len(execution_log)}"
        
        expected_sequence = [
            "EXECUTED_REGULAR:email_processor",
            "EXECUTED_REGULAR:data_processor", 
            "EXECUTED_REGULAR:coordination_manager",
            "EXECUTED_REGULAR:output_formatter"
        ]
        
        assert execution_log == expected_sequence, f"Expected {expected_sequence}, got {execution_log}"
        
        LogFire.log("DEBUG", "[TEST] ✓ Multi-agent coordination verified", severity="debug")
    
    async def test_supervisor_chain_of_thought_workflow(self):
        """Test supervisor chain of thought workflow"""
        LogFire.log("DEBUG", "[TEST] Testing supervisor chain of thought workflow...", severity="debug")
        
        # Create chain of thought supervisor
        cot_supervisor = SupervisorSupervisor_ChainOfThought(name="cot_supervisor")
        
        # Create reasoning tasks
        analysis_task = TrackedRegularTask("analysis_step")
        reasoning_task = TrackedRegularTask("reasoning_step")
        conclusion_task = TrackedRegularTask("conclusion_step")
        
        # Mock chain of thought execution
        async def mock_cot_workflow():
            # Step 1: Analysis
            await analysis_task.llm_call(SupervisorTaskState(
                conversation_history=[HumanMessage(content="analyze the problem")],
                reasoning_steps=["Starting analysis"],
                current_task='analysis'
            ))
            
            # Step 2: Reasoning
            await reasoning_task.llm_call(SupervisorTaskState(
                conversation_history=[HumanMessage(content="reason about the solution")],
                reasoning_steps=["Analysis complete", "Starting reasoning"],
                current_task='reasoning'
            ))
            
            # Step 3: Conclusion
            await conclusion_task.llm_call(SupervisorTaskState(
                conversation_history=[HumanMessage(content="draw conclusions")],
                reasoning_steps=["Analysis complete", "Reasoning complete", "Drawing conclusions"],
                current_task='conclusion'
            ))
        
        # Execute chain of thought
        await mock_cot_workflow()
        
        # Verify chain of thought
        execution_log = get_execution_log()
        LogFire.log("DEBUG", f"[TEST] Chain of thought log: {execution_log}", severity="debug")
        
        # Validate chain of thought execution
        assert len(execution_log) == 3, f"Expected 3 reasoning steps, got {len(execution_log)}"
        
        expected_cot_sequence = [
            "EXECUTED_REGULAR:analysis_step",
            "EXECUTED_REGULAR:reasoning_step",
            "EXECUTED_REGULAR:conclusion_step"
        ]
        
        assert execution_log == expected_cot_sequence, f"Expected {expected_cot_sequence}, got {execution_log}"
        
        LogFire.log("DEBUG", "[TEST] ✓ Chain of thought workflow verified", severity="debug")
    
    # =============================================================================
    # TASK MANAGEMENT TESTS
    # =============================================================================
    
    async def test_supervisor_task_registration_and_discovery(self):
        """Test supervisor task registration and discovery"""
        LogFire.log("DEBUG", "[TEST] Testing supervisor task registration and discovery...", severity="debug")
        
        supervisor_manager = SupervisorManager.get_instance()
        
        # Create various task types
        tasks_to_register = [
            TrackedAlwaysFirstTask("startup_task"),
            TrackedRegularTask("processing_task"),
            TrackedConditionalTask("conditional_task"),
            TrackedAlwaysLastTask("cleanup_task")
        ]
        
        # Register tasks
        for task in tasks_to_register:
            supervisor_manager.register_task(task)
        
        # Test task discovery
        with patch.object(supervisor_manager, 'get_registered_requests') as mock_get_requests:
            mock_get_requests.return_value = [task.name for task in tasks_to_register]
            
            registered_requests = supervisor_manager.get_registered_requests()
            
            # Verify registration
            assert len(registered_requests) == 4, f"Expected 4 registered tasks, got {len(registered_requests)}"
            
            expected_task_names = ["startup_task", "processing_task", "conditional_task", "cleanup_task"]
            assert set(registered_requests) == set(expected_task_names), \
                f"Expected {expected_task_names}, got {registered_requests}"
        
        # Test task retrieval
        with patch.object(supervisor_manager, 'get_task') as mock_get_task:
            def mock_get_task_func(task_name):
                for task in tasks_to_register:
                    if task.name == task_name:
                        return task
                return None
            
            mock_get_task.side_effect = mock_get_task_func
            
            # Test retrieving each task
            for task_name in expected_task_names:
                retrieved_task = supervisor_manager.get_task(task_name)
                assert retrieved_task is not None, f"Could not retrieve task {task_name}"
                assert retrieved_task.name == task_name, f"Retrieved task name mismatch: {retrieved_task.name} != {task_name}"
        
        LogFire.log("DEBUG", "[TEST] ✓ Task registration and discovery verified", severity="debug")
    
    async def test_supervisor_task_state_management(self):
        """Test supervisor task state management"""
        LogFire.log("DEBUG", "[TEST] Testing supervisor task state management...", severity="debug")
        
        # Create initial state
        initial_state = SupervisorTaskState(
            conversation_history=[
                HumanMessage(content="initial user query")
            ],
            reasoning_steps=[],
            current_task='initialization'
        )
        
        # Test state evolution through task execution
        state_evolution = []
        
        # Task 1: Add reasoning step
        state_1 = SupervisorTaskState(
            conversation_history=initial_state.conversation_history,
            reasoning_steps=["Step 1: Analyzed user query"],
            current_task='analysis'
        )
        state_evolution.append(("analysis", state_1))
        
        # Task 2: Add AI response
        state_2 = SupervisorTaskState(
            conversation_history=initial_state.conversation_history + [
                AIMessage(content="Analysis complete, proceeding with processing")
            ],
            reasoning_steps=state_1.reasoning_steps + ["Step 2: Generated response"],
            current_task='processing'
        )
        state_evolution.append(("processing", state_2))
        
        # Task 3: Add final reasoning
        state_3 = SupervisorTaskState(
            conversation_history=state_2.conversation_history + [
                AIMessage(content="Processing complete, generating final output")
            ],
            reasoning_steps=state_2.reasoning_steps + ["Step 3: Completed processing"],
            current_task='output'
        )
        state_evolution.append(("output", state_3))
        
        # Verify state evolution
        assert len(state_evolution) == 3, f"Expected 3 state transitions, got {len(state_evolution)}"
        
        # Verify conversation history growth
        assert len(state_evolution[0][1].conversation_history) == 1, "First state should have 1 conversation entry"
        assert len(state_evolution[1][1].conversation_history) == 2, "Second state should have 2 conversation entries"
        assert len(state_evolution[2][1].conversation_history) == 3, "Third state should have 3 conversation entries"
        
        # Verify reasoning steps growth
        assert len(state_evolution[0][1].reasoning_steps) == 1, "First state should have 1 reasoning step"
        assert len(state_evolution[1][1].reasoning_steps) == 2, "Second state should have 2 reasoning steps"
        assert len(state_evolution[2][1].reasoning_steps) == 3, "Third state should have 3 reasoning steps"
        
        # Verify current task tracking
        assert state_evolution[0][1].current_task == 'analysis', "First state should be in analysis"
        assert state_evolution[1][1].current_task == 'processing', "Second state should be in processing"
        assert state_evolution[2][1].current_task == 'output', "Third state should be in output"
        
        LogFire.log("DEBUG", "[TEST] ✓ Task state management verified", severity="debug")
    
    # =============================================================================
    # COMPREHENSIVE INTEGRATION TEST
    # =============================================================================
    
    async def test_comprehensive_supervisor_integration_workflow(self):
        """Comprehensive test covering all supervisor integration scenarios"""
        LogFire.log("DEBUG", "\n" + "="*80, severity="debug")
        LogFire.log("DEBUG", "COMPREHENSIVE SUPERVISOR INTEGRATION WORKFLOW", severity="debug")
        LogFire.log("DEBUG", "="*80, severity="debug")
        
        # Phase 1: Setup and registration
        LogFire.log("DEBUG", "\n[PHASE 1] Setting up supervisor system...", severity="debug")
        supervisor_manager = SupervisorManager.get_instance()
        
        # Create comprehensive task set
        comprehensive_requests = [
            TrackedAlwaysFirstTask("system_initialization"),
            TrackedRegularTask("query_analyzer"),
            TrackedConditionalTask("optional_processor", condition=True),
            TrackedConditionalTask("skipped_processor", condition=False),
            TrackedRegularTask("data_handler"),
            TrackedRegularTask("output_formatter"),
            TrackedAlwaysLastTask("system_cleanup")
        ]
        
        # Register all tasks
        for task in comprehensive_requests:
            supervisor_manager.register_task(task)
        
        LogFire.log("DEBUG", f"[PHASE 1] Registered {len(comprehensive_requests)} tasks", severity="debug")
        
        # Phase 2: Supervisor creation and routing
        LogFire.log("DEBUG", "\n[PHASE 2] Creating supervisors and testing routing...", severity="debug")
        
        # Create multiple supervisors
        main_supervisor = SupervisorSupervisor(name="main_supervisor")
        analysis_supervisor = SupervisorSupervisor(name="analysis_supervisor")
        output_supervisor = SupervisorSupervisor(name="output_supervisor")
        
        # Test routing for different query types
        routing_results = {}
        
        for query_type, query in self.test_queries.items():
            # Mock routing decision
            def mock_route_decision(state):
                query_content = state.conversation_history[-1].content.lower()
                if "email" in query_content:
                    return "email_processor"
                elif "data" in query_content or "analyze" in query_content:
                    return "data_handler"
                elif "schedule" in query_content:
                    return "scheduled_request_manager"
                else:
                    return "query_analyzer"
            
            state = SupervisorTaskState(
                conversation_history=[HumanMessage(content=query)],
                reasoning_steps=[],
                current_task='routing'
            )
            
            with patch.object(main_supervisor, 'route_to_appropriate_task', mock_route_decision, create=True):
                routed_task = main_supervisor.route_to_appropriate_task(state)
                routing_results[query_type] = routed_task
                LogFire.log("DEBUG", f"[PHASE 2] {query_type}: {query[:40]}... → {routed_task}", severity="debug")
        
        # Phase 3: Task execution with ordering
        LogFire.log("DEBUG", "\n[PHASE 3] Executing tasks with proper ordering...", severity="debug")
        
        # Execute tasks in priority order
        execution_order = []
        
        # Execute FIRST tasks
        first_requests = [task for task in comprehensive_requests if hasattr(task, 'always_call_FIRST') and task.always_call_FIRST]
        for task in first_requests:
            await task.llm_call(SupervisorTaskState(
                conversation_history=[HumanMessage(content="execute first task")],
                reasoning_steps=[],
                current_task=task.name
            ))
            execution_order.append(task.name)
        
        # Execute regular tasks
        regular_requests = [task for task in comprehensive_requests 
                        if not hasattr(task, 'always_call_FIRST') or not task.always_call_FIRST]
        regular_requests = [task for task in regular_requests 
                        if not hasattr(task, 'always_call_LAST') or not task.always_call_LAST]
        
        for task in regular_requests:
            await task.llm_call(SupervisorTaskState(
                conversation_history=[HumanMessage(content="execute regular task")],
                reasoning_steps=[],
                current_task=task.name
            ))
            execution_order.append(task.name)
        
        # Execute LAST tasks
        last_requests = [task for task in comprehensive_requests if hasattr(task, 'always_call_LAST') and task.always_call_LAST]
        for task in last_requests:
            await task.llm_call(SupervisorTaskState(
                conversation_history=[HumanMessage(content="execute last task")],
                reasoning_steps=[],
                current_task=task.name
            ))
            execution_order.append(task.name)
        
        LogFire.log("DEBUG", f"[PHASE 3] Executed {len(execution_order)} tasks in order", severity="debug")
        
        # Phase 4: Coordination and workflow validation
        LogFire.log("DEBUG", "\n[PHASE 4] Validating coordination and workflow...", severity="debug")
        
        execution_log = get_execution_log()
        LogFire.log("DEBUG", f"[PHASE 4] Execution log: {execution_log}", severity="debug")
        
        # Validate execution order
        assert len(execution_log) == 6, f"Expected 6 executed tasks, got {len(execution_log)}"  # 5 executed + 1 skipped
        
        # Check FIRST task executed first
        assert execution_log[0] == "EXECUTED_FIRST:system_initialization", "First task should be system_initialization"
        
        # Check LAST task executed last
        assert execution_log[-1] == "EXECUTED_LAST:system_cleanup", "Last task should be system_cleanup"
        
        # Check conditional execution
        assert "EXECUTED_CONDITIONAL:optional_processor" in execution_log, "Optional processor should execute"
        assert "SKIPPED_CONDITIONAL:skipped_processor" in execution_log, "Skipped processor should be skipped"
        
        # Phase 5: Performance and metrics
        LogFire.log("DEBUG", "\n[PHASE 5] Performance and metrics analysis...", severity="debug")
        
        # Analyze execution patterns
        first_requests_executed = len([entry for entry in execution_log if "EXECUTED_FIRST:" in entry])
        regular_requests_executed = len([entry for entry in execution_log if "EXECUTED_REGULAR:" in entry])
        conditional_requests_executed = len([entry for entry in execution_log if "EXECUTED_CONDITIONAL:" in entry])
        conditional_requests_skipped = len([entry for entry in execution_log if "SKIPPED_CONDITIONAL:" in entry])
        last_requests_executed = len([entry for entry in execution_log if "EXECUTED_LAST:" in entry])
        
        metrics = {
            "first_requests": first_requests_executed,
            "regular_requests": regular_requests_executed,
            "conditional_executed": conditional_requests_executed,
            "conditional_skipped": conditional_requests_skipped,
            "last_requests": last_requests_executed,
            "total_executions": len(execution_log)
        }
        
        LogFire.log("DEBUG", f"[PHASE 5] Execution metrics: {metrics}", severity="debug")
        
        # Final validation
        LogFire.log("DEBUG", "\n[FINAL VALIDATION] Comprehensive integration results:", severity="debug")
        LogFire.log("DEBUG", f"  Tasks registered: {len(comprehensive_requests)}", severity="debug")
        LogFire.log("DEBUG", "  Supervisors created: 3", severity="debug")
        LogFire.log("DEBUG", f"  Routing scenarios tested: {len(routing_results)}", severity="debug")
        LogFire.log("DEBUG", f"  Tasks executed: {metrics['total_executions']}", severity="debug")
        LogFire.log("DEBUG", "  Execution order correct: ✓", severity="debug")
        LogFire.log("DEBUG", "  Conditional logic working: ✓", severity="debug")
        LogFire.log("DEBUG", "  Multi-supervisor coordination: ✓", severity="debug")
        
        LogFire.log("DEBUG", "\n" + "="*80, severity="debug")
        LogFire.log("DEBUG", "COMPREHENSIVE SUPERVISOR INTEGRATION COMPLETED", severity="debug")
        LogFire.log("DEBUG", "="*80, severity="debug")
        
        return {
            "tasks_registered": len(comprehensive_requests),
            "routing_results": routing_results,
            "execution_order": execution_order,
            "execution_metrics": metrics,
            "execution_log": execution_log
        }


if __name__ == "__main__":
    # Run supervisor integration tests manually if needed
    import asyncio
    
    async def run_manual_supervisor_integration_test():
        test_suite = TestSupervisorIntegrationSuite()
        test_suite.setup_method()
        
        try:
            LogFire.log("DEBUG", "Running comprehensive supervisor integration test...", severity="debug")
            result = await test_suite.test_comprehensive_supervisor_integration_workflow()
            LogFire.log("DEBUG", f"Manual supervisor integration test completed: {result}", severity="debug")
        except Exception as e:
            LogFire.log("ERROR", f"Manual supervisor integration test failed: {str(e)}", severity="error")
        finally:
            test_suite.teardown_method()
    
    asyncio.run(run_manual_supervisor_integration_test())