// Dashboard Main JavaScript Functions
let currentView = 'user-list';
let selectedUserGuid = null;

// Browser compatibility check and fallbacks
const BrowserCompat = {
    // Check for modern JavaScript features
    features: {
        fetch: typeof fetch !== 'undefined',
        localStorage: (function() {
            try {
                return 'localStorage' in window && window.localStorage !== null;
            } catch (e) {
                return false;
            }
        })(),
        arrow_functions: (function() {
            try {
                eval('() => {}');
                return true;
            } catch (e) {
                return false;
            }
        })(),
        template_literals: (function() {
            try {
                eval('`template`');
                return true;
            } catch (e) {
                return false;
            }
        })(),
        const_let: (function() {
            try {
                eval('const test = 1; let test2 = 2;');
                return true;
            } catch (e) {
                return false;
            }
        })()
    },
    
    // Show compatibility warning
    showCompatibilityWarning: function() {
        const warningDiv = document.createElement('div');
        warningDiv.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: #dc3545;
            color: white;
            padding: 10px;
            text-align: center;
            z-index: 9999;
            font-family: Arial, sans-serif;
        `;
        warningDiv.innerHTML = `
            <strong>Browser Compatibility Warning:</strong> 
            Your browser may not support all dashboard features. 
            Please update to a modern browser for the best experience.
            <button onclick="this.parentElement.style.display='none'" style="background: none; border: 1px solid white; color: white; margin-left: 10px; padding: 2px 8px; cursor: pointer;">
                Dismiss
            </button>
        `;
        document.body.insertBefore(warningDiv, document.body.firstChild);
    },
    
    // Fetch fallback using XMLHttpRequest
    fetchFallback: function(url, options = {}) {
        return new Promise(function(resolve, reject) {
            var xhr = new XMLHttpRequest();
            xhr.open(options.method || 'GET', url);
            
            if (options.headers) {
                for (var key in options.headers) {
                    xhr.setRequestHeader(key, options.headers[key]);
                }
            }
            
            xhr.onload = function() {
                var response = {
                    status: xhr.status,
                    json: function() {
                        return Promise.resolve(JSON.parse(xhr.responseText));
                    }
                };
                resolve(response);
            };
            
            xhr.onerror = function() {
                reject(new Error('Network error'));
            };
            
            xhr.send(options.body);
        });
    },
    
    // Safe localStorage with fallback
    storage: {
        setItem: function(key, value) {
            try {
                if (BrowserCompat.features.localStorage) {
                    localStorage.setItem(key, value);
                } else {
                    // Fallback to in-memory storage
                    BrowserCompat._memoryStorage = BrowserCompat._memoryStorage || {};
                    BrowserCompat._memoryStorage[key] = value;
                }
            } catch (e) {
                console.warn('Could not save to storage:', e);
            }
        },
        
        getItem: function(key) {
            try {
                if (BrowserCompat.features.localStorage) {
                    return localStorage.getItem(key);
                } else {
                    return BrowserCompat._memoryStorage ? BrowserCompat._memoryStorage[key] : null;
                }
            } catch (e) {
                console.warn('Could not read from storage:', e);
                return null;
            }
        },
        
        removeItem: function(key) {
            try {
                if (BrowserCompat.features.localStorage) {
                    localStorage.removeItem(key);
                } else if (BrowserCompat._memoryStorage) {
                    delete BrowserCompat._memoryStorage[key];
                }
            } catch (e) {
                console.warn('Could not remove from storage:', e);
            }
        }
    },
    
    // Check compatibility and initialize fallbacks
    init: function() {
        var hasIssues = false;
        
        if (!this.features.fetch) {
            console.warn('Fetch API not supported, using XMLHttpRequest fallback');
            hasIssues = true;
        }
        
        if (!this.features.localStorage) {
            console.warn('localStorage not supported, using memory fallback');
            hasIssues = true;
        }
        
        if (!this.features.arrow_functions || !this.features.template_literals || !this.features.const_let) {
            console.warn('Modern JavaScript features not supported');
            hasIssues = true;
        }
        
        if (hasIssues) {
            this.showCompatibilityWarning();
        }
        
        return !hasIssues;
    }
};

// Safe fetch function that uses fallback
function safeFetch(url, options) {
    if (BrowserCompat.features.fetch) {
        return fetch(url, options);
    } else {
        return BrowserCompat.fetchFallback(url, options);
    }
}

// Session timeout handling
function handleSessionTimeout(data) {
    // Save current state before redirecting using safe storage
    BrowserCompat.storage.setItem('dashboard_state', JSON.stringify({
        currentView: currentView,
        selectedUserGuid: selectedUserGuid,
        timestamp: new Date().toISOString()
    }));
    
    // Show session expired message
    const body = document.body;
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        color: white;
        font-family: Arial, sans-serif;
    `;
    
    overlay.innerHTML = `
        <div style="background: #1e293b; padding: 2rem; border-radius: 8px; text-align: center; max-width: 400px;">
            <h2 style="color: #f59e0b; margin-bottom: 1rem;">Session Expired</h2>
            <p style="margin-bottom: 1.5rem;">${data.error || 'Your session has expired. Please refresh the page to continue.'}</p>
            <button onclick="window.location.reload()" style="background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 1rem;">
                Refresh Page
            </button>
        </div>
    `;
    
    body.appendChild(overlay);
}

// Check and restore dashboard state after refresh
function restoreDashboardState() {
    try {
        const savedState = BrowserCompat.storage.getItem('dashboard_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            const timeDiff = new Date() - new Date(state.timestamp);
            
            // Only restore if state is less than 5 minutes old
            if (timeDiff < 300000) {
                currentView = state.currentView;
                selectedUserGuid = state.selectedUserGuid;
                
                // Switch to saved view
                if (currentView && currentView !== 'user-list') {
                    showTab(currentView);
                }
                
                // Restore selected user if available
                if (selectedUserGuid) {
                    window.selectedUserGuid = selectedUserGuid;
                }
            }
            
            // Clear old state
            BrowserCompat.storage.removeItem('dashboard_state');
        }
    } catch (error) {
        console.log('Could not restore dashboard state:', error);
    }
}

// Tab functionality
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(tab => tab.classList.remove('active'));
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    
    // Show selected tab content
    document.getElementById(tabName).classList.add('active');
    
    // Add active class to clicked button
    event.target.classList.add('active');
    
    // Update current view for refresh purposes
    currentView = tabName;
}

// User search functionality
function filterUsers() {
    const searchTerm = document.getElementById('userSearchInput').value.toLowerCase();
    const userCards = document.querySelectorAll('.user-card');
    
    userCards.forEach(card => {
        const usernameElement = card.querySelector('.username');
        if (usernameElement) {
            const username = usernameElement.textContent.toLowerCase();
            if (username.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }
    });
}

// Load user list
async function loadUserList() {
    const container = document.getElementById('userListContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading users...</div>';
    
    try {
        const response = await safeFetch('/dashboard/api/user-list');
        const data = await response.json();
        
        // Handle session timeout
        if (response.status === 401 || data.error_type === 'session_timeout') {
            handleSessionTimeout(data);
            return;
        }
        
        // Handle rate limiting
        if (response.status === 429 || data.error_type === 'rate_limit') {
            const retryAfter = data.retry_after || 30;
            container.innerHTML = `
                <div class="error rate-limit-error">
                    <strong>Rate Limit Exceeded</strong><br>
                    ${data.error || 'Too many requests. Please wait before refreshing.'}<br>
                    <small>Please wait ${retryAfter} seconds before trying again.</small>
                    <button onclick="setTimeout(loadUserList, ${retryAfter * 1000})" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        Auto-retry in ${retryAfter}s
                    </button>
                </div>`;
            return;
        }
        
        if (data.error) {
            container.innerHTML = `<div class="error">Error: ${data.error}</div>`;
            return;
        }
        
        const users = data.users || [];
        if (users.length === 0) {
            container.innerHTML = '<div class="loading">No users found</div>';
            return;
        }
        
        let html = '';
        users.forEach(user => {
            html += `<div class="user-card" onclick="selectUser('${user.user_guid}', this)">
                <div style="font-weight: 600; margin-bottom: 0.25rem;">
                    <span class="username">${user.username || 'Unknown'}</span>
                    <span style="color: #60a5fa; font-size: 0.85rem; margin-left: 0.5rem;">${user.rank || 'USER'}</span>
                </div>
                <div style="font-size: 0.85rem; color: #94a3b8;">
                    GUID: ${user.user_guid}
                </div>
            </div>`;
        });
        
        // Add memory exhaustion warning if applicable
        if (data.is_truncated) {
            html += `
                <div class="memory-limit-warning" style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 1rem; margin: 1rem 0; border-radius: 4px;">
                    <h4 style="color: #92400e; margin: 0 0 0.5rem 0;">Large Dataset - Results Limited</h4>
                    <p style="color: #78350f; margin: 0; font-size: 0.9rem;">
                        ${data.truncated_message}
                        <br><small>Showing ${data.total_count} of ${data.actual_total} total users. Use search to find specific users.</small>
                    </p>
                </div>`;
        }
        
        container.innerHTML = html;
    } catch (error) {
        // Handle network errors that might indicate rate limiting
        if (error.message.includes('429') || error.message.toLowerCase().includes('rate limit')) {
            container.innerHTML = `
                <div class="error rate-limit-error">
                    <strong>Rate Limit Exceeded</strong><br>
                    Network request was rate limited. Please wait before trying again.<br>
                    <button onclick="setTimeout(loadUserList, 30000)" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        Retry in 30s
                    </button>
                </div>`;
        } else {
            container.innerHTML = `<div class="error">Failed to load users: ${error.message}</div>`;
        }
    }
}

// Select user
function selectUser(userGuid, element) {
    // Update visual selection
    document.querySelectorAll('.user-card').forEach(card => {
        card.classList.remove('selected');
    });
    element.classList.add('selected');
    
    // Store selected user
    window.selectedUserGuid = userGuid;
    selectedUserGuid = userGuid;
    
    // Load user's requests and chat history
    loadUserRequests(userGuid);
    loadUserChatHistory(userGuid);
}

// Load user requests
async function loadUserRequests(userGuid) {
    const container = document.getElementById('requestsContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading scheduled requests...</div>';
    
    try {
        const response = await fetch(`/dashboard/api/user-requests?user_guid=${encodeURIComponent(userGuid)}`);
        const data = await response.json();
        
        if (data.error) {
            container.innerHTML = `<div class="error">Error: ${data.error}</div>`;
            return;
        }
        
        const requests = data.requests || [];
        if (requests.length === 0) {
            container.innerHTML = '<div class="loading">No scheduled requests found for this user</div>';
            return;
        }
        
        let html = '<h3>Scheduled Requests</h3>';
        html += '<div style="display: grid; gap: 1rem;">';
        
        // Track requests with missing/incomplete data
        let incompleteRequests = 0;
        
        requests.forEach(request => {
            const statusColor = request.status === 'completed' ? '#10b981' : 
                              request.status === 'failed' ? '#ef4444' : '#f59e0b';
            
            // Check for missing critical data
            const hasIncompleteData = !request.scheduled_guid || !request.status || (!request.description && !request.target_prompt);
            if (hasIncompleteData) {
                incompleteRequests++;
            }
            
            // Style for incomplete data
            const cardStyle = hasIncompleteData ? 'opacity: 0.7; border-left: 3px solid #f59e0b;' : '';
            
            html += `<div class="user-card" style="${cardStyle}">
                <div style="display: flex; justify-content: space-between; align-items: start;">
                    <div style="flex: 1;">
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">
                            ${request.description || request.target_prompt || request.schedule_prompt || 'No description available'}
                            ${hasIncompleteData ? ' <span style="color: #f59e0b; font-size: 0.8rem;">[Incomplete Data]</span>' : ''}
                        </div>
                        <div style="font-size: 0.85rem; color: #94a3b8;">
                            ID: ${request.scheduled_guid || 'Unknown'}<br>
                            Created: ${request.created_at ? new Date(request.created_at).toLocaleString() : 'Unknown'}<br>
                            Next Run: ${request.next_run ? new Date(request.next_run).toLocaleString() : 'N/A'}<br>
                            ${request.schedule_type ? `Schedule: ${request.schedule_type}` : ''}
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <span style="display: inline-block; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem; font-weight: 600; background: ${statusColor}20; color: ${statusColor};">
                            ${(request.status || 'UNKNOWN').toUpperCase()}
                        </span>
                    </div>
                </div>
            </div>`;
        });
        
        html += '</div>';
        
        // Show warning for incomplete data
        if (incompleteRequests > 0) {
            html += `
                <div class="partial-data-warning" style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 1rem; margin: 1rem 0; border-radius: 4px;">
                    <h4 style="color: #92400e; margin: 0 0 0.5rem 0;">Partial Request Data</h4>
                    <p style="color: #78350f; margin: 0; font-size: 0.9rem;">
                        ${incompleteRequests} out of ${requests.length} requests have incomplete data due to loading issues.
                        <br><small>Some details may be missing. Try refreshing to load complete data.</small>
                    </p>
                    <button onclick="loadUserRequests('${userGuid}')" style="margin-top: 0.5rem; padding: 4px 8px; background: #f59e0b; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 0.8rem;">
                        Reload Requests
                    </button>
                </div>`;
        }
        
        container.innerHTML = html;
    } catch (error) {
        container.innerHTML = `<div class="error">Failed to load requests: ${error.message}</div>`;
    }
}

// Load user chat history
async function loadUserChatHistory(userGuid) {
    const container = document.getElementById('chatHistoryContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading chat history...</div>';
    
    try {
        const response = await fetch(`/dashboard/api/user-chat-history?user_guid=${encodeURIComponent(userGuid)}`);
        const data = await response.json();
        
        if (data.error) {
            container.innerHTML = `<div class="error">Error: ${data.error}</div>`;
            return;
        }
        
        const sessions = data.sessions_list || [];
        if (sessions.length === 0) {
            container.innerHTML = '<div class="loading">No chat history found for this user</div>';
            return;
        }
        
        let html = '<h3>Chat History</h3>';
        html += '<div style="display: grid; gap: 1rem;">';
        
        sessions.forEach(sessionId => {
            const messages = data.chat_sessions[sessionId] || [];
            const lastMessage = messages[messages.length - 1];
            
            html += `<div class="user-card" style="cursor: default;">
                <div style="font-weight: 600; margin-bottom: 0.5rem;">
                    Session: ${sessionId}
                </div>
                <div style="font-size: 0.85rem; color: #94a3b8;">
                    Messages: ${messages.length}<br>
                    Last Activity: ${lastMessage ? new Date(lastMessage.timestamp).toLocaleString() : 'Unknown'}
                </div>
            </div>`;
        });
        
        html += '</div>';
        
        // Add memory limitation warning if applicable
        if (data.is_truncated) {
            html += `
                <div class="memory-limit-warning" style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 1rem; margin: 1rem 0; border-radius: 4px;">
                    <h4 style="color: #92400e; margin: 0 0 0.5rem 0;">Chat History Limited</h4>
                    <p style="color: #78350f; margin: 0; font-size: 0.9rem;">
                        ${data.truncated_message}
                        <br><small>Memory limits applied: Showing ${data.truncated_info?.sessions_shown || 'recent'} sessions with ${data.truncated_info?.total_messages_shown || 'recent'} messages.</small>
                    </p>
                </div>`;
        }
        
        container.innerHTML = html;
    } catch (error) {
        container.innerHTML = `<div class="error">Failed to load chat history: ${error.message}</div>`;
    }
}

// Auto-refresh functionality
let autoRefreshInterval = null;
let autoRefreshEnabled = false;

function toggleAutoRefresh() {
    const toggleBtn = document.getElementById('refreshToggle');
    const timerElement = document.getElementById('refreshTimer');
    
    if (!toggleBtn || !timerElement) return;
    
    autoRefreshEnabled = !autoRefreshEnabled;
    
    if (autoRefreshEnabled) {
        toggleBtn.textContent = 'Disable';
        startAutoRefresh();
    } else {
        toggleBtn.textContent = 'Enable';
        stopAutoRefresh();
        timerElement.textContent = 'Auto-refresh: Disabled';
    }
}

function startAutoRefresh() {
    let countdown = 30;
    const timerElement = document.getElementById('refreshTimer');
    
    // Update timer display
    function updateTimer() {
        if (timerElement) {
            timerElement.textContent = `Auto-refresh: ${countdown}s`;
        }
        countdown--;
        
        if (countdown < 0) {
            // Refresh the page
            window.location.reload();
        }
    }
    
    // Update immediately and then every second
    updateTimer();
    autoRefreshInterval = setInterval(updateTimer, 1000);
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// System overview functionality
async function loadSystemOverview() {
    const container = document.getElementById('requestsContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading system overview...</div>';
    
    try {
        const response = await fetch('/dashboard/api/dashboard-overview');
        const data = await response.json();
        
        // Handle rate limiting
        if (response.status === 429 || data.error_type === 'rate_limit') {
            const retryAfter = data.retry_after || 30;
            let html = `
                <div class="error rate-limit-error">
                    <h3>System Overview - Rate Limited</h3>
                    <strong>Rate Limit Exceeded</strong><br>
                    ${data.error || 'Too many requests. Please wait before refreshing.'}<br>
                    <small>Showing cached data if available.</small>
                    <button onclick="setTimeout(loadSystemOverview, ${retryAfter * 1000})" style="margin: 10px 0; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        Auto-retry in ${retryAfter}s
                    </button>
                </div>`;
            
            // Show fallback data if available
            if (data.fallback_data) {
                html += '<div class="metrics-grid" style="opacity: 0.6;">';
                const fallback = data.fallback_data;
                
                if (fallback.system_health) {
                    html += `<div class="metric-card">
                        <div class="metric-title">System Status</div>
                        <div class="metric-value" style="color: #f59e0b;">${fallback.system_health.overall_status || 'Rate Limited'}</div>
                        <div class="metric-subtitle">Cached status</div>
                    </div>`;
                }
                
                if (fallback.factory_metrics) {
                    html += `<div class="metric-card">
                        <div class="metric-title">Active Managers</div>
                        <div class="metric-value">${fallback.factory_metrics.active_managers || 0}</div>
                        <div class="metric-subtitle">Last known count</div>
                    </div>`;
                }
                
                html += '</div>';
            }
            
            container.innerHTML = html;
            return;
        }
        
        if (data.error) {
            container.innerHTML = `<div class="error">Error: ${data.error}</div>`;
            return;
        }
        
        let html = '<h3>System Overview</h3>';
        
        // Track what data is missing for partial loading warning
        const missingData = [];
        let hasAnyData = false;
        
        html += '<div class="metrics-grid">';
        
        // System health metrics
        if (data.system_health) {
            hasAnyData = true;
            html += `<div class="metric-card">
                <div class="metric-title">Database Status</div>
                <div class="metric-value">${data.system_health.database_status || 'Unknown'}</div>
                <div class="metric-subtitle">PostgreSQL connection</div>
            </div>`;
            
            html += `<div class="metric-card">
                <div class="metric-title">Vector DB Status</div>
                <div class="metric-value">${data.system_health.vector_database_status || 'Unknown'}</div>
                <div class="metric-subtitle">Qdrant connection</div>
            </div>`;
            
            html += `<div class="metric-card">
                <div class="metric-title">Overall Status</div>
                <div class="metric-value">${data.system_health.overall_status || 'Unknown'}</div>
                <div class="metric-subtitle">System health</div>
            </div>`;
        } else {
            missingData.push('System Health');
            // Show placeholder for missing system health data
            html += `<div class="metric-card" style="opacity: 0.5; border: 2px dashed #f59e0b;">
                <div class="metric-title">Database Status</div>
                <div class="metric-value" style="color: #f59e0b;">Unavailable</div>
                <div class="metric-subtitle">Data loading failed</div>
            </div>`;
        }
        
        // Performance metrics
        if (data.performance_metrics) {
            hasAnyData = true;
            html += `<div class="metric-card">
                <div class="metric-title">Active Tasks</div>
                <div class="metric-value">${data.performance_metrics.active_tasks || 0}</div>
                <div class="metric-subtitle">Currently running</div>
            </div>`;
            
            html += `<div class="metric-card">
                <div class="metric-title">Total Requests</div>
                <div class="metric-value">${data.performance_metrics.total_requests || 0}</div>
                <div class="metric-subtitle">All-time requests</div>
            </div>`;
            
            html += `<div class="metric-card">
                <div class="metric-title">System Load</div>
                <div class="metric-value">${data.performance_metrics.system_load || 'Normal'}</div>
                <div class="metric-subtitle">Performance status</div>
            </div>`;
        } else {
            missingData.push('Performance Metrics');
            // Show placeholder for missing performance data
            html += `<div class="metric-card" style="opacity: 0.5; border: 2px dashed #f59e0b;">
                <div class="metric-title">Active Tasks</div>
                <div class="metric-value" style="color: #f59e0b;">?</div>
                <div class="metric-subtitle">Data loading failed</div>
            </div>`;
        }
        
        // Factory metrics (if available)
        if (data.factory_metrics) {
            hasAnyData = true;
            html += `<div class="metric-card">
                <div class="metric-title">Active Managers</div>
                <div class="metric-value">${data.factory_metrics.active_managers || 0}</div>
                <div class="metric-subtitle">User-specific managers</div>
            </div>`;
        } else {
            missingData.push('Factory Metrics');
        }
        
        html += '</div>';
        
        // Show partial data warning if some data is missing
        if (missingData.length > 0 && hasAnyData) {
            html += `
                <div class="partial-data-warning" style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 1rem; margin: 1rem 0; border-radius: 4px;">
                    <h4 style="color: #92400e; margin: 0 0 0.5rem 0;">Partial Data Loaded</h4>
                    <p style="color: #78350f; margin: 0; font-size: 0.9rem;">
                        Some data sections could not be loaded: ${missingData.join(', ')}
                        <br><small>This may be due to temporary service issues. Data will refresh automatically.</small>
                    </p>
                    <button onclick="loadSystemOverview()" style="margin-top: 0.5rem; padding: 4px 8px; background: #f59e0b; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 0.8rem;">
                        Retry Loading
                    </button>
                </div>`;
        }
        
        container.innerHTML = html;
    } catch (error) {
        // Handle network errors that might indicate rate limiting
        if (error.message.includes('429') || error.message.toLowerCase().includes('rate limit')) {
            container.innerHTML = `
                <div class="error rate-limit-error">
                    <h3>System Overview - Network Error</h3>
                    <strong>Rate Limit Exceeded</strong><br>
                    Network request was rate limited. Please wait before trying again.<br>
                    <button onclick="setTimeout(loadSystemOverview, 30000)" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        Retry in 30s
                    </button>
                </div>`;
        } else {
            container.innerHTML = `<div class="error">Failed to load system overview: ${error.message}</div>`;
        }
    }
}

// Manual load functions for individual tabs
function loadUserRequestsManual() {
    const userGuidInput = document.getElementById('userGuidInput');
    if (!userGuidInput) return;
    
    const userGuid = userGuidInput.value.trim();
    if (!userGuid) {
        alert('Please enter a user GUID');
        return;
    }
    
    loadUserRequests(userGuid);
}

function loadChatHistory() {
    const chatUserGuidInput = document.getElementById('chatUserGuidInput');
    if (!chatUserGuidInput) return;
    
    const userGuid = chatUserGuidInput.value.trim();
    if (!userGuid) {
        alert('Please enter a user GUID');
        return;
    }
    
    loadUserChatHistory(userGuid);
}

// Initialize dashboard on page load
function initializeDashboard() {
    // Initialize browser compatibility checks first
    BrowserCompat.init();
    
    // Set up any initial state
    console.log('Dashboard initialized');
    
    // Restore previous state if available
    restoreDashboardState();
    
    // Add event listeners for manual input buttons if they exist
    const loadRequestsBtn = document.querySelector('button[onclick="loadUserRequests()"]');
    if (loadRequestsBtn) {
        loadRequestsBtn.onclick = loadUserRequestsManual;
    }
    
    const loadChatBtn = document.querySelector('button[onclick="loadChatHistory()"]');
    if (loadChatBtn) {
        loadChatBtn.onclick = loadChatHistory;
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeDashboard);
} else {
    initializeDashboard();
}

// Expose functions globally
window.showTab = showTab;
window.filterUsers = filterUsers;
window.loadUserList = loadUserList;
window.selectUser = selectUser;
window.loadUserRequests = loadUserRequestsManual;
window.loadUserChatHistory = loadUserChatHistory;
window.loadChatHistory = loadChatHistory;
window.loadSystemOverview = loadSystemOverview;
window.toggleAutoRefresh = toggleAutoRefresh;