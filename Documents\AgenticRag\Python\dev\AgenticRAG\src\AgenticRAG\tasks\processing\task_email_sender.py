from imports import *

from langchain_core.tools import BaseTool
import logging
import smtplib
import base64
from typing import Optional
import json

from managers.manager_supervisors import Supervisor<PERSON>anager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaTask import LongRunningZairaTask


class EmailSenderTool(BaseTool):
    """Tool for sending emails using various SMTP providers"""
    name: str = "email_sender_tool"
    description: str = "Sends emails using SMTP with OAuth2 authentication for Gmail and other providers"
    
    def _run(self, email_data: str, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, email_data: str, state: SupervisorTaskState = None) -> str:
        """Sends an email using the provided email data"""
        try:
            # Parse email data (expecting J<PERSON><PERSON> from email generator)
            try:
                if isinstance(email_data, str):
                    email_info = json.loads(email_data)
                else:
                    email_info = email_data
            except json.JSONDecodeError:
                # Try to get email data from state if JSON parsing fails
                if hasattr(state, 'sections') and 'generated_email' in state.sections:
                    email_info = state.sections['generated_email']
                else:
                    return "Error: Invalid email data format. Expected JSON with email details."
            
            # Validate required fields
            required_fields = ['subject', 'content', 'sender', 'recipient']
            for field in required_fields:
                if field not in email_info:
                    return f"Error: Missing required field '{field}' in email data."
            
            subject = email_info['subject']
            content = email_info['content']
            sender = email_info['sender']
            recipient = email_info['recipient']
            
            # Check if email was approved (if using state-based approval)
            if hasattr(state, 'sections') and 'email_approved' in state.sections:
                if not state.sections['email_approved']:
                    return "Email sending cancelled - not approved by user."
            
            user = await ZairaUserManager.get_instance().find_user(state.user_guid)
            
            # Create EmailClient for sending operations
            class EmailClient:
                """Handles mail sending operations"""
                def __init__(self):
                    self.sender: str = sender
                    self.recipient: str = recipient
                    self.smtp_server: str = ""
                    self.smtp_port: int = 0
                    self.bearer_token: str = ""
                    self.refresh_token: str = ""

                async def send_email(self, subject: str, content: str) -> bool:
                    """Send an email to the specified recipient"""
                    try:
                        if not self.smtp_server or self.smtp_port == 0:
                            await user.my_task.send_response(
                                f"{self.sender}'s email provider is nog niet geïmplementeerd. "
                                f"Contacteer <EMAIL> met het verzoek om deze toe te voegen.", 
                                False
                            )
                            return False
                        
                        if self.smtp_server == "smtp.gmail.com":
                            return await self._send_via_gmail(subject, content)
                        else:
                            return await self._send_via_smtp(subject, content)
                            
                    except Exception as e:
                        print(f"Failed to send email: {e}")
                        from etc.helper_functions import exception_triggered
                        exception_triggered(e, "EmailClient.send_email", user.user_guid if user else None)
                        return False

                async def _send_via_gmail(self, subject: str, content: str) -> bool:
                    """Send email via Gmail API"""
                    if not self.bearer_token:
                        logging.error("Failed to obtain a valid Gmail token. Email not sent.")
                        return False
                    
                    from google.oauth2.credentials import Credentials
                    from googleapiclient.discovery import build
                    from email.message import EmailMessage

                    # Call the Gmail API
                    token_info = {
                        "client_id": OAuth2Verifier.get_instance().oauth_client_keys["google"][0],
                        "client_secret": OAuth2Verifier.get_instance().oauth_client_keys["google"][1],
                        "refresh_token": await OAuth2Verifier.get_instance().get_token(identifier="google", token_type="refresh_token"),
                        "token_uri": OAuth2Verifier.get_instance().oauth_auth_token_urls["google"][1],
                        "access_token": await OAuth2Verifier.get_instance().get_token(identifier="google"),
                        "expires_in": await OAuth2Verifier.get_instance().get_token(identifier="google", token_type="expires_in"),
                        "scopes": OAuth2Verifier.get_instance().apps["google"].scopes,
                    }
                    
                    service = build("gmail", "v1", credentials=Credentials.from_authorized_user_info(token_info))
                    
                    # Create message
                    message = EmailMessage()
                    message.set_content(content)
                    message['To'] = self.recipient
                    message['From'] = self.sender
                    message['Subject'] = subject

                    raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
                    gmail_message = {'raw': raw_message}
                    
                    sent_message = service.users().messages().send(userId="me", body=gmail_message).execute()
                    print(f"Gmail message sent. ID: {sent_message['id']}")
                    
                    await user.my_task.send_response("Mail is verzonden via Gmail.", False)
                    return True

                async def _send_via_smtp(self, subject: str, content: str) -> bool:
                    """Send email via SMTP"""
                    # Determine connection type based on port
                    if self.smtp_port == 465:
                        session = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
                    else:
                        session = smtplib.SMTP(self.smtp_server, self.smtp_port)
                        # Check if STARTTLS is supported
                        ehlo_response = session.ehlo()
                        if "starttls" in ehlo_response[1].decode().lower():
                            session.starttls()
                    
                    # Authenticate using username and password
                    session.login(self.sender, self.refresh_token)
                    
                    # Prepare and send the email
                    from email.mime.text import MIMEText
                    from email.mime.multipart import MIMEMultipart
                    
                    msg = MIMEMultipart()
                    msg['From'] = self.sender
                    msg['To'] = self.recipient
                    msg['Subject'] = subject
                    msg.attach(MIMEText(content, 'plain', 'utf-8'))
                    
                    session.sendmail(self.sender, self.recipient, msg.as_string())
                    session.quit()
                    
                    await user.my_task.send_response("Mail is verzonden via SMTP.", False)
                    return True

            # Initialize email client
            email_client = EmailClient()
            
            # Configure SMTP settings based on sender domain
            if "@gmail.com" in sender:
                email_client.smtp_server = "smtp.gmail.com"
                email_client.smtp_port = 587
                email_client.bearer_token = await OAuth2Verifier.get_instance().get_token("google")
                email_client.refresh_token = await OAuth2Verifier.get_instance().get_token("google", "refresh_token")
            else:
                # Use generic SMTP configuration from OAuth
                email_client.smtp_server = await OAuth2Verifier.get_instance().get_token("smtp", "access_token")
                email_client.smtp_port = int(await OAuth2Verifier.get_instance().get_token("smtp", "expires_in"))
                email_client.bearer_token = await OAuth2Verifier.get_instance().get_token("smtp", "refresh_token")
                email_client.refresh_token = await OAuth2Verifier.get_instance().get_token("smtp", "token_type")
            
            # Send the email
            success = await email_client.send_email(subject, content)
            
            if success:
                # Update state to indicate successful sending
                if hasattr(state, 'sections'):
                    state.sections['email_sent'] = True
                    state.sections['email_sent_at'] = datetime.now().isoformat()
                
                return f"Email successfully sent from {sender} to {recipient} with subject '{subject}'"
            else:
                return "Failed to send email. Please check configuration and try again."
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "email_sender_tool", state.user_guid if state else None)
            return f"Error sending email: {e}"


class EmailSenderDirectTool(BaseTool):
    """Tool for sending emails directly with provided parameters (bypass generation)"""
    name: str = "email_sender_direct_tool"
    description: str = "Sends emails directly with provided subject, content, sender, and recipient"
    
    def _run(self, subject: str, content: str, sender: str, recipient: str, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, subject: str, content: str, sender: str, recipient: str, state: SupervisorTaskState = None) -> str:
        """Sends an email directly with provided parameters"""
        # Create email data structure
        email_data = {
            "subject": subject,
            "content": content,
            "sender": sender,
            "recipient": recipient,
            "generated_at": datetime.now().isoformat(),
            "user_guid": state.user_guid if state else "unknown"
        }
        
        # Use the main email sender tool
        email_sender = EmailSenderTool()
        return await email_sender._arun(json.dumps(email_data), state)


# Create tool instances
email_sender_tool = EmailSenderTool()
email_sender_direct_tool = EmailSenderDirectTool()


async def create_supervisor_email_sender() -> SupervisorSupervisor:
    """Create supervisor for email sending tasks"""
    class TaskCreator:
        email_sending_task: SupervisorTask_SingleAgent = None
        email_direct_sending_task: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.email_sending_task = SupervisorManager.get_instance().register_task(
                SupervisorTask_SingleAgent(
                    name="email_sender_task", 
                    tools=[email_sender_tool], 
                    prompt_id="Task_EmailSender_Agent"
                )
            )
            
            self.email_direct_sending_task = SupervisorManager.get_instance().register_task(
                SupervisorTask_SingleAgent(
                    name="email_direct_sender_task", 
                    tools=[email_sender_direct_tool], 
                    prompt_id="Task_EmailSender_Direct_Agent"
                )
            )
        
        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.get_instance().register_supervisor(
                SupervisorSupervisor(
                    name="email_sending_supervisor", 
                    prompt_id="Task_EmailSender_Supervisor"
                )
            ).add_task(self.email_sending_task) \
             .add_task(self.email_direct_sending_task) \
             .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()