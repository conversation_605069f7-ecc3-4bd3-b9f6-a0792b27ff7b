# imports.py

# ------------------------------------------------
# -            Standard library imports          -
# ------------------------------------------------
from os import environ

# ------------------------------------------------
# -             Third-party imports              -
# ------------------------------------------------

# ------------------------------------------------
# - Local application / library specific imports -
# ------------------------------------------------
import etc.helper_functions

# ------------------------------------------------
# -     Common functions or configurations       -
# ------------------------------------------------
from globals import *
from config import *
from etc.ZairaSettings import *
from managers.manager_logfire import LogFire

# Set up API keys
environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
environ["TAVILY_API_KEY"] = '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'

environ["LANGSMITH_TRACING"] = "true"
environ["LANGSMITH_ENDPOINT"] = "https://api.smith.langchain.com"
environ["LANGSMITH_API_KEY"] = "***************************************************"
environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"




