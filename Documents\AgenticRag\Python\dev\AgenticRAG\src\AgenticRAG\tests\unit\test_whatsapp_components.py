"""
Unit tests for WhatsApp bot components
"""
import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.unit
@pytest.mark.asyncio
class TestWhatsAppBotCore:
    """Test core WhatsApp bot functionality"""
    
    def test_whatsapp_bot_singleton(self):
        """Test WhatsApp bot singleton pattern"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot1 = MyWhatsappBot()
        bot2 = MyWhatsappBot()
        
        # Should be the same instance
        assert bot1 is bot2
        assert MyWhatsappBot.get_instance() is bot1

    async def test_message_text_extraction(self):
        """Test message text extraction from webhook data"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Test data with text message
        webhook_data = {
            "entry": [{
                "changes": [{
                    "field": "messages",
                    "value": {
                        "messages": [{
                            "id": "test_id",
                            "from": "31611239487",
                            "text": {"body": "Hello world"}
                        }]
                    }
                }]
            }]
        }
        
        # Mock the on_message method to capture calls
        with patch.object(MyWhatsappBot, 'on_message') as mock_on_message:
            await MyWhatsappBot.process_webhook(webhook_data)
            
            # Verify on_message was called with correct parameters
            mock_on_message.assert_called_once_with(
                "Hello world", 
                "31611239487", 
                "31611239487"
            )

    async def test_empty_message_handling(self):
        """Test handling of empty or very short messages"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        with patch('managers.manager_users.ZairaUserManager') as mock_manager:
            # Test with empty message
            await MyWhatsappBot.on_message("", "31611239487", "31611239487")
            
            # Should not process empty messages
            mock_manager.get_user.assert_not_called()
            
            # Test with single character message
            await MyWhatsappBot.on_message("a", "31611239487", "31611239487")
            
            # Should not process very short messages
            mock_manager.get_user.assert_not_called()

    async def test_message_api_payload_construction(self, mock_whatsapp_api):
        """Test WhatsApp API payload construction"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        await MyWhatsappBot.send_a_whatsapp_message(
            to_number="31611239487",
            message="Test message with special chars: 🚀 & < >"
        )
        
        # Verify API call was made
        mock_whatsapp_api['client'].post.assert_called_once()
        
        # Check payload structure
        call_args = mock_whatsapp_api['client'].post.call_args
        payload = call_args[1]['json']
        
        assert payload['messaging_product'] == 'whatsapp'
        assert payload['recipient_type'] == 'individual'
        assert payload['to'] == '31611239487'
        assert payload['type'] == 'text'
        assert payload['text']['body'] == 'Test message with special chars: 🚀 & < >'

    async def test_api_headers_construction(self, mock_whatsapp_api):
        """Test WhatsApp API headers construction"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        await MyWhatsappBot.send_a_whatsapp_message("31611239487", "Test")
        
        # Verify headers
        call_args = mock_whatsapp_api['client'].post.call_args
        headers = call_args[1]['headers']
        
        assert headers['Content-Type'] == 'application/json'
        assert headers['Authorization'].startswith('Bearer ')
        assert len(headers['Authorization']) > 10  # Should have actual token

@pytest.mark.unit
@pytest.mark.asyncio
class TestWhatsAppWebhookHandling:
    """Test WhatsApp webhook handling components"""
    
    async def test_webhook_verification_valid_token(self):
        """Test webhook verification with valid token"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        
        # Mock request with valid verification
        mock_request = MagicMock()
        mock_request.remote = "127.0.0.1"
        mock_request.query_string = "hub.mode=subscribe&hub.verify_token=12346&hub.challenge=valid_challenge"
        
        response = await bot.whatsapp_verify(mock_request)
        
        assert response.status == 200
        assert response.text == "valid_challenge"

    async def test_webhook_verification_invalid_token(self):
        """Test webhook verification with invalid token"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        
        # Mock request with invalid verification
        mock_request = MagicMock()
        mock_request.remote = "127.0.0.1"
        mock_request.query_string = "hub.mode=subscribe&hub.verify_token=wrong_token&hub.challenge=challenge"
        
        response = await bot.whatsapp_verify(mock_request)
        
        assert response.status == 403

    async def test_webhook_verification_missing_params(self):
        """Test webhook verification with missing parameters"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        
        # Mock request with missing parameters
        mock_request = MagicMock()
        mock_request.remote = "127.0.0.1"
        mock_request.query_string = "hub.mode=subscribe"  # Missing token and challenge
        
        response = await bot.whatsapp_verify(mock_request)
        
        assert response.status == 403

    async def test_webhook_content_type_validation(self):
        """Test webhook content type validation"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        
        # Mock request with wrong content type
        mock_request = AsyncMock()
        mock_request.content_type = 'text/plain'
        
        response = await bot.whatsapp_webhook(mock_request)
        
        assert response.status == 415  # Unsupported Media Type

@pytest.mark.unit
@pytest.mark.asyncio
class TestWhatsAppMessageProcessing:
    """Test WhatsApp message processing components"""
    
    async def test_user_lookup_and_creation(self, sample_whatsapp_user_data):
        """Test user lookup and creation flow"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user, \
             patch.object(ZairaUserManager, 'add_user') as mock_add_user, \
             patch.object(ZairaUserManager, 'create_guid') as mock_create_guid:
            
            # Mock new user scenario
            mock_get_user.return_value = None
            mock_create_guid.return_value = "test-guid"
            
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_add_user.return_value = mock_user
            
            await MyWhatsappBot.on_message(
                "Hello bot", 
                "31611239487", 
                "31611239487"
            )
            
            # Verify user lookup
            mock_get_user.assert_called_once_with("31611239487")
            
            # Verify user creation
            mock_add_user.assert_called_once()
            
            # Verify message processing
            mock_user.on_message.assert_called_once()

    async def test_existing_user_message_processing(self, sample_whatsapp_user_data):
        """Test message processing for existing users"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            # Mock existing user
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            await MyWhatsappBot.on_message(
                "Follow up message", 
                "31611239487", 
                "31611239487"
            )
            
            # Verify user lookup
            mock_get_user.assert_called_once_with("31611239487")
            
            # Verify message processing
            mock_user.on_message.assert_called_once_with(
                complete_message="Follow up message",
                calling_bot=MyWhatsappBot.get_instance().bot_generic,
                attachments=[],
                original_message="31611239487"
            )

@pytest.mark.unit
@pytest.mark.asyncio
class TestWhatsAppErrorHandling:
    """Test WhatsApp bot error handling"""
    
    async def test_api_call_exception_handling(self):
        """Test handling of API call exceptions"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        with patch('httpx.AsyncClient') as mock_client:
            # Mock client that raises exception
            mock_client.side_effect = Exception("Network error")
            
            result = await MyWhatsappBot.send_a_whatsapp_message(
                "31611239487", 
                "Test message"
            )
            
            # Should return False on exception
            assert result is False

    async def test_empty_message_sending(self):
        """Test sending empty messages"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Test with empty message
        result = await MyWhatsappBot.send_a_whatsapp_message("31611239487", "")
        assert result is False
        
        # Test with None message
        result = await MyWhatsappBot.send_a_whatsapp_message("31611239487", None)
        assert result is False

    async def test_missing_configuration_handling(self):
        """Test handling of missing WhatsApp configuration"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Mock missing phone number ID
        with patch('endpoints.whatsapp_endpoint.WHATSAPP_PHONE_NUMBER_ID', None):
            result = await MyWhatsappBot.send_a_whatsapp_message(
                "31611239487", 
                "Test message"
            )
            
            # Should return False when configuration is missing
            assert result is False

@pytest.mark.unit
class TestWhatsAppBotGeneric:
    """Test WhatsApp bot generic interface"""
    
    def test_bot_generic_initialization(self):
        """Test MyBot_Generic initialization for WhatsApp"""
        from endpoints.mybot_generic import MyBot_Generic
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        bot_generic = MyBot_Generic(bot, "Whatsapp")
        
        assert bot_generic.name == "Whatsapp"
        assert bot_generic.parent_instance is bot

    async def test_message_splitting_for_whatsapp(self):
        """Test message splitting for WhatsApp character limits"""
        from endpoints.mybot_generic import MyBot_Generic
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Create a long message that exceeds WhatsApp limits
        long_message = "A" * 1500  # Longer than 1000 char limit
        
        bot = MyWhatsappBot()
        bot_generic = MyBot_Generic(bot, "Whatsapp")
        
        with patch.object(MyWhatsappBot, 'send_a_whatsapp_message') as mock_send:
            mock_send.return_value = True
            
            await bot_generic.send_response(
                text=long_message,
                physical_message="31611239487",
                add_to_chat_history=False
            )
            
            # Should be called multiple times due to message splitting
            assert mock_send.call_count > 1
            
            # Each call should be within limits
            for call in mock_send.call_args_list:
                message_text = call[0][1]  # Second argument is the message
                assert len(message_text) <= 1000
