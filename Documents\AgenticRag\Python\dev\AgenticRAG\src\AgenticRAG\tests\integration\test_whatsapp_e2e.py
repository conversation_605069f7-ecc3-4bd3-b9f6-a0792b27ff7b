"""
End-to-end tests for WhatsApp bot functionality
"""
import pytest
import asyncio
import json
import time
from unittest.mock import AsyncMock, patch, MagicMock
from aiohttp import web, ClientSession
from imports import *

@pytest.mark.e2e
@pytest.mark.asyncio
class TestWhatsAppEndToEnd:
    """End-to-end tests for complete WhatsApp workflows"""
    
    @pytest.fixture
    async def mock_whatsapp_server(self):
        """Start mock WhatsApp server for E2E testing"""
        from tests.mock_whatsapp_server import MockWhatsAppServer
        
        server = MockWhatsAppServer(port=8081)
        runner = await server.start()
        
        # Configure bot to use mock server
        with patch('endpoints.whatsapp_endpoint.WHATSAPP_PHONE_NUMBER_ID', '***************'), \
             patch('endpoints.whatsapp_endpoint.WHATSAPP_ACCESS_TOKEN', 'valid_token_123'):
            
            # Patch the API URL to point to mock server
            original_url = "https://graph.facebook.com/v18.0"
            mock_url = "http://localhost:8081/v18.0"
            
            with patch('endpoints.whatsapp_endpoint.MyWhatsappBot.send_a_whatsapp_message') as mock_send:
                async def mock_send_message(to_number: str, message: str):
                    async with ClientSession() as session:
                        url = f"{mock_url}/***************/messages"
                        payload = {
                            "messaging_product": "whatsapp",
                            "to": to_number,
                            "type": "text",
                            "text": {"body": message}
                        }
                        headers = {
                            "Content-Type": "application/json",
                            "Authorization": "Bearer valid_token_123"
                        }
                        async with session.post(url, json=payload, headers=headers) as response:
                            return response.status == 200
                
                mock_send.side_effect = mock_send_message
                
                yield server
        
        await server.stop(runner)
    
    async def test_complete_message_flow(self, mock_whatsapp_server, mock_external_services):
        """Test complete message flow from webhook to response"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user, \
             patch.object(ZairaUserManager, 'add_user') as mock_add_user, \
             patch.object(ZairaUserManager, 'create_guid') as mock_create_guid:
            
            # Mock user creation
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_user.sessionGUID = "test-session"
            mock_user.conversationGUID = "test-conversation"
            mock_user.chat_history = {"test-session": []}
            
            mock_get_user.return_value = None  # New user
            mock_add_user.return_value = mock_user
            mock_create_guid.return_value = "test-guid"
            
            # Initialize bot
            bot = MyWhatsappBot()
            await MyWhatsappBot.setup()
            
            # Simulate incoming webhook
            webhook_data = {
                "object": "whatsapp_business_account",
                "entry": [{
                    "id": "mock_business_account",
                    "changes": [{
                        "value": {
                            "messaging_product": "whatsapp",
                            "metadata": {
                                "display_phone_number": "***********",
                                "phone_number_id": "***************"
                            },
                            "contacts": [{
                                "profile": {"name": "Test User"},
                                "wa_id": "***********"
                            }],
                            "messages": [{
                                "from": "***********",
                                "id": "wamid.test123",
                                "timestamp": str(int(time.time())),
                                "text": {"body": "Hello, what can you help me with?"},
                                "type": "text"
                            }]
                        },
                        "field": "messages"
                    }]
                }]
            }
            
            # Process webhook
            await MyWhatsappBot.process_webhook(webhook_data)
            
            # Allow async processing
            await asyncio.sleep(0.2)
            
            # Verify user was created
            mock_add_user.assert_called_once()
            
            # Verify message was processed
            mock_user.on_message.assert_called_once()
            call_args = mock_user.on_message.call_args
            assert call_args[1]['complete_message'] == "Hello, what can you help me with?"
            
            # Check messages sent to mock server
            async with ClientSession() as session:
                async with session.get("http://localhost:8081/test/messages") as response:
                    data = await response.json()
                    # Should have at least one message sent (bot response)
                    assert len(data['messages']) >= 0  # May be 0 if user.on_message doesn't send response

    async def test_rag_query_workflow(self, mock_whatsapp_server, mock_external_services, mock_rag_components):
        """Test complete RAG query workflow through WhatsApp"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        from endpoints.mybot_generic import MyBot_Generic
        
        # Setup mock user with RAG capabilities
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            mock_user.sessionGUID = "test-session"
            mock_user.conversationGUID = "test-conversation"
            mock_user.chat_history = {"test-session": []}
            mock_user.my_task = AsyncMock()
            mock_user.my_task.calling_bot = MyBot_Generic(None, "Whatsapp")
            mock_user.my_task.send_response = AsyncMock()
            
            # Mock user.on_message to simulate RAG processing
            async def mock_on_message(complete_message, calling_bot, attachments, original_message):
                # Simulate RAG processing and response
                await calling_bot.send_response(
                    text="Based on my knowledge, I can help you with various tasks including document analysis, scheduling, and answering questions.",
                    physical_message=original_message,
                    add_to_chat_history=True
                )
            
            mock_user.on_message = mock_on_message
            mock_get_user.return_value = mock_user
            
            # Initialize bot
            bot = MyWhatsappBot()
            await MyWhatsappBot.setup()
            
            # Simulate RAG query
            await MyWhatsappBot.on_message(
                "What can you help me with?",
                "***********",
                "***********"
            )
            
            # Allow processing time
            await asyncio.sleep(0.2)
            
            # Verify RAG components were used
            assert mock_rag_components.retrieve_similar.called
            
            # Check response was sent
            async with ClientSession() as session:
                async with session.get("http://localhost:8081/test/messages") as response:
                    data = await response.json()
                    assert len(data['messages']) > 0
                    
                    # Verify response content
                    last_message = data['messages'][-1]
                    assert "help you with" in last_message['data']['text']['body']

    async def test_error_recovery_workflow(self, mock_whatsapp_server, mock_external_services):
        """Test error recovery in end-to-end workflow"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup failing user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user, \
             patch.object(ZairaUserManager, 'add_user') as mock_add_user:
            
            # First call fails, second succeeds
            mock_get_user.side_effect = [Exception("Database error"), None]
            
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_add_user.return_value = mock_user
            
            # Initialize bot
            bot = MyWhatsappBot()
            
            # First message should fail gracefully
            await MyWhatsappBot.on_message(
                "First message",
                "***********",
                "***********"
            )
            
            # Second message should succeed
            await MyWhatsappBot.on_message(
                "Second message",
                "***********", 
                "***********"
            )
            
            # Verify error was handled gracefully
            assert mock_get_user.call_count == 2

    async def test_concurrent_message_handling(self, mock_whatsapp_server, mock_external_services):
        """Test handling of concurrent messages"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user, \
             patch.object(ZairaUserManager, 'add_user') as mock_add_user:
            
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            # Initialize bot
            bot = MyWhatsappBot()
            
            # Send multiple concurrent messages
            tasks = []
            for i in range(5):
                task = asyncio.create_task(
                    MyWhatsappBot.on_message(
                        f"Concurrent message {i}",
                        "***********",
                        "***********"
                    )
                )
                tasks.append(task)
            
            # Wait for all to complete
            await asyncio.gather(*tasks)
            
            # Verify all messages were processed
            assert mock_user.on_message.call_count == 5

@pytest.mark.e2e
@pytest.mark.asyncio
class TestWhatsAppWebhookEndToEnd:
    """End-to-end tests for webhook handling"""
    
    async def test_webhook_verification_e2e(self, mock_external_services):
        """Test complete webhook verification flow"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from endpoints.api_endpoint import APIEndpoint
        
        # Mock API endpoint
        with patch.object(APIEndpoint, 'get_instance') as mock_api:
            mock_app = MagicMock()
            mock_api_instance = AsyncMock()
            mock_api_instance.aio_app = mock_app
            mock_api.return_value = mock_api_instance
            
            # Initialize bot
            bot = MyWhatsappBot()
            await MyWhatsappBot.setup()
            
            # Test verification request
            mock_request = MagicMock()
            mock_request.remote = "127.0.0.1"
            mock_request.query_string = "hub.mode=subscribe&hub.verify_token=12346&hub.challenge=verification_challenge"
            
            response = await bot.whatsapp_verify(mock_request)
            
            assert response.status == 200
            assert response.text == "verification_challenge"

    async def test_webhook_message_processing_e2e(self, mock_external_services, sample_whatsapp_webhook_data):
        """Test complete webhook message processing"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user, \
             patch.object(ZairaUserManager, 'add_user') as mock_add_user:
            
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = None
            mock_add_user.return_value = mock_user
            
            # Initialize bot
            bot = MyWhatsappBot()
            
            # Mock webhook request
            mock_request = AsyncMock()
            mock_request.content_type = 'application/json'
            mock_request.json.return_value = sample_whatsapp_webhook_data
            
            # Process webhook
            response = await bot.whatsapp_webhook(mock_request)
            
            # Verify response
            assert response.status == 200
            
            # Allow async processing
            await asyncio.sleep(0.1)
            
            # Verify user creation and message processing
            mock_add_user.assert_called_once()
            mock_user.on_message.assert_called_once()

@pytest.mark.e2e
@pytest.mark.asyncio
class TestWhatsAppPerformanceE2E:
    """End-to-end performance tests"""
    
    async def test_message_throughput(self, mock_external_services):
        """Test message processing throughput"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup fast mock user
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            # Initialize bot
            bot = MyWhatsappBot()
            
            # Measure processing time for multiple messages
            start_time = time.time()
            
            tasks = []
            for i in range(10):
                task = asyncio.create_task(
                    MyWhatsappBot.on_message(
                        f"Performance test message {i}",
                        f"3161123948{i}",
                        f"3161123948{i}"
                    )
                )
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should process 10 messages in reasonable time (< 2 seconds)
            assert processing_time < 2.0
            assert mock_user.on_message.call_count == 10

    async def test_memory_usage_stability(self, mock_external_services):
        """Test memory usage remains stable during processing"""
        import psutil
        import os
        
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            # Initialize bot
            bot = MyWhatsappBot()
            
            # Measure initial memory
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss
            
            # Process many messages
            for i in range(50):
                await MyWhatsappBot.on_message(
                    f"Memory test message {i}",
                    f"31611239{i:03d}",
                    f"31611239{i:03d}"
                )
            
            # Measure final memory
            final_memory = process.memory_info().rss
            memory_increase = final_memory - initial_memory
            
            # Memory increase should be reasonable (< 50MB)
            assert memory_increase < 50 * 1024 * 1024
