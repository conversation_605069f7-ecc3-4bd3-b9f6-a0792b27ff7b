from imports import *

from os import path as os_path
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from langchain_core.messages import  HumanMessage, SystemMessage
from managers.manager_users import ZairaUserManager
from managers.manager_prompts import PromptManager

class SupervisorTask_Quick_RAG(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)

        #Perform RAG search using the provided query engine
        try:
            chat_session = await self.get_chat_session_from_state(state)
            LogFire.log("DEBUG", f"Starting retrieve_data for: {input_message}", chat=chat_session, severity="debug")
            # filters = MetadataFilters(filters=[])
            # # Configure the query engine with advanced settings
            # configured_engine = query_engine.as_query_engine(
            #     filters=filters,
            #     similarity_top_k=10,
            #     sparse_top_k=10,
            #     vector_store_query_mode="hybrid",
            #     node_postprocessors=[
            #         LLMRerank(top_n=5)
            #     ]
            # )
            # Use aquery for async operation
            #response = await configured_engine.aquery(input_message)
            LogFire.log("DEBUG", "About to call query_engine.aquery...", chat=chat_session, severity="debug")
            chat_session = user.chat_history.get(user.session_guid) if user else None
            input = ""
            if chat_session:
                for chat in chat_session.messages:
                    input += f"{chat.role}: {chat.content}\n"
            input += f"user: {input_message}\nassistant:"
            response = await Globals.get_query_engine_default().aquery(f"{PromptManager.get_prompt('AskZaira_Prompt')}. Prompt: {PromptManager.get_prompt('Quick_RAG_Search')}. User query: {input}")
            LogFire.log("DEBUG", f"Query completed, response: {response}", chat=chat_session, severity="debug")
            ragdata = f"{response}"
        except Exception as e:
            LogFire.log("ERROR", f"Error in retrieve_data: {e}", chat=chat_session, severity="error")
            etc.helper_functions.exception_triggered(e)
            ragdata = f"An error occurred: {e}"
        if Globals.is_debug_values():
            LogFire.log("TASK", "", "RAGed: " + ragdata)
        return ragdata

class SupervisorTask_Quick_LLM(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)
        llmdata = ""

        #Perform generic search using the provided query engine
        try:
            
            chat_history = user.get_chat_history() if user else []
            messages = chat_history
            messages.insert(0, SystemMessage(PromptManager.get_prompt("AskZaira_Prompt") + ". Prompt: " + PromptManager.get_prompt("Quick_LLM_Search")))
            messages.append(HumanMessage(input_message))
            result = await ZairaSettings.llm.ainvoke(
                input=messages
            )
            if isinstance(result, etc.helper_functions.get_any_message_as_type()):
                result = result.content
            llmdata = result#Command(update={"messages":result})
        except Exception as e:
            etc.helper_functions.exception_triggered(e)
        if not llmdata:
            chat_session = await self.get_chat_session_from_state(state)
            LogFire.log("DEBUG", "No relevant data found for your query.", chat=chat_session, severity="debug")
        if Globals.is_debug_values():
            LogFire.log("TASK", "", "LLMed: " + llmdata)
        return llmdata

class SupervisorTask_Quick_Complexity(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)
        complexity_score = 1
        if Globals.is_debug_values():
            LogFire.log("TASK", "", "Compl: " + str(complexity_score))
        return ""

async def create_task_quick_rag_search() -> SupervisorTask_Base:
    ret_val = SupervisorManager.register_task(SupervisorTask_Quick_RAG(name="quick_rag_task", prompt_id="Quick_RAG_Search"))
    ret_val.always_call_FIRST = True
    return ret_val

async def create_task_quick_llm_search() -> SupervisorTask_Base:
    ret_val = SupervisorManager.register_task(SupervisorTask_Quick_LLM(name="quick_llm_task", prompt_id="Quick_LLM_Search"))
    
    ret_val.always_call_FIRST = True
    return ret_val

async def create_task_quick_complexity_search() -> SupervisorTask_Base:
    ret_val = SupervisorManager.register_task(SupervisorTask_Quick_Complexity(name="quick_complexity_task", prompt="Do nothing"))
    
    ret_val.always_call_FIRST = True
    return ret_val
