{"permissions": {"allow": ["Bash(rm:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskManager::test_llm_call_structure -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskManager -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskManager::test_llm_call_error_handling -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskTools::test_create_scheduled_task_tool -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskTasks -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskPersistenceIntegration -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskManagerIntegration -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskTools::test_create_scheduled_task_tool_user_not_found -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_comprehensive.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_persistence.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_rag_system.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_database_operations.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/ --tb=no -q --maxfail=5)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_scheduled_task_manager_integration.py::TestScheduledTaskManagerIntegration::test_create_task_workflow -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --cov=. --cov-report=term-missing)", "Bash(../../.venv/Scripts/pip list)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_chat_session.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_chat_session_simple.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --cov=. --cov-report=term-missing --cov-report=html)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message_updated.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message_updated.py -v --tb=short)", "Bash(../../.venv/Scripts/python.exe:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --collect-only -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ -x --tb=short -q)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestDevRun::test_main_function_debug_mode -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --continue-on-collection-errors -x --tb=short -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestMain::test_main_func_claude_environment -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestMain::test_main_func_docker_environment -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestMain::test_main_func_docker_no_dev_env -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestMainEdgeCases::test_main_func_query_engine_in_production -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --collect-only)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --maxfail=5 -x --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py --cov=main --cov=dev_run --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message.py --cov=userprofiles.ZairaMessage --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message.py::TestZairaMessageValidation --cov=userprofiles.ZairaMessage --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message.py::TestZairaMessage::test_to_langchain_unknown_role_fallback -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_enum_field_validators_with_non_string_input --cov=userprofiles.ZairaMessage --cov-report=term-missing)", "Bash(find:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestMain::test_environment_variables_set -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message.py --tb=line -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py --tb=line -q)", "Bash(../../.venv/Scripts/pytest.exe --cov=main --cov-report=term-missing tests/unit/test_main_dev_run.py -q)", "Bash(../../.venv/Scripts/pytest.exe --cov=dev_run --cov-report=term-missing tests/unit/test_main_dev_run.py -q)", "Bash(../../.venv/Scripts/pytest.exe --cov=globals --cov-report=term-missing tests/unit/ -q --tb=no)", "Bash(../../.venv/Scripts/pytest.exe --cov=globals --cov-report=term-missing tests/unit/test_main_dev_run.py -q)", "Bash(../../.venv/Scripts/pytest.exe --cov=main_loop --cov-report=term-missing tests/unit/test_main_dev_run.py -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_globals.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_globals.py::TestGlobals::test_is_docker_with_containerd_in_cgroup -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_globals.py::TestGlobals::test_is_docker_with_containerd_in_cgroup -v)", "Bash(../../.venv/Scripts/pytest.exe --cov=globals --cov-report=term-missing tests/unit/test_globals.py -q)", "Bash(rg:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_logfire_data_validation.py -v)"], "deny": []}}