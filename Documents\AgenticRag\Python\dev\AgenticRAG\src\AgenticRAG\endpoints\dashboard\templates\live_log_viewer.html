<div id="liveLogViewer" class="user-card" style="margin-top: 1rem;">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div style="font-weight: 600; font-size: 1rem; color: #e2e8f0;">
                📋 Live System Logs
            </div>
            <!-- Log Source Selection Tabs -->
            <div style="display: flex; gap: 0.5rem; align-items: center;">
                <button id="logSourceDebug" onclick="switchLogSource('debug')" 
                        class="log-source-tab active"
                        style="padding: 0.25rem 0.5rem; background: #3b82f6; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                    🗂️ Debug Trace
                </button>
                <button id="logSourceConsole" onclick="switchLogSource('console')"
                        class="log-source-tab"
                        style="padding: 0.25rem 0.5rem; background: #374151; color: #94a3b8; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                    💻 Console
                </button>
                <button id="logSourceSystem" onclick="switchLogSource('system')"
                        class="log-source-tab"
                        style="padding: 0.25rem 0.5rem; background: #374151; color: #94a3b8; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                    ⚙️ System
                </button>
            </div>
        </div>
        <div style="display: flex; gap: 0.5rem; align-items: center;">
            <div id="logStatus" style="font-size: 0.8rem; color: #94a3b8; display: flex; align-items: center; gap: 0.25rem;">
                <span id="logStatusIcon" style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></span>
                <span id="logStatusText">Connected</span>
            </div>
            <button id="testRealServerBtn" onclick="checkAndRedirectTestReal()" 
                    title="Switch to test_real server (Debug mode only)"
                    onmouseover="this.style.background='#7c3aed'"
                    onmouseout="this.style.background='#8b5cf6'"
                    style="padding: 0.25rem 0.5rem; background: #8b5cf6; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s; display: none;">
                🔄 Test_Real Server
            </button>
            <button id="logPauseBtn" onclick="toggleLogViewer()" 
                    title="Pause/Resume real-time updates"
                    onmouseover="this.style.background='#1f2937'"
                    onmouseout="this.style.background='#374151'"
                    style="padding: 0.25rem 0.5rem; background: #374151; color: #e2e8f0; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                ⏸️ Pause
            </button>
            <button onclick="clearLogViewer()" 
                    title="Clear log display"
                    onmouseover="this.style.background='#dc2626'"
                    onmouseout="this.style.background='#ef4444'"
                    style="padding: 0.25rem 0.5rem; background: #ef4444; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                🗑️ Clear
            </button>
            <button onclick="copyLogContent()" 
                    title="Copy logs to clipboard"
                    onmouseover="this.style.background='#0369a1'"
                    onmouseout="this.style.background='#0284c7'"
                    style="padding: 0.25rem 0.5rem; background: #0284c7; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                📋 Copy
            </button>
        </div>
    </div>
    
    <div style="margin-bottom: 1rem;">
        <input type="text" id="logSearchInput" placeholder="Search logs... (press Enter or type to filter)"
               onkeyup="filterLogContent()"
               style="width: 100%; padding: 0.5rem; background: #1e293b; border: 1px solid #374151; border-radius: 4px; color: #e2e8f0; font-size: 0.85rem;">
    </div>
    
    <!-- Log Filtering Controls -->
    <div style="margin-bottom: 1rem; padding: 1rem; background: #1e293b; border: 1px solid #374151; border-radius: 6px;">
        <div style="font-size: 0.85rem; font-weight: 600; color: #e2e8f0; margin-bottom: 0.75rem;">
            🔧 Log Filters
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <!-- Filter Options Column 1 -->
            <div>
                <label style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.8rem; color: #94a3b8; margin-bottom: 0.5rem;">
                    <input type="checkbox" id="excludeFrontendDebug" checked onchange="applyLogFilters()"
                           style="margin: 0; accent-color: #ef4444;">
                    Hide FRONTEND_DEBUG noise
                </label>
                <label style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.8rem; color: #94a3b8; margin-bottom: 0.5rem;">
                    <input type="checkbox" id="excludeScrubbedAuth" onchange="applyLogFilters()"
                           style="margin: 0; accent-color: #f59e0b;">
                    Hide scrubbed auth messages
                </label>
            </div>
            
            <!-- Event Code Filter Column 2 -->
            <div>
                <label style="display: block; font-size: 0.8rem; color: #94a3b8; margin-bottom: 0.25rem;">
                    Show only event codes:
                </label>
                <input type="text" id="eventCodesFilter" placeholder="e.g., INIT,TASK,ERROR"
                       onkeyup="applyLogFilters()" style="width: 100%; padding: 0.25rem; background: #0f172a; border: 1px solid #374151; border-radius: 3px; color: #e2e8f0; font-size: 0.75rem;">
                <div style="font-size: 0.7rem; color: #64748b; margin-top: 0.25rem;">
                    Comma-separated (empty = show all)
                </div>
            </div>
        </div>
        
        <!-- Filter Status -->
        <div id="filterStatus" style="margin-top: 0.75rem; padding-top: 0.5rem; border-top: 1px solid #374151; font-size: 0.75rem; color: #10b981;">
            Active: Hide FRONTEND_DEBUG
        </div>
    </div>
    
    <div id="logContent" 
         style="background: #0f172a; border: 1px solid #374151; border-radius: 6px; padding: 1rem; min-height: 300px; max-height: 400px; overflow-y: auto; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.8rem; line-height: 1.4; color: #e2e8f0; white-space: pre-wrap; word-break: break-all;">
        <div style="color: #94a3b8; text-align: center; margin-top: 2rem;">
            🔄 Loading system logs...
        </div>
    </div>
    
    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 0.75rem; padding-top: 0.75rem; border-top: 1px solid #374151;">
        <div style="display: flex; gap: 1rem; align-items: center;">
            <label style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.8rem; color: #94a3b8;">
                <input type="checkbox" id="logAutoScroll" checked 
                       style="margin: 0; accent-color: #3b82f6;">
                Auto-scroll to bottom
            </label>
            <label style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.8rem; color: #94a3b8;">
                Update every:
                <select id="logUpdateInterval" onchange="updateLogInterval(this.value)"
                        style="background: #1e293b; border: 1px solid #374151; color: #e2e8f0; padding: 0.25rem; border-radius: 3px; font-size: 0.8rem;">
                    <option value="1000" selected>1 second</option>
                    <option value="2000">2 seconds</option>
                    <option value="5000">5 seconds</option>
                    <option value="10000">10 seconds</option>
                </select>
            </label>
        </div>
        <div id="logStats" style="font-size: 0.75rem; color: #94a3b8; display: flex; gap: 1rem; flex-wrap: wrap; align-items: center;">
            <span>📊 Lines: <span id="logLineCount" style="font-weight: 500;">0</span></span>
            <span>💾 Size: <span id="logSizeInfo" style="font-weight: 500;">0 KB</span></span>
            <span>🔄 Updated: <span id="logLastUpdate" style="font-weight: 500;">Never</span></span>
            <span>📡 Source: <span id="logSourceInfo" style="font-weight: 500; color: #3b82f6;">Debug Trace</span></span>
            <span>🔍 Filtered: <span id="logFilteredCount" style="font-weight: 500;">0</span></span>
        </div>
    </div>
</div>