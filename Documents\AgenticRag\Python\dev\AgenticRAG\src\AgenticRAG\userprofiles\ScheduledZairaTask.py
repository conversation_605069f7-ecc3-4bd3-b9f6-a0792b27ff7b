from imports import *

import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any
from uuid import uuid4
from pydantic import Field, BaseModel
from enum import Enum
from threading import Lock
from time import time as time_time

from userprofiles.LongRunningZairaTask import <PERSON><PERSON>unning<PERSON>airaTask
from userprofiles.ZairaUser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from endpoints.mybot_generic import MyBot_Generic
from langchain_core.messages import HumanMessage
from managers.manager_supervisors import SupervisorManager, SupervisorTask_SingleAgent, SupervisorTaskState
from userprofiles.ScheduleParsingTools import ParseRecurringScheduleTool, ParseDailyScheduleTool, ParseMonthlyScheduleTool, ParseGenericScheduleTool

class ScheduleType(Enum):
    ONCE = "once"
    RECURRING = "recurring"

class ScheduledZairaTask(LongRunningZairaTask):
    schedule_prompt: str = Field(default="", description="The original scheduling prompt")
    target_prompt: str = Field(default="", description="The prompt to execute after delay")
    delay_seconds: float = Field(default=0.0, description="Delay in seconds before execution")
    schedule_type: ScheduleType = Field(default=ScheduleType.ONCE, description="Whether to run once or repeatedly")
    next_execution: Optional[datetime] = Field(None, description="Next scheduled execution time")
    is_active: bool = Field(default=True, description="Whether this scheduled task is active")
    
    def __init__(self, user: ZairaUser, calling_bot: MyBot_Generic, original_message, schedule_prompt: str):
        # Parse the schedule prompt using LLM agent with tools
        try:
            # Check if we're in an event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, create a task to parse asynchronously and use default values temporarily
                target_prompt = schedule_prompt
                delay_seconds = 0.0
                schedule_type = ScheduleType.ONCE
                # Schedule LLM parsing to update the task later
                asyncio.create_task(self._async_parse_and_update(schedule_prompt, user))
            else:
                # If no loop is running, we can use asyncio.run
                target_prompt, delay_seconds, schedule_type = asyncio.run(self._parse_schedule_prompt_with_llm(schedule_prompt, user))
        except RuntimeError:
            # No event loop available - use default values and log
            LogFire.log("ERROR", "No event loop available for LLM parsing, using default values")
            target_prompt = schedule_prompt
            delay_seconds = 0.0
            schedule_type = ScheduleType.ONCE
            
        next_execution = datetime.now() + timedelta(seconds=delay_seconds)
        
        task_id = uuid4()
        complete_message = f"Scheduled task: {schedule_prompt}"
        
        # Call parent constructor with correct parameters
        super().__init__(user, task_id, complete_message, calling_bot, original_message)
        
        # Then set our specific Pydantic fields directly
        object.__setattr__(self, 'schedule_prompt', schedule_prompt)
        object.__setattr__(self, 'target_prompt', target_prompt)
        object.__setattr__(self, 'delay_seconds', delay_seconds)
        object.__setattr__(self, 'schedule_type', schedule_type)
        object.__setattr__(self, 'next_execution', next_execution)
        object.__setattr__(self, 'is_active', True)
        
        # Auto-save to persistence on creation (only if event loop is running)
        try:
            from etc.helper_functions import handle_asyncio_task_result_errors
            task = asyncio.create_task(self._save_to_persistence())
            task.add_done_callback(handle_asyncio_task_result_errors)
        except (RuntimeError, Exception):
            # No event loop running (e.g., in tests), skip auto-save
            pass
    
    def _set_task_status(self, new_status: Dict[str, Any]):
        """Update task status with thread safety"""
        with self._lock:
            self.task_status.update(new_status)
    
    async def _async_parse_and_update(self, schedule_prompt: str, user: 'ZairaUser'):
        """Parse the schedule prompt asynchronously and update task attributes"""
        try:
            target_prompt, delay_seconds, schedule_type = await self._parse_schedule_prompt_with_llm(schedule_prompt, user)
            
            # Update the task attributes
            object.__setattr__(self, 'target_prompt', target_prompt)
            object.__setattr__(self, 'delay_seconds', delay_seconds)
            object.__setattr__(self, 'schedule_type', schedule_type)
            object.__setattr__(self, 'next_execution', datetime.now() + timedelta(seconds=delay_seconds))
            
            # Update persistence with the new parsed information
            await self._save_to_persistence()
            
            LogFire.log("TASK", f"Updated scheduled task with LLM parsing: {target_prompt}")
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Failed to async parse schedule prompt", user)
            LogFire.log("ERROR", f"Failed to update task with LLM parsing: {str(e)}")
    
    async def run_scheduled_task(self):
        while self.is_active:
            # Wait for the scheduled time
            if self.next_execution:
                now = datetime.now()
                if self.next_execution > now:
                    delay = (self.next_execution - now).total_seconds()
                    if delay > 0:
                        await asyncio.sleep(delay)
            else:
                await asyncio.sleep(self.delay_seconds)
            
            if not self.is_active:
                break
                
            # Execute the target prompt by calling parent class
            try:
                # Create a new LongRunningZairaTask for the actual execution
                execution_task = LongRunningZairaTask(
                    user=self.user,
                    task_id=uuid4(),
                    complete_message=self.target_prompt,
                    calling_bot=self.calling_bot,
                    original_message=self.original_physical_message
                )
                
                # Run the task
                await execution_task.run_task()
                await execution_task.await_status_complete(wait_on_complete=True)
                
                LogFire.log("TASK", f"Executed scheduled task: {self.target_prompt}")
                
            except Exception as e:
                LogFire.log("ERROR", f"Scheduled task execution failed: {str(e)}")
                await self.calling_bot.send_reply(f"Scheduled task failed: {str(e)}", self, self.original_physical_message, False)
            
            # Handle recurring vs one-time tasks
            if self.schedule_type == ScheduleType.ONCE:
                self.is_active = False
                break
            else:
                # Schedule next execution
                self.next_execution = datetime.now() + timedelta(seconds=self.delay_seconds)
                self._set_task_status({
                    'status': 'scheduled',
                    'message': f'Next execution at {self.next_execution.strftime("%Y-%m-%d %H:%M:%S")}',
                    'next_execution': self.next_execution.isoformat()
                })
                
                # Update persistence with new schedule
                from etc.helper_functions import handle_asyncio_task_result_errors
                task = asyncio.create_task(self._update_persistence())
                task.add_done_callback(handle_asyncio_task_result_errors)
    
    async def run_task(self):
        # Override parent's run_task to start the scheduling loop
        self._set_task_status({
            'status': 'scheduled',
            'message': f'Task scheduled for {self.next_execution.strftime("%Y-%m-%d %H:%M:%S")}',
            'next_execution': self.next_execution.isoformat()
        })
        
        # Start the scheduling loop
        await self.run_scheduled_task()
        
        # Mark as completed when done
        self._set_task_status({
            'status': 'completed',
            'message': 'Scheduled task completed',
            'completed_at': datetime.now().timestamp()
        })
    
    def cancel_schedule(self, reason: str = "User requested cancellation"):
        """Cancel the scheduled task"""
        self.is_active = False
        self._set_task_status({
            'status': 'cancelled',
            'message': f'Scheduled task cancelled: {reason}',
            'cancelled_at': datetime.now().timestamp(),
            'cancellation_reason': reason
        })
        
        # Save cancellation to persistence
        from etc.helper_functions import handle_asyncio_task_result_errors
        task = asyncio.create_task(self._save_cancellation_to_persistence(reason))
        task.add_done_callback(handle_asyncio_task_result_errors)
    
    def get_schedule_info(self) -> Dict[str, Any]:
        """Get information about this scheduled task"""
        return {
            'schedule_prompt': self.schedule_prompt,
            'target_prompt': self.target_prompt,
            'delay_seconds': self.delay_seconds,
            'schedule_type': self.schedule_type.value,
            'next_execution': self.next_execution.isoformat() if self.next_execution else None,
            'is_active': self.is_active,
            'task_id': str(self.task_id)
        }
    
    async def _save_to_persistence(self):
        """Save this task to persistence layer"""
        try:
            from managers.manager_scheduled_tasks import get_persistence_manager
            persistence_manager = await get_persistence_manager()
            await persistence_manager.save_task(self)
        except Exception as e:
            LogFire.log("ERROR", f"Failed to save scheduled task to persistence: {str(e)}")
    
    async def _save_cancellation_to_persistence(self, reason: str):
        """Save task cancellation to persistence layer"""
        try:
            from managers.manager_scheduled_tasks import get_persistence_manager
            persistence_manager = await get_persistence_manager()
            await persistence_manager.cancel_task(str(self.task_id), reason)
        except Exception as e:
            LogFire.log("ERROR", f"Failed to save task cancellation to persistence: {str(e)}")
    
    async def _update_persistence(self):
        """Update this task in persistence (called when schedule changes)"""
        await self._save_to_persistence()
    
    async def _parse_schedule_prompt_with_llm(self, prompt: str, user: 'ZairaUser') -> tuple[str, float, ScheduleType]:
        """Parse schedule prompt using LLM agent with tools"""
        try:
            # Create LLM parsing agent
            parsing_agent = await self._create_schedule_parsing_agent()
            
            # Create state for the parsing task
            state = SupervisorTaskState(
                original_input=prompt,
                user_guid=str(user.user_guid),
                additional_input={},
                messages=[HumanMessage(content=f"Parse this schedule prompt: {prompt}")]
            )
            
            # Execute the parsing agent
            result = await parsing_agent.llm_call(state)
            
            # Extract parsed information from the result
            return self._extract_parsed_info_from_llm_result(result, prompt)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "LLM schedule parsing failed", user)
            # Return default values if LLM parsing fails
            return prompt, 0.0, ScheduleType.ONCE
    
    async def _create_schedule_parsing_agent(self) -> SupervisorTask_SingleAgent:
        """Create an LLM agent with tools for parsing schedule prompts"""
        # Create parsing tools
        parse_recurring_tool = ParseRecurringScheduleTool()
        parse_daily_tool = ParseDailyScheduleTool()
        parse_monthly_tool = ParseMonthlyScheduleTool()
        parse_generic_tool = ParseGenericScheduleTool()
        
        # Create the agent with tools
        agent = SupervisorTask_SingleAgent(
            name="schedule_parser",
            tools=[parse_recurring_tool, parse_daily_tool, parse_monthly_tool, parse_generic_tool],
            prompt_id="Schedule_Parser_Prompt"
        )
        
        return agent
    
    def _extract_parsed_info_from_llm_result(self, result: SupervisorTaskState, original_prompt: str) -> tuple[str, float, ScheduleType]:
        """Extract parsed schedule information from LLM result"""
        try:
            # Look for parsing results in the messages
            for message in result.messages:
                if hasattr(message, 'content') and isinstance(message.content, str):
                    content = message.content
                    
                    # Look for structured parsing results
                    if 'TARGET_PROMPT:' in content and 'DELAY_SECONDS:' in content and 'SCHEDULE_TYPE:' in content:
                        lines = content.split('\n')
                        target_prompt = ""
                        delay_seconds = 0.0
                        schedule_type = ScheduleType.ONCE
                        
                        for line in lines:
                            line = line.strip()
                            if line.startswith('TARGET_PROMPT:'):
                                target_prompt = line.replace('TARGET_PROMPT:', '').strip()
                            elif line.startswith('DELAY_SECONDS:'):
                                delay_seconds = float(line.replace('DELAY_SECONDS:', '').strip())
                            elif line.startswith('SCHEDULE_TYPE:'):
                                schedule_type_str = line.replace('SCHEDULE_TYPE:', '').strip().lower()
                                schedule_type = ScheduleType.RECURRING if schedule_type_str == 'recurring' else ScheduleType.ONCE
                        
                        if target_prompt:  # Only return if we got a valid target prompt
                            return target_prompt, delay_seconds, schedule_type
            
            # If no structured result found, return default values
            return original_prompt, 0.0, ScheduleType.ONCE
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to extract parsing result: {str(e)}")
            return original_prompt, 0.0, ScheduleType.ONCE