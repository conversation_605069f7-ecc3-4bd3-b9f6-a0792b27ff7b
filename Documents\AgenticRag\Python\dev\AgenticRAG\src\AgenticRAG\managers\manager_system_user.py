from imports import *

from uuid import UUID, uuid4
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import os
from pathlib import Path

from managers.manager_users import ZairaUserManager
from managers.manager_scheduled_tasks import get_persistence_manager
from userprofiles.permission_levels import PERMISSION_LEVELS
from userprofiles.ScheduledZairaTask import ScheduledZairaTask
from etc.helper_functions import is_claude_environment


class SystemUserManager:
    """
    Manages the SYSTEM user and environment-specific scheduled tasks.
    Creates appropriate scheduled tasks based on the runtime environment.
    """
    
    _instance: Optional['SystemUserManager'] = None
    _initialized: bool = False
    SYSTEM_USER_GUID = UUID("00000000-0000-0000-0000-000000000001")  # Fixed GUID for SYSTEM user
    SYSTEM_DEVICE_GUID = UUID("00000000-0000-0000-0000-000000000002")  # Fixed device GUID for SYSTEM
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> 'SystemUserManager':
        """Get singleton instance of SystemUserManager"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        """Initialize the system user"""
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        instance._initialized = True
        
        # Create or retrieve SYSTEM user
        await instance._create_system_user()
        
        LogFire.log("SYSTEM_USER", "SystemUserManager initialized with SYSTEM user")
    
    @classmethod
    async def late_setup(cls):
        """Create environment-specific scheduled tasks after all managers are initialized"""
        instance = cls.get_instance()
        try:
            # Create environment-specific scheduled tasks
            await instance._create_environment_tasks()
            LogFire.log("SYSTEM_USER", "Environment-specific scheduled tasks created")
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create environment tasks: {e}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "SystemUserManager.late_setup", str(instance.SYSTEM_USER_GUID))
    
    async def _create_system_user(self):
        """Create the SYSTEM user if it doesn't exist"""
        try:
            user_manager = ZairaUserManager.get_instance()
            
            # Check if SYSTEM user already exists
            system_user_guid_str = str(self.SYSTEM_USER_GUID)
            if system_user_guid_str in user_manager.users:
                LogFire.log("SYSTEM_USER", "SYSTEM user already exists", system_user_guid_str)
                return
            
            # Create SYSTEM user with highest permissions
            system_user = await ZairaUserManager.add_user(
                username="SYSTEM",
                rank=PERMISSION_LEVELS.ADMIN,  # Highest permission level
                guid=self.SYSTEM_USER_GUID,
                device_guid=self.SYSTEM_DEVICE_GUID
            )
            
            # Set additional system user properties
            system_user.email = "<EMAIL>"
            system_user.platform = "SYSTEM"
            system_user.is_system_user = True
            
            LogFire.log("SYSTEM_USER", "SYSTEM user created successfully", str(self.SYSTEM_USER_GUID))
            
        except ValueError as e:
            if "already exists" in str(e):
                LogFire.log("SYSTEM_USER", "SYSTEM user already exists", str(self.SYSTEM_USER_GUID))
            else:
                LogFire.log("ERROR", f"Failed to create SYSTEM user: {e}", str(self.SYSTEM_USER_GUID))
                raise
        except Exception as e:
            LogFire.log("ERROR", f"Unexpected error creating SYSTEM user: {e}", str(self.SYSTEM_USER_GUID))
            raise
    
    async def _create_environment_tasks(self):
        """Create scheduled tasks based on the current environment"""
        try:
            # Detect environment
            is_claude = is_claude_environment()
            is_docker = Globals.is_docker()
            is_debug = Globals.is_debug()
            
            LogFire.log("SYSTEM_USER", f"Environment detection: Claude={is_claude}, Docker={is_docker}, Debug={is_debug}")
            
            # Always create the Monday 9am team task for all environments
            await self._create_monday_team_task()
            
            if not is_claude and not is_docker and not is_debug:
                # Non-Claude, non-Docker, non-debug environment: Create test .bat file tasks
                pass
                #await self._create_test_bat_tasks()
            elif is_docker:
                # Docker environment: Create weekly standup notification task
                await self._create_standup_notification_task()
            else:
                LogFire.log("SYSTEM_USER", f"Additional environment-specific tasks not created for: Claude={is_claude}, Docker={is_docker}, Debug={is_debug}")
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create environment tasks: {e}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "SystemUserManager._create_environment_tasks", str(self.SYSTEM_USER_GUID))
    
    async def _create_test_bat_tasks(self):
        """Create scheduled tasks for the 5 test .bat files"""
        test_bat_files = [
            {
                "name": "Monday Unit Tests",
                "file": "test_monday_unit.bat",
                "schedule": "weekly monday 08:00",
                "description": "Run comprehensive unit test suite every Monday at 8:00 AM"
            },
            {
                "name": "Tuesday Integration Tests", 
                "file": "test_tuesday_integration.bat",
                "schedule": "weekly tuesday 08:00",
                "description": "Run integration test suite every Tuesday at 8:00 AM"
            },
            {
                "name": "Thursday Performance Tests",
                "file": "test_thursday_performance.bat", 
                "schedule": "weekly thursday 08:00",
                "description": "Run performance test suite every Thursday at 8:00 AM"
            },
            {
                "name": "Friday Health Tests",
                "file": "test_friday_health.bat",
                "schedule": "weekly friday 08:00", 
                "description": "Run health check tests every Friday at 8:00 AM"
            },
            {
                "name": "Weekly Test Summary",
                "file": "run_weekly_tests.bat",
                "schedule": "weekly sunday 20:00",
                "description": "Run comprehensive weekly test summary every Sunday at 8:00 PM"
            }
        ]
        
        persistence_manager = await get_persistence_manager()
        created_tasks = []
        
        for test_config in test_bat_files:
            try:
                # Check if task already exists
                existing_tasks = await persistence_manager.get_active_tasks(str(self.SYSTEM_USER_GUID))
                task_exists = any(
                    task.target_prompt and test_config["file"] in task.target_prompt 
                    for task in existing_tasks
                )
                
                if task_exists:
                    LogFire.log("SYSTEM_USER", f"Test task already exists: {test_config['name']}")
                    continue
                
                # Create scheduled task
                task_guid = str(uuid4())
                schedule_prompt = f"Run {test_config['name']} - {test_config['description']}"
                target_prompt = f"Execute test script: {test_config['file']}"
                
                # Calculate next execution time based on schedule
                delay_seconds = self._calculate_weekly_delay(test_config["schedule"])
                
                # Get the system user
                user_manager = ZairaUserManager.get_instance()
                system_user = user_manager.users.get(str(self.SYSTEM_USER_GUID))
                
                if not system_user:
                    LogFire.log("ERROR", "SYSTEM user not found for creating scheduled task")
                    continue
                
                # Create a mock bot for system tasks
                from endpoints.mybot_generic import MyBot_Generic
                system_bot = MyBot_Generic(parent_instance=None, name="SYSTEM")
                
                # Create the scheduled task using the proper constructor
                scheduled_task = ScheduledZairaTask(
                    user=system_user,
                    calling_bot=system_bot,
                    original_message=None,
                    schedule_prompt=f"{schedule_prompt} -> {target_prompt}"
                )
                
                # Save task to persistence
                await persistence_manager.save_task(scheduled_task)
                created_tasks.append(test_config["name"])
                
                LogFire.log("SYSTEM_USER", f"Created test task: {test_config['name']}", task_guid)
                
            except Exception as e:
                LogFire.log("ERROR", f"Failed to create test task {test_config['name']}: {e}")
        
        if created_tasks:
            LogFire.log("SYSTEM_USER", f"Created {len(created_tasks)} test tasks", ", ".join(created_tasks))
        else:
            LogFire.log("SYSTEM_USER", "All test tasks already exist or failed to create")
    
    async def _create_monday_team_task(self):
        """Create Monday 9am good luck message task"""
        try:
            persistence_manager = await get_persistence_manager()
            
            # Check if Monday good luck message task already exists
            existing_tasks = await persistence_manager.get_active_tasks(str(self.SYSTEM_USER_GUID))
            monday_task_exists = any(
                task.get('target_prompt') and "good luck this week" in task.get('target_prompt', '').lower()
                for task in existing_tasks
            )
            
            if monday_task_exists:
                LogFire.log("SYSTEM_USER", "Monday 9am good luck message task already exists")
                return
            
            # Create Monday 9am good luck message task
            task_guid = str(uuid4())
            schedule_prompt = "Monday 9am Good Luck Message - Weekly motivation message"
            target_prompt = """Send a 'good luck this week' message to all communication platforms:
1. Execute output_sender supervisor to distribute the message: 'Good luck this week!'
2. Send to all available communication platforms: Discord, Teams, Slack, WhatsApp, Email

Message content: 'Good luck this week!'"""
            
            # Get the system user
            user_manager = ZairaUserManager.get_instance()
            system_user = user_manager.users.get(str(self.SYSTEM_USER_GUID))
            
            if not system_user:
                LogFire.log("ERROR", "SYSTEM user not found for creating Monday team task")
                return
            
            # Create a mock bot for system tasks
            from endpoints.mybot_generic import MyBot_Generic
            system_bot = MyBot_Generic(parent_instance=None, name="SYSTEM")
            
            # Create the scheduled task using the proper constructor
            scheduled_task = ScheduledZairaTask(
                user=system_user,
                calling_bot=system_bot,
                original_message=None,
                schedule_prompt=f"{schedule_prompt} -> {target_prompt}"
            )
            
            # Save task to persistence
            await persistence_manager.save_task(scheduled_task)
            
            LogFire.log("SYSTEM_USER", "Created Monday 9am good luck message task", task_guid)
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create Monday team task: {e}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "SystemUserManager._create_monday_team_task", str(self.SYSTEM_USER_GUID))

    async def _create_standup_notification_task(self):
        """Create weekly standup notification task for Docker environment"""
        try:
            persistence_manager = await get_persistence_manager()
            
            # Check if standup task already exists
            existing_tasks = await persistence_manager.get_active_tasks(str(self.SYSTEM_USER_GUID))
            standup_exists = any(
                task.target_prompt and "standup" in task.target_prompt.lower() 
                for task in existing_tasks
            )
            
            if standup_exists:
                LogFire.log("SYSTEM_USER", "Weekly standup notification task already exists")
                return
            
            # Create weekly standup notification task
            task_guid = str(uuid4())
            schedule_prompt = "Weekly standup notification - Start of the week team coordination"
            target_prompt = """Send weekly standup notification to all communication channels:
            - Discord: Notify Zaira's coworkers that standup will commence in 15 minutes
            - Teams: Send start of week standup reminder  
            - WhatsApp: Weekly team coordination message
            Message: 'Good morning team! Start of a new week! Standup meeting will commence in 15 minutes. Please prepare your updates and join the call. Have a productive week ahead! - Zaira'"""
            
            # Get the system user
            user_manager = ZairaUserManager.get_instance()
            system_user = user_manager.users.get(str(self.SYSTEM_USER_GUID))
            
            if not system_user:
                LogFire.log("ERROR", "SYSTEM user not found for creating standup task")
                return
            
            # Create a mock bot for system tasks
            from endpoints.mybot_generic import MyBot_Generic
            system_bot = MyBot_Generic(parent_instance=None, name="SYSTEM")
            
            # Create the scheduled task using the proper constructor
            scheduled_task = ScheduledZairaTask(
                user=system_user,
                calling_bot=system_bot,
                original_message=None,
                schedule_prompt=f"{schedule_prompt} -> {target_prompt}"
            )
            
            # Save task to persistence
            await persistence_manager.save_task(scheduled_task)
            
            LogFire.log("SYSTEM_USER", "Created weekly standup notification task", task_guid)
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create standup notification task: {e}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "SystemUserManager._create_standup_notification_task", str(self.SYSTEM_USER_GUID))
    
    def _calculate_weekly_delay(self, schedule_str: str) -> float:
        """Calculate delay in seconds for weekly schedule (e.g., 'weekly monday 08:00')"""
        try:
            parts = schedule_str.lower().split()
            if len(parts) != 3 or parts[0] != "weekly":
                raise ValueError(f"Invalid schedule format: {schedule_str}")
            
            day_name = parts[1]
            time_str = parts[2]
            
            # Map day names to weekday numbers (Monday = 0)
            day_mapping = {
                "monday": 0, "tuesday": 1, "wednesday": 2, "thursday": 3,
                "friday": 4, "saturday": 5, "sunday": 6
            }
            
            if day_name not in day_mapping:
                raise ValueError(f"Invalid day name: {day_name}")
            
            target_weekday = day_mapping[day_name]
            
            # Parse time
            hour, minute = map(int, time_str.split(":"))
            
            # Calculate next occurrence
            now = datetime.now()
            days_ahead = target_weekday - now.weekday()
            
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            
            target_date = now + timedelta(days=days_ahead)
            target_datetime = target_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # Calculate delay in seconds
            delay = (target_datetime - now).total_seconds()
            
            return max(delay, 60)  # Minimum 1 minute delay
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to calculate weekly delay for {schedule_str}: {e}")
            # Default to 1 week delay if calculation fails
            return 7 * 24 * 60 * 60  # 1 week in seconds
    
    async def get_system_user(self):
        """Get the SYSTEM user instance"""
        user_manager = ZairaUserManager.get_instance()
        return user_manager.users.get(str(self.SYSTEM_USER_GUID))
    
    async def cleanup_system_tasks(self):
        """Clean up all SYSTEM user tasks (for testing/maintenance)"""
        try:
            persistence_manager = await get_persistence_manager()
            system_tasks = await persistence_manager.get_active_tasks(str(self.SYSTEM_USER_GUID))
            
            for task in system_tasks:
                await persistence_manager.cancel_task(task.task_guid, "System cleanup")
            
            LogFire.log("SYSTEM_USER", f"Cleaned up {len(system_tasks)} system tasks")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to cleanup system tasks: {e}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "SystemUserManager.cleanup_system_tasks", str(self.SYSTEM_USER_GUID))

