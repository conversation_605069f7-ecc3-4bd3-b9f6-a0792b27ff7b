from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../..'))

from imports import *
from userprofiles.ScheduledZairaTask import ScheduledZairaTask, ScheduleType
from userprofiles.ScheduleParsingTools import ParseRecurringScheduleTool, ParseDailyScheduleTool, ParseMonthlyScheduleTool, ParseGenericScheduleTool
from userprofiles.<PERSON>airaUser import ZairaUser
from userprofiles.permission_levels import PERMISSION_LEVELS
from endpoints.mybot_generic import MyBot_Generic
import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

class TestScheduleParsingLLM:
    """Test LLM-based schedule parsing functionality"""
    
    def test_creation_with_llm_parsing(self):
        """Test that ScheduledZairaTask creation works with LLM parsing"""
        # Create test user and bot
        user = <PERSON>air<PERSON><PERSON><PERSON>("test_user", PERMISSION_LEVELS.USER, uuid4(), uuid4())
        user.email = "<EMAIL>"
        user.real_name = "Test User"
        bot = MyBot_Generic(None, "test_bot")
        
        # Test with a simple schedule prompt
        schedule_prompt = "send me a reminder in 5 minutes"
        
        # Create the task (should fall back to regex parsing if LLM fails)
        task = ScheduledZairaTask(user, bot, None, schedule_prompt)
        
        # Verify the task was created successfully
        assert task.schedule_prompt == schedule_prompt
        assert task.is_active is True
        assert task.schedule_type in [ScheduleType.ONCE, ScheduleType.RECURRING]
        assert task.delay_seconds >= 0
        assert task.target_prompt is not None
    
    async def test_recurring_schedule_tool(self):
        """Test the ParseRecurringScheduleTool"""
        tool = ParseRecurringScheduleTool()
        
        # Test recurring pattern
        result = await tool._arun("trigger IMAP IDLE every 30 minutes")
        assert "TARGET_PROMPT:" in result
        assert "DELAY_SECONDS:" in result
        assert "SCHEDULE_TYPE: recurring" in result
        assert "1800" in result  # 30 minutes = 1800 seconds
        
        # Test generic recurring pattern
        result = await tool._arun("check email every 2 hours")
        assert "TARGET_PROMPT:" in result
        assert "DELAY_SECONDS:" in result
        assert "SCHEDULE_TYPE: recurring" in result
        assert "7200" in result  # 2 hours = 7200 seconds
        
        # Test no match
        result = await tool._arun("send me a message at 9am")
        assert result == "No recurring pattern found"
    
    async def test_daily_schedule_tool(self):
        """Test the ParseDailyScheduleTool"""
        tool = ParseDailyScheduleTool()
        
        # Test daily pattern
        result = await tool._arun("send me a good morning message at 9am monday to friday")
        assert "TARGET_PROMPT:" in result
        assert "send message: a good morning message" in result
        assert "DELAY_SECONDS:" in result
        assert "SCHEDULE_TYPE: recurring" in result
        
        # Test no match
        result = await tool._arun("check email every 30 minutes")
        assert result == "No daily pattern found"
    
    async def test_monthly_schedule_tool(self):
        """Test the ParseMonthlyScheduleTool"""
        tool = ParseMonthlyScheduleTool()
        
        # Test monthly pattern
        result = await tool._arun("email me a report every first of the month")
        assert "TARGET_PROMPT:" in result
        assert "email report: a report" in result
        assert "DELAY_SECONDS:" in result
        assert "SCHEDULE_TYPE: recurring" in result
        
        # Test no match
        result = await tool._arun("send me a reminder in 5 minutes")
        assert result == "No monthly pattern found"
    
    async def test_generic_schedule_tool(self):
        """Test the ParseGenericScheduleTool"""
        tool = ParseGenericScheduleTool()
        
        # Test "in X time" pattern
        result = await tool._arun("remind me to call John in 30 minutes")
        assert "TARGET_PROMPT:" in result
        assert "remind me to call John" in result
        assert "DELAY_SECONDS: 1800" in result  # 30 minutes = 1800 seconds
        assert "SCHEDULE_TYPE: once" in result
        
        # Test fallback pattern (immediate task)
        result = await tool._arun("check the weather")
        assert "TARGET_PROMPT: check the weather" in result
        assert "DELAY_SECONDS: 0" in result
        assert "SCHEDULE_TYPE: once" in result
    
    async def test_llm_parsing_agent_creation(self):
        """Test that the LLM parsing agent can be created"""
        user = ZairaUser("test_user", PERMISSION_LEVELS.USER, uuid4(), uuid4())
        user.email = "<EMAIL>"
        user.real_name = "Test User"
        bot = MyBot_Generic(None, "test_bot")
        task = ScheduledZairaTask(user, bot, None, "test prompt")
        
        # Test agent creation
        agent = await task._create_schedule_parsing_agent()
        
        # Verify agent has the expected tools
        assert agent.name == "schedule_parser"
        assert len(agent.tools) == 4
        assert agent.prompt_id == "Schedule_Parser_Prompt"
        
        # Verify tool types
        tool_names = [tool.name for tool in agent.tools]
        expected_tools = ["parse_recurring_schedule", "parse_daily_schedule", "parse_monthly_schedule", "parse_generic_schedule"]
        for expected_tool in expected_tools:
            assert expected_tool in tool_names
    
    def test_extract_parsed_info_from_llm_result(self):
        """Test extraction of parsing results from LLM output"""
        user = ZairaUser("test_user", PERMISSION_LEVELS.USER, uuid4(), uuid4())
        user.email = "<EMAIL>"
        user.real_name = "Test User"
        bot = MyBot_Generic(None, "test_bot")
        task = ScheduledZairaTask(user, bot, None, "test prompt")
        
        # Create mock result with structured output
        from langchain_core.messages import AIMessage
        from managers.manager_supervisors import SupervisorTaskState
        
        mock_result = SupervisorTaskState(
            original_input="test",
            user_guid="test_guid",
            additional_input={},
            messages=[AIMessage(content="TARGET_PROMPT: test action\nDELAY_SECONDS: 300\nSCHEDULE_TYPE: once")]
        )
        
        target_prompt, delay_seconds, schedule_type = task._extract_parsed_info_from_llm_result(mock_result, "original prompt")
        
        assert target_prompt == "test action"
        assert delay_seconds == 300.0
        assert schedule_type == ScheduleType.ONCE
    
    def test_fallback_to_regex_parsing(self):
        """Test that the system falls back to regex parsing when LLM fails"""
        user = ZairaUser("test_user", PERMISSION_LEVELS.USER, uuid4(), uuid4())
        user.email = "<EMAIL>"
        user.real_name = "Test User"
        bot = MyBot_Generic(None, "test_bot")
        
        # Create task with a pattern that regex can handle
        schedule_prompt = "remind me to call in 5 minutes"
        task = ScheduledZairaTask(user, bot, None, schedule_prompt)
        
        # Should have been parsed successfully (either by LLM or regex fallback)
        assert task.target_prompt is not None
        assert task.delay_seconds > 0
        assert task.schedule_type == ScheduleType.ONCE

if __name__ == "__main__":
    # Run async tests for tools only (agent creation requires full system setup)
    async def run_async_tests():
        test_instance = TestScheduleParsingLLM()
        await test_instance.test_recurring_schedule_tool()
        await test_instance.test_daily_schedule_tool()
        await test_instance.test_monthly_schedule_tool()
        await test_instance.test_generic_schedule_tool()
        print("All async tool tests passed!")
    
    # Run sync tests
    test_instance = TestScheduleParsingLLM()
    test_instance.test_creation_with_llm_parsing()
    test_instance.test_extract_parsed_info_from_llm_result()
    test_instance.test_fallback_to_regex_parsing()
    print("All sync tests passed!")
    
    # Run async tests
    asyncio.run(run_async_tests())
    print("All tests completed successfully!")