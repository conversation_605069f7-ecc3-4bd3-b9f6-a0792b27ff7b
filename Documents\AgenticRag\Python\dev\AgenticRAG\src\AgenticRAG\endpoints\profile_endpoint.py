from imports import *

from aiohttp import web, web_response
from aiohttp.web_request import Request
from aiohttp.web_response import Response
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import json
from pathlib import Path

from managers.manager_logfire import LogFire
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import <PERSON>aira<PERSON><PERSON>, PERMISSION_LEVELS
from etc.helper_functions import exception_triggered

class ProfileEndpoint:
    """
    Profile management endpoint for AskZaira dashboard.
    Provides user profile viewing and editing functionality.
    """
    
    def __init__(self):
        self.base_path = Path(__file__).parent.parent / "ui"
        LogFire.log("INIT", "ProfileEndpoint initialized")
    
    async def setup_routes(self, app: web.Application):
        """Setup profile endpoint routes"""
        app.router.add_get('/profile', self.profile_page)
        app.router.add_post('/profile/update', self.update_profile)
        app.router.add_get('/profile/api/stats', self.get_profile_stats)
        LogFire.log("INIT", "Profile endpoint routes configured")
    
    async def profile_page(self, request: Request) -> Response:
        """Serve the profile dashboard page"""
        try:
            # Get user from session/auth (simplified for demo)
            user_guid = request.query.get('user_guid', 'demo-user-guid')
            
            # Get user data
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                # Use SYSTEM user as fallback
                user = await self._get_system_user()
            
            # Load UI components
            head_content = await self._load_ui_file("profile_head.txt")
            header_content = await self._load_ui_file("profile_header.txt") 
            footer_content = await self._load_ui_file("profile_footer.txt")
            whitelabel_css = await self._load_ui_file("whitelabel.txt")
            
            # Replace template variables
            head_content = head_content.replace("{whitelabel_css}", whitelabel_css)
            header_content = await self._replace_user_variables(header_content, user)
            
            # Combine all content
            full_html = head_content + header_content + footer_content
            
            LogFire.log("RETRIEVE", f"Profile page served for user {user_guid}")
            return web.Response(text=full_html, content_type='text/html')
            
        except Exception as e:
            exception_triggered(e, "profile_page", user_guid if 'user_guid' in locals() else None)
            return web.Response(text="Error loading profile page", status=500)
    
    async def update_profile(self, request: Request) -> Response:
        """Update user profile information"""
        try:
            # Parse form data
            data = await request.post()
            user_guid = request.query.get('user_guid', 'demo-user-guid')
            
            # Get user
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                return web.json_response({"error": "User not found"}, status=404)
            
            # Update profile fields
            user.first_name = data.get('first_name', '').strip()
            user.last_name = data.get('last_name', '').strip()
            user.email = data.get('email', '').strip()
            user.job_title = data.get('job_title', '').strip()
            user.company = data.get('company', '').strip()
            user.personal_prompt = data.get('personal_prompt', '').strip()
            user.preferred_language = data.get('preferred_language', 'en')
            user.timezone = data.get('timezone', 'UTC')
            
            # Update real_name for backward compatibility
            if user.first_name or user.last_name:
                user.real_name = f"{user.first_name} {user.last_name}".strip()
            
            # Validate email if provided
            if user.email and not self._is_valid_email(user.email):
                return web.json_response({"error": "Invalid email address"}, status=400)
            
            # In a real implementation, you would persist to database here
            # For now, the in-memory user object is updated
            
            # Get user's chat session for logging
            chat_session = user.get_current_chat_session() if user else None
            LogFire.log("USER", f"Profile updated for user {user_guid}", chat=chat_session)
            return web.json_response({
                "success": True,
                "message": "Profile updated successfully"
            })
            
        except Exception as e:
            # Get user's chat session for exception logging
            chat_session = user.get_current_chat_session() if 'user' in locals() and user else None
            exception_triggered(e, "update_profile", chat_session)
            return web.json_response({"error": "Failed to update profile"}, status=500)
    
    async def get_profile_stats(self, request: Request) -> Response:
        """Get profile statistics for the dashboard"""
        try:
            user_guid = request.query.get('user_guid', 'demo-user-guid')
            
            # Get user
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                return web.json_response({"error": "User not found"}, status=404)
            
            # Calculate statistics
            stats = {
                "total_requests": len(user.my_requests),
                "active_sessions": len(user.chat_history),
                "permission_level": user.rank.name,
                "last_activity": "Just now",  # Would be calculated from actual activity
                "chat_sessions": len(user.chat_history),
                "completed_requests": len([r for r in user.my_requests.values() 
                                         if hasattr(r, 'status') and r.status == 'completed'])
            }
            
            return web.json_response(stats)
            
        except Exception as e:
            exception_triggered(e, "get_profile_stats", user_guid if 'user_guid' in locals() else None)
            return web.json_response({"error": "Failed to get profile statistics"}, status=500)
    
    async def _load_ui_file(self, filename: str) -> str:
        """Load UI template file content"""
        try:
            file_path = self.base_path / filename
            if file_path.exists():
                return file_path.read_text(encoding='utf-8')
            else:
                LogFire.log("ERROR", f"UI file not found: {filename}", severity="warning")
                return ""
        except Exception as e:
            exception_triggered(e, f"Loading UI file: {filename}", None)
            return ""
    
    async def _replace_user_variables(self, content: str, user: ZairaUser) -> str:
        """Replace template variables with user data"""
        try:
            # Calculate display values
            display_name = user.real_name or f"{user.first_name} {user.last_name}".strip() or user.username or "User Profile"
            user_initials = self._get_user_initials(user)
            status_class = "active" if user.has_active_requests() else "inactive"
            status_text = "Active" if user.has_active_requests() else "Inactive"
            
            # Permission level display
            permission_display = user.rank.name.title()
            
            # Activity display (simplified)
            last_activity = "Just now"
            
            # Language selection
            lang_options = {
                'en_selected': 'selected' if user.preferred_language == 'en' else '',
                'nl_selected': 'selected' if user.preferred_language == 'nl' else '',
                'fr_selected': 'selected' if user.preferred_language == 'fr' else '',
                'de_selected': 'selected' if user.preferred_language == 'de' else '',
                'es_selected': 'selected' if user.preferred_language == 'es' else '',
            }
            
            # Timezone selection
            tz_options = {
                'utc_selected': 'selected' if user.timezone == 'UTC' else '',
                'amsterdam_selected': 'selected' if user.timezone == 'Europe/Amsterdam' else '',
                'ny_selected': 'selected' if user.timezone == 'America/New_York' else '',
                'la_selected': 'selected' if user.timezone == 'America/Los_Angeles' else '',
                'tokyo_selected': 'selected' if user.timezone == 'Asia/Tokyo' else '',
            }
            
            # Replace all variables
            replacements = {
                '{user_initials}': user_initials,
                '{display_name}': display_name,
                '{job_title}': user.job_title or 'No title set',
                '{status_class}': status_class,
                '{status_text}': status_text,
                '{total_requests}': str(len(user.my_requests)),
                '{active_sessions}': str(len(user.chat_history)),
                '{permission_level}': permission_display,
                '{last_activity}': last_activity,
                '{messages_html}': '',  # No messages by default
                '{first_name}': user.first_name,
                '{last_name}': user.last_name,
                '{email}': user.email,
                '{company}': user.company,
                '{personal_prompt}': user.personal_prompt,
                **lang_options,
                **tz_options
            }
            
            for placeholder, value in replacements.items():
                content = content.replace(placeholder, str(value))
            
            return content
            
        except Exception as e:
            exception_triggered(e, "Replacing user variables in template", user.user_guid if user else None)
            return content
    
    def _get_user_initials(self, user: ZairaUser) -> str:
        """Get user initials for avatar display"""
        try:
            if user.first_name and user.last_name:
                return f"{user.first_name[0]}{user.last_name[0]}".upper()
            elif user.real_name:
                parts = user.real_name.split()
                if len(parts) >= 2:
                    return f"{parts[0][0]}{parts[-1][0]}".upper()
                elif len(parts) == 1:
                    return parts[0][:2].upper()
            elif user.username:
                return user.username[:2].upper()
            else:
                return "U"
        except Exception:
            return "U"
    
    def _is_valid_email(self, email: str) -> bool:
        """Basic email validation"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    async def _get_system_user(self) -> ZairaUser:
        """Get or create the SYSTEM user for profile display"""
        try:
            from managers.manager_system_user import SystemUserManager
            system_manager = SystemUserManager.get_instance()
            user = await system_manager.get_system_user()
            
            if not user:
                # If SYSTEM user doesn't exist, create it
                await SystemUserManager.setup()
                user = await system_manager.get_system_user()
            
            # Set profile information for SYSTEM user if needed
            if user:
                if not user.first_name:
                    user.first_name = "System"
                if not user.last_name:
                    user.last_name = "Administrator"  
                if not user.email:
                    user.email = "<EMAIL>"
                if not user.job_title:
                    user.job_title = "System Administrator"
                if not user.company:
                    user.company = "AskZaira System"
                if not user.personal_prompt:
                    user.personal_prompt = "I manage system operations and provide administrative oversight."
                if not user.real_name:
                    user.real_name = "System Administrator"
            
            LogFire.log("USER", "SYSTEM user retrieved for profile display")
            return user
            
        except Exception as e:
            exception_triggered(e, "Getting SYSTEM user", None)
            raise

# Singleton instance
profile_endpoint = ProfileEndpoint()

async def setup_profile_routes(app: web.Application):
    """Setup profile endpoint routes - called from main application"""
    await profile_endpoint.setup_routes(app)