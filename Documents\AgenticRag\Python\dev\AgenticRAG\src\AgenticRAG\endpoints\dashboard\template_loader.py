from imports import *
from os import path as os_path
from typing import Dict, Any

class DashboardTemplateLoader:
    """Template loader for dashboard HTML templates and static assets"""
    
    def __init__(self):
        self.template_dir = os_path.join(os_path.dirname(__file__), 'templates')
        self.static_dir = os_path.join(os_path.dirname(__file__), 'static')
    
    def load_template(self, template_name: str) -> str:
        """Load HTML template from templates directory"""
        template_path = os_path.join(self.template_dir, template_name)
        
        if not os_path.exists(template_path):
            raise FileNotFoundError(f"Template not found: {template_path}")
        
        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def render_template(self, template_name: str, **context) -> str:
        """
        Load template and render with context variables.
        Uses simple string formatting for template variables.
        """
        template_content = self.load_template(template_name)
        
        # Simple template variable substitution
        try:
            return template_content.format(**context)
        except KeyError as e:
            LogFire.log("ERROR", f"Missing template variable: {e}")
            return template_content
    
    def render_jinja_template(self, template_name: str, **context) -> str:
        """
        Advanced template rendering using Jinja2-like syntax.
        Handles {{ variable }} and simple conditionals.
        """
        template_content = self.load_template(template_name)
        
        # Replace Jinja2-style variables with Python format strings
        import re
        
        # Convert {{ variable }} to {variable}
        template_content = re.sub(r'\{\{\s*(\w+(?:\.\w+)*)\s*\}\}', r'{\1}', template_content)
        
        # Handle simple conditionals like {{ var | default('default_value') }}
        def replace_default(match):
            var_path = match.group(1)
            default_val = match.group(2).strip('\'"')
            
            # Navigate nested attributes
            try:
                value = context
                for part in var_path.split('.'):
                    value = value[part] if isinstance(value, dict) else getattr(value, part)
                return str(value) if value is not None else default_val
            except (KeyError, AttributeError):
                return default_val
        
        template_content = re.sub(
            r'\{\{\s*(\w+(?:\.\w+)*)\s*\|\s*default\([\'"]([^\'"]*)[\'\"]\)\s*\}\}',
            replace_default,
            template_content
        )
        
        # Handle method calls like {{ var.upper() }}
        def replace_method_call(match):
            var_path = match.group(1)
            method_name = match.group(2)
            
            try:
                value = context
                for part in var_path.split('.'):
                    value = value[part] if isinstance(value, dict) else getattr(value, part)
                
                if hasattr(value, method_name):
                    result = getattr(value, method_name)()
                    return str(result)
                return str(value)
            except (KeyError, AttributeError):
                return f"{{{var_path}.{method_name}()}}"
        
        template_content = re.sub(
            r'\{\{\s*(\w+(?:\.\w+)*)\.(\w+)\(\)\s*\}\}',
            replace_method_call,
            template_content
        )
        
        # Final substitution for remaining variables
        try:
            return template_content.format(**self._flatten_context(context))
        except (KeyError, ValueError) as e:
            LogFire.log("ERROR", f"Template rendering error: {e}")
            return template_content
    
    def _flatten_context(self, context: Dict[str, Any], prefix: str = '') -> Dict[str, Any]:
        """Flatten nested context for template variable access"""
        flat = {}
        
        for key, value in context.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                flat.update(self._flatten_context(value, full_key))
            else:
                flat[full_key] = value
        
        return flat
    
    def get_static_url(self, static_path: str) -> str:
        """Generate URL for static assets"""
        return f"/dashboard/static/{static_path}"
    
    def inject_menu_template(self, html_content: str) -> str:
        """Inject menu template into main HTML content"""
        try:
            menu_html = self.load_template('menu.html')
            
            # Insert menu before closing body tag
            if '</body>' in html_content:
                insertion_point = html_content.rfind('</body>')
                return html_content[:insertion_point] + menu_html + html_content[insertion_point:]
            else:
                return html_content + menu_html
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to inject menu template: {str(e)}")
            return html_content

# Singleton instance
_template_loader = None

def get_dashboard_template_loader() -> DashboardTemplateLoader:
    """Get singleton instance of dashboard template loader"""
    global _template_loader
    if _template_loader is None:
        _template_loader = DashboardTemplateLoader()
    return _template_loader