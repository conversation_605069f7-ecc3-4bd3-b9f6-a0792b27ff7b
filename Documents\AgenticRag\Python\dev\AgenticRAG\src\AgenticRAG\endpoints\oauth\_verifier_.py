from imports import *

from os import path as os_path
from typing import Optional
from json import dump as json_dump
from aiohttp import web, ClientSession
from aiohttp_oauth2_client.grant.authorization_code import AuthorizationCodeGrant
from urllib.parse import urlencode
from typing import TYPE_CHECKING, Literal
from datetime import datetime, timezone
from os import getcwd

from managers.manager_postgreSQL import PostgreSQLManager
from endpoints.oauth.commands import oauth_handle_commands

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from aiohttp_oauth2_client.client import OAuth2Client

class OAuth2App:
    identifier: str = ""
    scopes: list[str] = []
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    auth_url: Optional[str] = None
    token_url: Optional[str] = None
    meltano_env: dict = {}
    commands: list[str] = []
    section: str = ""
    status: str = "idle" # idle, running, error, complete, or return value
    has_input: bool = False
    has_oauth: bool = False
    save_to_sql: bool = True
    save_to_env: bool = True

    def __init__(self, myname: str):
        self.scopes = []
        self.setup(myname)

    def setup(self, myname: str):
        self.identifier = myname

    def create_input(self, section: Literal["", "comm", "input", "auto", "debug"], input_fields: list[str], admin_only: bool = False) -> "OAuth2App":
        """strings saved in order: access_token, refresh_token, token_type, str1, str2, str3, str4, str5, str6, str7.
            Integers saved in order: expires_in, refresh_token_expires_in, int1, int2, int3, int4, int5, int6, int7, int8
            
            If create_oauth has also been called, the token-specific fields are skipped"""
        if isinstance(input_fields, str):
            input_fields = [input_fields]
        self.section = section if section else self.section
        self.scopes.extend(input_fields)
        self.has_input = True
        return self

    def create_oauth(self, section: Literal["", "comm", "input", "auto", "debug"], scopes: list[str], client_id: str, client_secret: str, auth_url: str, token_url: str, admin_only:bool = False) -> "OAuth2App":
        if isinstance(scopes, str):
            scopes = [scopes]
        self.section = section if section else self.section
        self.scopes.extend(scopes)
        self.client_id = client_id
        self.client_secret = client_secret
        self.auth_url = auth_url
        self.token_url = token_url
        self.has_oauth = True
        return self

    def set_meltano(self, meltano_env: dict[str, list[str]]) -> "OAuth2App":
        if isinstance(meltano_env, str):
            meltano_env = [meltano_env]
        self.meltano_env = meltano_env
        return self

    def set_commands(self, commands: list[str]) -> "OAuth2App":
        self.commands = commands
        return self
    
    def get_status(self, request: web.Request) -> str:
        return self.status
    
    def get_status_html(self, request: web.Request) -> str:
        return web.Response(text=self.status)
    
    async def on_success_return(self, request: web.Request) -> str:
        return ""
    
    async def on_success_execute(self, request: web.Request) -> str:
        return ""
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        return self.status

class OAuth2Verifier:
    _instance = None
    _initialized = False

    apps: dict[str, OAuth2App] = {}

    bot_tokens: dict[str, dict[str, str]] = {}
    oauth_clients: dict[str, "OAuth2Client"] = {}

    default_value = {
        "access_token": "",
        "expires_in": 0,
        "token_type": "",
        "refresh_token": "",
        "refresh_token_expires_in": 0,
        "valid": True,
        "created_at": datetime.now(timezone.utc).replace(tzinfo=None),
        "str1": "",
        "str2": "",
        "str3": "",
        "str4": "",
        "str5": "",
        "str6": "",
        "str7": "",
        "int1": 0,
        "int2": 0,
        "int3": 0,
        "int4": 0,
        "int5": 0,
        "int6": 0,
        "int7": 0,
        "int8": 0,
    }

    def instantiate(self):
        from endpoints.oauth.discord import OAuth2Discord
        from endpoints.oauth.website import OAuth2Website
        from endpoints.oauth.imap import OAuth2IMAP
        from endpoints.oauth.gdrive import OAuth2GDrive
        from endpoints.oauth.debug import OAuth2Debug
        from endpoints.oauth.woocommerce import OAuth2Woocommerce
        from endpoints.oauth.whatsapp import OAuth2Whatsapp
        self.apps["discord"] = OAuth2Discord("discord")

        self.apps["website"] = OAuth2Website("website")
        self.apps["slack"] = OAuth2App("slack").create_oauth("comm", ["app_mentions:read", "chat:write", "channels:read", "channels:history"], SLACK_BOT_CLIENT_ID, SLACK_BOT_CLIENT_SECRET, "https://slack.com/oauth/v2/authorize", "https://slack.com/api/oauth.v2.access")
        self.apps["gcalendar"] = OAuth2App("gcalendar").create_oauth("input", ["https://www.googleapis.com/auth/calendar"], GOOGLE_BOT_CLIENT_ID, GOOGLE_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token")
        self.apps["gmail"] = OAuth2App("gmail").create_oauth("input", ["https://www.googleapis.com/auth/gmail.modify"], GOOGLE_BOT_CLIENT_ID, GOOGLE_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token")
        self.apps["gdrive"] = OAuth2GDrive("gdrive")
        self.apps["googleads"] = OAuth2App("googleads").create_oauth("input", ["https://www.googleapis.com/auth/adwords"], GOOGLE_ADS_BOT_CLIENT_ID, GOOGLE_ADS_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token") \
                                    .set_meltano({"TAP_GOOGLEADS_OAUTH_CREDENTIALS": "full_token", "TAP_GOOGLEADS_OAUTH_CREDENTIALS_REFRESH_TOKEN": "refresh_token"})
        self.apps["airtable"] = OAuth2App("airtable").create_input("input", "str:Access Token") \
                                    .set_meltano({"TAP_AIRTABLE_TOKEN": "access_token"})
        self.apps["imap"] = OAuth2IMAP("imap")
        self.apps["smtp"] = OAuth2App("smtp").create_input("auto", ["str:Server Naam", "int:Netwerk port", "str:E-mail adres", "str:E-mail wachtwoord"])
        self.apps["woocommerce"] = OAuth2Woocommerce("woocommerce")
        self.apps["whatsapp"] = OAuth2Whatsapp("whatsapp")
        self.apps["debug"] = OAuth2Debug("debug")
        self.apps["ZairaPrompts1"] = OAuth2App("ZairaPrompts1").create_input("debug", ["str:AskZaira Personality",
                                                                                       "str:Quick RAG Search",
                                                                                       "str:Quick LLM Search",
                                                                                       "str:Global Supervisor Prompt",
                                                                                       "str:Top Supervisor Prompt",
                                                                                       "str:Output Supervisor Prompt",
                                                                                       "str:Output Supervisor Processing",
                                                                                       "str:Output Supervisor Sender",]) \
                                    .set_meltano({
                                        "AskZaira_Prompt": "access_token",
                                        "Quick_RAG_Search": "refresh_token",
                                        "Quick_LLM_Search": "token_type",
                                        "Global_Supervisor_Prompt": "str1",
                                        "Top_Supervisor_Prompt": "str2",
                                        "Output_Supervisor_Prompt": "str3",
                                        "Output_Supervisor_Processing": "str4",
                                        "Output_Supervisor_Sender": "str5",
                                    })
        self.apps["ZairaPrompts2"] = OAuth2App("ZairaPrompts2").create_input("debug", ["str:Retrieval Supervisor",
                                                                                       "str:Retrieval RAG Task",
                                                                                       "str:Retrieval Web Search",
                                                                                       "str:Retrieval Filepath",]) \
                                    .set_meltano({
                                        "Supervisor_Retrieval": "access_token",
                                        "Task_Retrieval_RAG": "refresh_token",
                                        "Task_Retrieval_Web_Search": "token_type",
                                        "Task_Retrieval_FilePath": "str1",
                                        "Task_IMAP_Idle_Activate": "str2",
                                        "Task_Scheduled_Task_Manager": "str3",
                                    })
        self.apps["ZairaPrompts3"] = OAuth2App("ZairaPrompts3").create_input("debug", ["str:Google Drive",
                                                                                       "str:IMAP",
                                                                                       "str:Task Supervisor Email Writer",
                                                                                       "str:Task Agent Email Writer Writer",
                                                                                       "str:Task Supervisor Receipts Scanner",
                                                                                       "str:Task Agent Receipts Scanner Scanner",
                                                                                       "str:Task Agent Receipts Scanner Analyzer",
                                                                                       "str:Change Chat Session",]) \
                                    .set_meltano({
                                        "Task_GDrive": "access_token",
                                        "Task_IMAP": "refresh_token",
                                        "Task_EmailWriter_Supervisor": "token_type",
                                        "Task_EmailWriter_Agent": "str1",
                                        "Task_Receipts_Scanner_Supervisor": "str2",
                                        "Task_Receipts_Scanner_Agent_Scanner": "str3",
                                        "Task_Receipts_Scanner_Agent_Analyzer": "str4",
                                        "Task_Manage_Chat_Sessions": "str5",
                                    })
        self.apps["ZairaPrompts4"] = OAuth2App("ZairaPrompts4").create_input("debug", ["str:Output Processing Supervisor Language Verifier",
                                                                                       "str:Output Procesing Language Verifier Prompt Language Input",
                                                                                       "str:Output Processing Language Verifier Prompt Language Output",
                                                                                       "str:Output Sender Discord",
                                                                                       "str:Output Sender Mail",
                                                                                       "str:Output Sender HTTP",
                                                                                       "str:Output Sender Python",
                                                                                       "str:Output Sender Teams",
                                                                                       "str:Output Sender Whatsapp",]) \
                                    .set_meltano({
                                        "Output_Processing_Language_Verifier": "access_token",
                                        "Output_Processing_Language_Verifier_Detect_Input": "refresh_token",
                                        "Output_Processing_Language_Verifier_Detect_Output": "token_type",
                                        "Output_Sender_Discord": "str1",
                                        "Output_Sender_Mail": "str2",
                                        "Output_Sender_HTTP": "str3",
                                        "Output_Sender_Python": "str4",
                                        "Output_Sender_Teams": "str5",
                                        "Output_Sender_Whatsapp": "str6",
                                    })
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls) -> "OAuth2Verifier":
        return cls()

    @classmethod
    async def _create_oauth_table(cls):
        """Create the OAuth table if it doesn't exist"""
        try:
            # First check if connection exists, if not create it
            connection = await PostgreSQLManager.get_connection("vectordb")
            if connection is None:
                # Create database first if it doesn't exist
                await PostgreSQLManager.create_database("vectordb")
                # Then connect to it with pooling to avoid concurrency issues
                connection = await PostgreSQLManager.connect_to_database("vectordb", use_pool=True, min_size=2, max_size=10)
            
            create_table_query = """
            CREATE TABLE IF NOT EXISTS oauth (
                identifier VARCHAR(100) PRIMARY KEY,
                access_token TEXT,
                expires_in INTEGER,
                token_type VARCHAR(50),
                refresh_token TEXT,
                refresh_token_expires_in INTEGER,
                str1 TEXT,
                str2 TEXT,
                str3 TEXT,
                str4 TEXT,
                str5 TEXT,
                str6 TEXT,
                str7 TEXT,
                int1 INTEGER,
                int2 INTEGER,
                int3 INTEGER,
                int4 INTEGER,
                int5 INTEGER,
                int6 INTEGER,
                int7 INTEGER,
                int8 INTEGER,
                valid BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE INDEX IF NOT EXISTS idx_oauth_identifier ON oauth(identifier);
            CREATE INDEX IF NOT EXISTS idx_oauth_valid ON oauth(valid);
            """
            
            await connection.execute(create_table_query)
            LogFire.log("OAUTH", "OAuth database table created/verified")

            await PostgreSQLManager.close_connection("vectordb")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create OAuth table: {str(e)}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "OAuth2Verifier._create_oauth_table")

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        try:
            # Create OAuth table if needed
            await cls._create_oauth_table()
            instance.instantiate()
            from endpoints.api_endpoint import APIEndpoint
            from aiohttp_oauth2_client.client import OAuth2Client

            for identifier, app in instance.apps.items():
                if app.has_oauth:
                    # Create an OAuth2 Client if the bot keys are provided. Without them we want a html page for user input
                    myscope = ""
                    for scope in app.scopes:
                        input_field_check = scope.split(":", 1)[0]
                        if input_field_check == "int" or input_field_check == "str" or input_field_check == "bool":
                            pass
                        else:
                            myscope += f" {scope}"
                    myscope = myscope.lstrip()

                    redirect_url = (f"http://localhost:8084" if Globals.is_debug() else "https://oauth.askzaira.com") + f"/oauth_redirect"
                    network_name = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME", "") if Globals.is_docker() else "python"
                    params = {
                        "client_id": app.client_id,
                        "redirect_uri": redirect_url,
                        "scope": myscope,
                        "state": f"{network_name}-{identifier}", # TODO: Needs improved
                        "response_type": "code",
                        "access_type": "offline",
                        "prompt": "consent",
                    }
                    grant = AuthorizationCodeGrant(
                        client_id=app.client_id,
                        client_secret=app.client_secret,
                        token_url=f"{app.token_url}",
                        authorization_url=app.auth_url + ("&" if "?" in app.auth_url else "?") + urlencode(params),
                        scope=myscope,
                    )
                    client = OAuth2Client(
                        grant=grant,
                    )
                    instance.oauth_clients[identifier] = client
                    APIEndpoint.get_instance().aio_app.add_routes([
                        web.get(f"/oauth_redirect", instance.oauth_redirect_get),
                    ])
                if app.has_input:
                    APIEndpoint.get_instance().aio_app.add_routes([
                        web.post(f"/{identifier}/oauth_redirect", instance.oauth_redirect_post),
                    ])
                APIEndpoint.get_instance().aio_app.add_routes([
                    web.get(f"/{identifier}/oauth", instance.oauth),
                    web.get(f"/{identifier}/oauth/twice", instance.oauth_twice),
                    web.get(f"/{identifier}/oauth/status_wait", instance.wait_on_status),
                    web.get(f"/{identifier}/oauth/status_get", instance.apps[identifier].get_status_html)
                ])

            instance._initialized = True

        except Exception as error:
            etc.helper_functions.exception_triggered(error)

    @classmethod
    async def get_token(cls, identifier: str, token_key: str = "access_token") -> str:
        instance = cls.get_instance()
        token = await instance.get_full_token(identifier)
        if token:
            if token_key == "full_token":
                return token
            return token[token_key]
        return ""

    @classmethod
    async def get_full_token(cls, identifier: str) -> Optional[dict[str]]:
        """Returns a default token if save_to_sql is disabled"""
        instance = cls.get_instance()

        if identifier in instance.bot_tokens:
            return instance.bot_tokens[identifier]

        # First check if connection exists, if not create it
        connection = await PostgreSQLManager.get_connection("vectordb")
        if connection is None:
            # Create database first if it doesn't exist
            await PostgreSQLManager.create_database("vectordb")
            # Then connect to it with pooling to avoid concurrency issues
            await PostgreSQLManager.connect_to_database("vectordb", use_pool=True, min_size=2, max_size=10)
        
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM oauth WHERE identifier = $1 ORDER BY created_at DESC LIMIT 1;", [identifier])
        if result:
            row = result[0]
            token = {
                "access_token": row["access_token"],
                "expires_in": row["expires_in"],
                "token_type": row["token_type"],
                "refresh_token": row["refresh_token"],
                "refresh_token_expires_in": row["refresh_token_expires_in"],
                "str1": row["str1"],
                "str2": row["str2"],
                "str3": row["str3"],
                "str4": row["str4"],
                "str5": row["str5"],
                "str6": row["str6"],
                "str7": row["str7"],
                "int1": row["int1"],
                "int2": row["int2"],
                "int3": row["int3"],
                "int4": row["int4"],
                "int5": row["int5"],
                "int6": row["int6"],
                "int7": row["int7"],
                "int8": row["int8"],
                "valid": row["valid"],
                "created_at": datetime.now(timezone.utc)
            }
            instance.bot_tokens[identifier] = token
            await PostgreSQLManager.close_connection("vectordb")
            return instance.bot_tokens[identifier]

        await PostgreSQLManager.close_connection("vectordb")
        return None

    @classmethod
    def get_client(cls, identifier: str) -> "OAuth2Client":
        return cls.get_instance().oauth_clients[identifier]

    async def save_tokens(self, identifier, tokens):
        """Returns True if server is still running after commands"""
        ret_val = True
        tokens["valid"] = True
        if self.apps[identifier].save_to_sql:
            # First check if connection exists, if not create it
            connection = await PostgreSQLManager.get_connection("vectordb")
            if connection is None:
                # Create database first if it doesn't exist
                await PostgreSQLManager.create_database("vectordb")
                # Then connect to it with pooling to avoid concurrency issues
                await PostgreSQLManager.connect_to_database("vectordb", use_pool=True, min_size=2, max_size=10)
            query = """
            INSERT INTO oauth (
                identifier,
                access_token,
                expires_in,
                token_type,
                refresh_token,
                refresh_token_expires_in,
                str1,
                str2,
                str3,
                str4,
                str5,
                str6,
                str7,
                int1,
                int2,
                int3,
                int4,
                int5,
                int6,
                int7,
                int8,
                valid,
                created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23)
            ON CONFLICT (identifier)
            DO UPDATE SET
                access_token = EXCLUDED.access_token,
                expires_in = EXCLUDED.expires_in,
                token_type = EXCLUDED.token_type,
                refresh_token = EXCLUDED.refresh_token,
                refresh_token_expires_in = EXCLUDED.refresh_token_expires_in,
                str1 = EXCLUDED.str1,
                str2 = EXCLUDED.str2,
                str3 = EXCLUDED.str3,
                str4 = EXCLUDED.str4,
                str5 = EXCLUDED.str5,
                str6 = EXCLUDED.str6,
                str7 = EXCLUDED.str7,
                int1 = EXCLUDED.int1,
                int2 = EXCLUDED.int2,
                int3 = EXCLUDED.int3,
                int4 = EXCLUDED.int4,
                int5 = EXCLUDED.int5,
                int6 = EXCLUDED.int6,
                int7 = EXCLUDED.int7,
                int8 = EXCLUDED.int8,
                valid = EXCLUDED.valid,
                created_at = EXCLUDED.created_at;
            """
            params = [
                identifier,
                tokens['access_token'],
                tokens['expires_in'],
                tokens['token_type'],
                tokens['refresh_token'],
                tokens['refresh_token_expires_in'],
                tokens['str1'],
                tokens['str2'],
                tokens['str3'],
                tokens['str4'],
                tokens['str5'],
                tokens['str6'],
                tokens['str7'],
                tokens['int1'],
                tokens['int2'],
                tokens['int3'],
                tokens['int4'],
                tokens['int5'],
                tokens['int6'],
                tokens['int7'],
                tokens['int8'],
                tokens['valid'],
                tokens['created_at'],
            ]
            await PostgreSQLManager.execute_query("vectordb", query, params)
            await PostgreSQLManager.close_connection("vectordb")

        if self.apps[identifier].save_to_env:
            self.save_to_env(identifier, tokens)
        self.bot_tokens[identifier] = tokens

        LogFire.log("RETRIEVE", f"Identifier saved: {identifier}.", str(tokens))
        return await oauth_handle_commands(self, self.apps[identifier], tokens)

    def save_to_env(self, identifier, tokens):
        # Add the token value to the .env file
        app = self.apps[identifier]
        items = {}
        if len(app.meltano_env) > 0:
            for key, value in app.meltano_env.items():
                if value == "full_token":
                    new_token = tokens
                elif value in tokens:
                    # If the new value is identical to memory, simpy do not store the value to the .env
                    # Feels unneeded, but this way default prompts don't make it to the config file
                    if tokens[value] != self.bot_tokens[identifier][value]:
                        new_token = tokens[value]
                    else:
                        continue
                else:
                    continue
                items[key] = new_token
            etc.helper_functions.save_to_env(items)

    async def invalidate_tokens(self, identifier):
        tokens = await self.get_full_token(identifier)
        tokens["valid"] = False
        with open(f'./tokens/{identifier}.json', "w") as f:
            json_dump(tokens, f)

    async def load_content_oauth_input_fields(self, request: web.Request):
        identifier = request.rel_url.parts[1]
        app = self.apps[identifier]
        token_int_values = ["expires_in", "refresh_token_expires_in", "int1", "int2", "int3", "int4", "int5", "int6", "int7", "int8"]
        token_string_values = ["access_token", "refresh_token", "token_type", "str1", "str2", "str3", "str4", "str5", "str6", "str7"]
        if app.client_id:
            token_int_values = ["int1", "int2", "int3", "int4", "int5", "int6", "int7", "int8"]
            token_string_values = ["str1", "str2", "str3", "str4", "str5", "str6", "str7"]
        # <form action="/{identifier}/oauth_redirect" method="post">
        # <div class="form-row">
        html_content = f"""
            <div class="card-header">
                <h2 class="card-title">Server Configuratie</h2>
                <p class="card-subtitle">Voer je {identifier.upper()} instellingen in om door te gaan naar het dashboard</p>
            </div>
            <form class="form-container" id="setupForm">
                <input type="hidden" id="identifier" name="identifier" value="{identifier}">"""
        int_id = 0
        str_id = 0
        for scope in app.scopes:
            input_field_check = scope.split(":", 1)[0]
            if input_field_check == "int":
                is_int = "integer"
            elif input_field_check == "str":
                is_int = "text"
            elif input_field_check == "bool":
                is_int = "checkbox"
            else:
                continue
            scope = scope.split(":")[1]
            if is_int == "integer":
                token_value = str(await self.get_token(identifier, token_int_values[int_id]))
                int_id += 1
            else:
                token_value = await self.get_token(identifier, token_string_values[str_id])
                str_id += 1
            html_content += f"""
                            <div class="form-group">
                                <label class="form-label" for="{scope}">{scope.replace("_", " ")}</label>
                                <input 
                                    type="{f'{is_int}' if not 'wachtwoord' in scope.lower() else 'password'}" 
                                    id="{scope}"
                                    name="{scope}"
                                    class="form-input"
                                    placeholder="{""}"
                                    value="{token_value}"
                                    {"" if ("Optional" in scope) else "required"}
                                >
                                <div class="form-help">{""}</div>
                                <div class="form-error" id="serverNameError">Vul een geldige waarde in</div>
                            </div>
                            """
        # Temporarily add a password verification
        html_content += f"""
                        <div class="form-group">
                            <label class="form-label" for="username" style="display: none;"></label>
                            <input 
                                type="text" 
                                id="username" 
                                name="username" 
                                class="form-input"
                                autocomplete="username"
                                style="position: absolute; left: -9999px; top: -9999px;" 
                                tabindex="-1"
                                aria-hidden="true"
                            >
                            <div class="form-help">{""}</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="userpass">Bevestig met AskZaira admin wachtwoord</label>
                            <input 
                                type="{'password'}" 
                                id="userpass" 
                                name="userpass" 
                                class="form-input" 
                                {"required"}
                            >
                            <div class="form-help">{""}</div>
                            <div class="form-error" id="serverNameError">Vul een geldige waarde in</div>
                        </div>
                        """
        
        html_content += """
                    <button type="button" onclick="window.history.back();" class="back-button" id="cancelButton">
                        Annuleer
                    </button>
                    <button type="submit" class="submit-button" id="submitButton">
                        <span class="loading"></span>
                        <span class="button-text">Configuratie Voltooien</span>
                    </button>
                </form>
        """
        return html_content
    
    async def wait_on_status(self, request: web.Request):
        identifier = request.rel_url.parts[1]
        try:
            if request.content_type == 'application/json':
                data = await request.json()
            else:
                data = await request.post()
        except Exception:
            return web.json_response({"error": "Invalid Post", "success": False}, status=400)
        # Execute OAuth-specific and create return page
        content = await self.apps[identifier].on_success_return(request)
        if content != "":
            content = "<div id='status'><br /><br /><div class='spinner-container'><div class='spinner'></div></div><br /><br />" + content + "</div>"
            self.apps[identifier].status = "running"
            async def delayed_execute():
                try:
                    self.apps[identifier].status = await self.apps[identifier].on_success_execute(request)
                except Exception as e:
                    self.apps[identifier].status = "error: " + str(e)
                    self.apps[identifier].status = await self.apps[identifier].on_success_execute_fail(request)
                    etc.helper_functions.exception_triggered(e)
                if self.apps[identifier].status == "":
                    self.apps[identifier].status = f"""
                                                    <p>Token succesvol opgeslagen en verwerkt! Klik <a href='{target_url}'>hier</a> om terug te gaan naar de OAuth koppeling pagina als dit niet automatisch gebeurt.</p>
                                                    <script>
                                                        // Redirect after 10 seconds
                                                        setTimeout(function() {{
                                                            window.location.href = "{target_url}";
                                                        }}, 10000);
                                                    </script>
                                                    """
            from asyncio import create_task
            create_task(delayed_execute())
            status_url = f"/{identifier}/oauth/status_get"
            target_url = f"{Globals.get_endpoint_address()}/login"
            content +=  f"""
                        <script>
                            const checkStatus = async () => {{
                                try {{
                                    const response = await fetch("{status_url}");
                                    const status = await response.text();

                                    if (status.trim().toLowerCase() !== "running") {{
                                        if (status.trim().toLowerCase() === "idle") {{
                                            document.getElementById("status").innerHTML = "Token succesvol opgeslagen! Klik <a href='{target_url}'>hier</a> om terug te gaan naar de OAuth koppeling pagina als dit niet automatisch gebeurt.";
                                            // Redirect after 10 seconds
                                            setTimeout(function() {{
                                                window.location.href = "{target_url}";
                                            }}, 10000);
                                        }} else if (status.trim().toLowerCase().indexOf("error: ") === 0) {{
                                            document.getElementById("status").innerHTML = status.trim() + "<br /><br /> Er is iets fout gegaan met het verwerken van de authorisatie.<br /><br />Klik <a href='{target_url}'>hier</a> om terug te gaan naar de OAuth koppeling pagina om het opnieuw te proberen. Neem contact op met AskZaira mocht deze fout blijven voorkomen.";
                                        }} else {{
                                            document.getElementById("status").innerHTML = status.trim() + " Klik <a href='{target_url}'>hier</a> om terug te gaan naar de OAuth koppeling pagina als dit niet automatisch gebeurt.";
                                            // Redirect after 10 seconds
                                            setTimeout(function() {{
                                                window.location.href = "{target_url}";
                                            }}, 10000);
                                        }}
                                        clearInterval(intervalId);
                                    }}
                                }} catch (error) {{
                                    console.error("Error checking status:", error);
                                }}
                            }};

                            // Check every 1.5 seconds
                            const intervalId = setInterval(checkStatus, 2000);
                        </script>
                        """
            html_content = etc.helper_functions.create_html_out("inputfield", content)
            return web.Response(text=html_content, content_type='text/html')
        else:
            target_url = f"{Globals.get_endpoint_address()}/login"
            content = f"""
                <p>Token succesvol opgeslagen! Klik <a href='{target_url}'>hier</a> om terug te gaan naar de OAuth koppeling pagina als dit niet automatisch gebeurt.</p>
                <script>
                    // Redirect after 1.5 seconds
                    setTimeout(function() {{
                        window.location.href = "{target_url}";
                    }}, 1500);
                </script>
            """
        
        html_content = etc.helper_functions.create_html_out("inputfield", content)

        return web.Response(text=html_content, content_type='text/html')
    
    async def handle_oauth_endpoint(self, request: web.Request, second_call: bool = False):
        print(f"Endpoint oauth called from IP: {request['real_ip']}")
        identifier = request.rel_url.parts[1]
        client = self.oauth_clients.get(identifier)
        if self.apps[identifier].has_oauth and second_call == False:
            self.apps[identifier].status = "idle"

            authorization_url = client.grant.authorization_url
            #return web.Response(status=302,headers={'Location':str(authorization_url)})

            html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8" />
                <title>Redirecting...</title>
                <script>
                    // Redirect immediately
                    window.location.href = "{authorization_url}";
                </script>
            </head>
            <body>
                <p>Token aanvragen... Klik <a href='{authorization_url}'>hier</a> als dit niet automatisch laadt.</p>
            </body>
            </html>
            """
            return web.Response(text=html_content, content_type='text/html')
        elif self.apps[identifier].has_input:
            content = await self.load_content_oauth_input_fields(request)
            site = etc.helper_functions.create_html_out("inputfield", content)
            return web.Response(text=site, content_type="text/html")

    async def oauth(self, request: web.Request):
        return await self.handle_oauth_endpoint(request)
    
    async def oauth_twice(self, request: web.Request):
        return await self.handle_oauth_endpoint(request, second_call=True)

    async def oauth_redirect_post(self, request: web.Request):
        identifier = request.rel_url.parts[1]
        try:
            if request.content_type == 'application/json':
                data = await request.json()
            else:
                data = await request.post()
        except Exception:
            return web.json_response({"error": "Invalid Post", "success": False}, status=400)
        
        # Temporary until user login remains saved
        from hashlib import md5
        username = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME", "") if Globals.is_docker() else "proxyhttpaio"
        md5pass = md5(("!askzaira#" + username + "-askzaira=").encode('utf-8')).hexdigest()
        datapass = data.get('userpass')
        if md5pass != datapass:
            return web.json_response({"error": "Invalid Post", "success": False}, status=400)

        app = self.apps[identifier]
        string_id = 0
        int_id = 0
        old_token = await self.get_token(identifier, "full_token")
        save_value = self.default_value | old_token if old_token else self.default_value
        token_int_values = ["expires_in", "refresh_token_expires_in", "int1", "int2", "int3", "int4", "int5", "int6", "int7", "int8"]
        token_string_values = ["access_token", "refresh_token", "token_type", "str1", "str2", "str3", "str4", "str5", "str6", "str7"]
        if app.client_id:
            token_int_values = ["int1", "int2", "int3", "int4", "int5", "int6", "int7", "int8"]
            token_string_values = ["str1", "str2", "str3", "str4", "str5", "str6", "str7"]
        for scope in app.scopes:
            input_field_check = scope.split(":", 1)[0]
            value = data.get(scope.split(":")[1].replace(" ", ""))

            if input_field_check == "int":#fullmatch(r"-?\d+", value):  # Matches optional minus sign and digits
                if (not "Optional" in scope) and not value:
                    return web.json_response({"error": "Alle waardes dienen ingevoerd te worden."}, status=400)
                save_value[token_int_values[int_id]] = int(value)
                int_id += 1
            elif input_field_check == "str":
                if (not "Optional" in scope) and not value:
                    return web.json_response({"error": "Alle waardes dienen ingevoerd te worden."}, status=400)
                save_value[token_string_values[string_id]] = value
                string_id += 1
            elif input_field_check == "bool":
                # Booleans are saved as integers
                if (not "Optional" in scope) and not value:
                    return web.json_response({"error": "Alle waardes dienen ingevoerd te worden."}, status=400)
                save_value[token_int_values[int_id]] = (1 if value == True else 0)
                int_id += 1
            else:
                # Value isn't an input field
                continue

        if await self.save_tokens(identifier, save_value):
            return web.json_response({"success": True}, status=200)
        else:
            return web.json_response({"success": True}, status=200)

    async def oauth_redirect_get(self, request: web.Request):
        if "state" in request.query:
            identifier = request.query.get("state").split("-", 1)[1]
        else:
            identifier = request.rel_url.parts[1]
        client = self.oauth_clients.get(identifier)
        app = self.apps[identifier]
        if not client:
                html_content = etc.helper_functions.create_html_out("inputfield", f"""OAuth2 client not found for {identifier}. Klik <a href='{Globals.get_endpoint_address() + "/login"}'>hier</a> om het opnieuw te proberen.""")
                return web.Response(text=html_content, type="text/html", status=404)
        try:
            # Currently as first, probably should be inside the 'not code or not scope' if statement
            if request.can_read_body == True and request.text() != "":
                response_data = await request.json()
                token = self.default_value | response_data["token"]
                if not token:
                    return web.Response(text="Token not received", status=400)

                self.bot_tokens[identifier] = {"access_token":token}
                await self.save_tokens(identifier, self.bot_tokens[identifier])

                return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_return")
            else:
                code = request.query.get("code", "")
                scopes = request.query.get("scope", "")
                permissions = request.query.get("permissions") # Currently Discord-specific. Code needs expanded if more than 1 OAuth fails to deliver the scope
                if not code or (not scopes and not permissions):
                    html_content = etc.helper_functions.create_html_out("inputfield", f"""Error: Geen code of scope meeverstuurd. Klik <a href='{Globals.get_endpoint_address() + "/login"}'>hier</a> om het opnieuw te proberen.""")
                    return web.Response(text=html_content, content_type='text/html')
                redirect_url = (f"http://localhost:8084" if Globals.is_debug() else f"https://oauth.askzaira.com") + f"/oauth_redirect"
                async with ClientSession() as session:
                    async with session.post(
                        app.token_url,
                        data={
                            "grant_type": "authorization_code",
                            "code": code,
                            "redirect_uri": redirect_url,
                            "client_id": app.client_id,
                            "client_secret": app.client_secret,
                        }
                    ) as resp:
                        html_content = f"""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8" /><title>Er is iets fout gegaan...</title></head><body>
                                        <p>Authenticatie successvol binnengekomen maar scopes niet kunnen verifiëren. Klik <a href='{Globals.get_endpoint_address() + '/login'}'>hier</a> om het opnieuw te proberen.</p>
                                        </body></html>"""
                        if resp.status != 200:
                            error_text = await resp.text()
                            print(f"Token request failed: {error_text}")
                            return web.Response(text=error_text, status=resp.status, content_type='application/json')
                        tokens = await resp.json()
                        # Check if all scopes have been granted
                        myscope = []
                        for scope in app.scopes:
                            input_field_check = scope.split(":", 1)[0]
                            if input_field_check == "int" or input_field_check == "str" or input_field_check == "bool":
                                pass
                            else:
                                myscope.append(scope)
                        if (set(tokens["scope"].split()) == set(scopes.split()) and set(tokens["scope"].split()) == set(myscope)) or permissions == "274877975616":
                            guild_id = request.query.get("guild_id", "") # Specific to Discord
                            if guild_id != "":
                                tokens["token_type"] = guild_id
                                #self.save_to_env("discord_guild_id", {"discord_guild_id": guild_id}, "/app/.env") # Only works from within Docker
                            save_value = {
                                "access_token": tokens["access_token"],
                                "expires_in": tokens["expires_in"],
                                "token_type": tokens["token_type"],
                                "refresh_token": tokens["refresh_token"],
                                "refresh_token_expires_in": tokens["refresh_token_expires_in"] if "refresh_token_expires_in" in tokens else 0,
                                "valid": True,
                                "created_at": datetime.now(timezone.utc).replace(tzinfo=None)
                            }
                            old_token = await self.get_token(identifier, "full_token")
                            old_token = self.default_value | old_token if old_token else self.default_value
                            if await self.save_tokens(identifier, old_token | save_value): # False if server is rebooting
                                if app.has_input:
                                    return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/{identifier}/oauth/twice")
                                else:
                                    return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/{identifier}/oauth/status_wait")
                            else:
                                target_url = f"{Globals.get_endpoint_address()}/login"
                                content = f"""
                                    <p>Token succesvol opgeslagen, server wordt herstart! Klik <a href='{target_url}'>hier</a> om terug te gaan naar de OAuth koppeling pagina als dit niet automatisch gebeurt.</p>
                                    <script>
                                        // Redirect after 60 seconds
                                        setTimeout(function() {{
                                            window.location.href = "{target_url}";
                                        }}, 60000);
                                    </script>
                                """
                                html_content = etc.helper_functions.create_html_out("inputfield", content)

                                return web.Response(text=html_content, content_type='text/html')
                        else:
                            html_content = etc.helper_functions.create_html_out("inputfield", f"<p>Token aanvraag gefaald: Alle verzochte permissies moeten aangevinkt worden. Klik <a href='{Globals.get_endpoint_address() + '/login'}'>hier</a> om het opnieuw te proberen.</p>")
                        return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            etc.helper_functions.handle_asyncio_task_result_errors(e)
            site = etc.helper_functions.create_html_out("inputfield", f"""OAuth error: {str(e)}. Klik <a href='{Globals.get_endpoint_address() + "/login"}'>hier</a> om het opnieuw te proberen.""")
            return web.Response(text=site, content_type='text/html', status=500)
