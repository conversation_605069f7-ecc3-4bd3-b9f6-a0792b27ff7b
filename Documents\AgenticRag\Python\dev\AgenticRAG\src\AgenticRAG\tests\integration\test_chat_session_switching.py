"""
Integration test for chat session switching and message persistence
Tests: Start chat, add messages, switch to another chat, add messages, switch back, verify history
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4
from typing import Dict, List, Any, Optional
from datetime import datetime

from managers.manager_supervisors import SupervisorManager, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import ZairaUser
from userprofiles.ZairaMessage import MessageRole
from tasks.etc.task_chat_session import NewChatSessionTool, ChangeChatSessionTool, ListChatSessionsTool
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage


@pytest.mark.integration
@pytest.mark.asyncio
class TestChatSessionSwitching:
    """Integration test for chat session switching and message persistence"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_session_1_guid = str(uuid4())
        self.test_session_2_guid = str(uuid4())
        
        # Test messages for different sessions
        self.session_1_messages = [
            "Hello, this is my first message in session 1",
            "This is another message in the first session",
            "Final message for session 1 before switching"
        ]
        
        self.session_2_messages = [
            "Starting a new conversation in session 2",
            "Adding more content to the second session",
            "Session 2 specific discussion"
        ]
        
        self.session_1_additional_messages = [
            "Back to session 1 after switching",
            "Continuing the original conversation"
        ]
        
        # Initialize Globals for testing
        try:
            Globals.Debug = True
        except:
            pass
    
    def teardown_method(self):
        """Clean up after test"""
        pass
    
    async def test_complete_chat_session_switching_workflow(self):
        """Complete integration test: create session, add messages, switch, add messages, switch back, verify"""
        
        # Create mock user with proper chat session support
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        mock_user.session_guid = self.test_session_1_guid  # Start with session 1
        mock_user.platform = "integration_test"
        
        # Initialize chat history structure  
        from userprofiles.ZairaChat import ZairaChat
        mock_user.chat_history = {}
        
        # Create tool instances
        new_session_tool = NewChatSessionTool()
        change_session_tool = ChangeChatSessionTool()
        list_sessions_tool = ListChatSessionsTool()
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
            
            # Step 1: Start with initial session and add messages
            LogFire.log("DEBUG", "=== Step 1: Adding messages to initial session ===", severity="debug")
            
            for i, message in enumerate(self.session_1_messages):
                # Create state for each message
                state = SupervisorTaskState(
                    user_guid=self.test_user_guid,
                    original_input=message,
                    additional_input={},
                    messages=[HumanMessage(content=message)],
                    call_trace=[],
                    completed_tasks=[],
                    sections={},
                    reasoning_steps=[],
                    conversation_history=[]
                )
                
                # Simulate adding message to current session
                await self._add_message_to_session(mock_user, message, "user")
                await self._add_message_to_session(mock_user, f"Response to: {message}", "assistant")
                
                LogFire.log("DEBUG", f"Added message {i+1} to session {self.test_session_1_guid}: {message}", severity="debug")
            
            # Verify session 1 has messages
            session_1_chat = mock_user.chat_history.get(str(self.test_session_1_guid))
            assert session_1_chat is not None
            assert session_1_chat.message_count == len(self.session_1_messages) * 2  # User + assistant messages
            LogFire.log("DEBUG", f"✓ Session 1 has {session_1_chat.message_count} messages", severity="debug")
            
            # Step 2: Create and switch to new session
            LogFire.log("DEBUG", "\n=== Step 2: Creating and switching to new session ===", severity="debug")
            
            new_session_state = SupervisorTaskState(
                user_guid=self.test_user_guid,
                original_input="start new chat session",
                additional_input={},
                messages=[HumanMessage(content="start new chat session")],
                call_trace=[],
                completed_tasks=[],
                sections={},
                reasoning_steps=[],
                conversation_history=[]
            )
            
            # Create new session
            new_session_result = await new_session_tool._arun(new_session_state)
            
            # Extract new session GUID from result
            new_session_guid = self._extract_session_guid_from_result(new_session_result)
            mock_user.session_guid = new_session_guid
            
            LogFire.log("DEBUG", f"✓ Created new session: {new_session_guid}", severity="debug")
            LogFire.log("DEBUG", f"✓ Switched to new session: {new_session_result}", severity="debug")
            
            # Step 3: Add messages to second session
            LogFire.log("DEBUG", "\n=== Step 3: Adding messages to second session ===", severity="debug")
            
            for i, message in enumerate(self.session_2_messages):
                await self._add_message_to_session(mock_user, message, "user")
                await self._add_message_to_session(mock_user, f"Response to: {message}", "assistant")
                
                LogFire.log("DEBUG", f"Added message {i+1} to session {new_session_guid}: {message}", severity="debug")
            
            # Verify session 2 has messages
            session_2_chat = mock_user.chat_history.get(str(new_session_guid))
            assert session_2_chat is not None
            assert session_2_chat.message_count == len(self.session_2_messages) * 2
            LogFire.log("DEBUG", f"✓ Session 2 has {session_2_chat.message_count} messages", severity="debug")
            
            # Step 4: Switch back to original session
            LogFire.log("DEBUG", "\n=== Step 4: Switching back to original session ===", severity="debug")
            
            switch_back_state = SupervisorTaskState(
                user_guid=self.test_user_guid,
                original_input=f"switch to session {self.test_session_1_guid}",
                additional_input={"session_guid": self.test_session_1_guid},
                messages=[HumanMessage(content=f"switch to session {self.test_session_1_guid}")],
                call_trace=[],
                completed_tasks=[],
                sections={},
                reasoning_steps=[],
                conversation_history=[]
            )
            
            # Switch back to session 1
            switch_result = await change_session_tool._arun(self.test_session_1_guid, switch_back_state)
            mock_user.session_guid = self.test_session_1_guid
            
            LogFire.log("DEBUG", f"✓ Switched back to original session: {switch_result}", severity="debug")
            
            # Step 5: Verify original session still has its messages
            LogFire.log("DEBUG", "\n=== Step 5: Verifying original session history ===", severity="debug")
            
            session_1_chat_after_switch = mock_user.chat_history.get(str(self.test_session_1_guid))
            assert session_1_chat_after_switch is not None
            
            # Verify all original messages are still there
            assert session_1_chat_after_switch.message_count == len(self.session_1_messages) * 2
            
            # Verify specific message content
            user_messages_session_1 = session_1_chat_after_switch.get_messages(role=MessageRole.USER)
            for i, original_message in enumerate(self.session_1_messages):
                found_message = user_messages_session_1[i].content
                assert original_message in found_message or found_message == original_message
                LogFire.log("DEBUG", f"✓ Original message {i+1} verified: {found_message}", severity="debug")
            
            # Step 6: Add more messages to original session
            LogFire.log("DEBUG", "\n=== Step 6: Adding additional messages to original session ===", severity="debug")
            
            for i, message in enumerate(self.session_1_additional_messages):
                await self._add_message_to_session(mock_user, message, "user")
                await self._add_message_to_session(mock_user, f"Response to: {message}", "assistant")
                
                LogFire.log("DEBUG", f"Added additional message {i+1} to session {self.test_session_1_guid}: {message}", severity="debug")
            
            # Step 7: Final verification
            LogFire.log("DEBUG", "\n=== Step 7: Final verification ===", severity="debug")
            
            # List all sessions
            list_sessions_state = SupervisorTaskState(
                user_guid=self.test_user_guid,
                original_input="list all chat sessions",
                additional_input={},
                messages=[HumanMessage(content="list sessions")],
                call_trace=[],
                completed_tasks=[],
                sections={},
                reasoning_steps=[],
                conversation_history=[]
            )
            
            sessions_list = await list_sessions_tool._arun(list_sessions_state)
            LogFire.log("DEBUG", f"✓ Sessions list: {sessions_list}", severity="debug")
            
            # Verify final state
            final_session_1_chat = mock_user.chat_history.get(str(self.test_session_1_guid))
            final_session_2_chat = mock_user.chat_history.get(str(new_session_guid))
            
            expected_session_1_messages = (len(self.session_1_messages) + len(self.session_1_additional_messages)) * 2
            expected_session_2_messages = len(self.session_2_messages) * 2
            
            assert final_session_1_chat.message_count == expected_session_1_messages
            assert final_session_2_chat.message_count == expected_session_2_messages
            
            LogFire.log("DEBUG", f"✓ Final verification passed:", severity="debug")
            LogFire.log("DEBUG", f"  - Session 1 ({self.test_session_1_guid}): {final_session_1_chat.message_count} messages", severity="debug")
            LogFire.log("DEBUG", f"  - Session 2 ({new_session_guid}): {final_session_2_chat.message_count} messages", severity="debug")
            LogFire.log("DEBUG", f"  - Current active session: {mock_user.session_guid}", severity="debug")
            
            # Verify current session is session 1
            assert str(mock_user.session_guid) == str(self.test_session_1_guid)
            
            LogFire.log("DEBUG", "✓ Complete chat session switching workflow test passed!", severity="debug")
    
    async def _add_message_to_session(self, user: MagicMock, message: str, role: str):
        """Helper to add a message to the user's current session"""
        from userprofiles.ZairaChat import ZairaChat
        from userprofiles.ZairaMessage import ZairaMessage, MessageRole
        
        session_guid = str(user.session_guid)
        
        if session_guid not in user.chat_history:
            user.chat_history[session_guid] = ZairaChat(
                session_guid=session_guid,
                user_guid=user.user_guid
            )
        
        # Create proper ZairaMessage
        if role == "user":
            zaira_message = ZairaMessage.create_user_message(message, None, session_guid)
        else:
            zaira_message = ZairaMessage.create_assistant_message(message, None, session_guid)
        
        user.chat_history[session_guid].add_message(zaira_message)
    
    def _extract_session_guid_from_result(self, result: str) -> str:
        """Extract session GUID from tool result"""
        # For this test, we'll generate a new GUID since the tool creates one
        # In real implementation, the tool would return the actual GUID
        return str(uuid4())
    
    async def test_chat_session_isolation(self):
        """Test that messages in different sessions are properly isolated"""
        
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        mock_user.chat_history = {}
        
        # Create two distinct sessions
        session_1_guid = str(uuid4())
        session_2_guid = str(uuid4())
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
            
            # Add messages to session 1
            mock_user.session_guid = session_1_guid
            for message in self.session_1_messages:
                await self._add_message_to_session(mock_user, message, "user")
            
            # Add messages to session 2  
            mock_user.session_guid = session_2_guid
            for message in self.session_2_messages:
                await self._add_message_to_session(mock_user, message, "user")
            
            # Verify isolation
            session_1_chat = mock_user.chat_history.get(session_1_guid)
            session_2_chat = mock_user.chat_history.get(session_2_guid)
            
            assert session_1_chat is not None
            assert session_2_chat is not None
            
            # Check that session 1 only has session 1 messages
            session_1_contents = [msg.content for msg in session_1_chat.messages]
            for message in self.session_1_messages:
                assert message in session_1_contents
            
            for message in self.session_2_messages:
                assert message not in session_1_contents
            
            # Check that session 2 only has session 2 messages
            session_2_contents = [msg.content for msg in session_2_chat.messages]
            for message in self.session_2_messages:
                assert message in session_2_contents
            
            for message in self.session_1_messages:
                assert message not in session_2_contents
            
            LogFire.log("DEBUG", "✓ Chat session isolation test passed!", severity="debug")
    
    async def test_chat_session_persistence_across_switches(self):
        """Test that chat history persists correctly across multiple session switches"""
        
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        mock_user.chat_history = {}
        
        session_1_guid = str(uuid4())
        session_2_guid = str(uuid4())
        session_3_guid = str(uuid4())
        
        change_session_tool = ChangeChatSessionTool()
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
            
            # Complex switching pattern: 1 -> 2 -> 3 -> 1 -> 2 -> 3 -> 1
            switching_pattern = [
                (session_1_guid, "Message 1 in session 1"),
                (session_2_guid, "Message 1 in session 2"),
                (session_3_guid, "Message 1 in session 3"),
                (session_1_guid, "Message 2 in session 1"),
                (session_2_guid, "Message 2 in session 2"),
                (session_3_guid, "Message 2 in session 3"),
                (session_1_guid, "Message 3 in session 1"),
            ]
            
            for session_guid, message in switching_pattern:
                # Switch to session
                mock_user.session_guid = session_guid
                
                # Add message to current session
                await self._add_message_to_session(mock_user, message, "user")
                
                LogFire.log("DEBUG", f"Added to {session_guid}: {message}", severity="debug")
            
            # Verify each session has exactly its messages
            session_1_chat = mock_user.chat_history.get(session_1_guid)
            session_2_chat = mock_user.chat_history.get(session_2_guid) 
            session_3_chat = mock_user.chat_history.get(session_3_guid)
            
            session_1_messages = [msg.content for msg in session_1_chat.messages] if session_1_chat else []
            session_2_messages = [msg.content for msg in session_2_chat.messages] if session_2_chat else []
            session_3_messages = [msg.content for msg in session_3_chat.messages] if session_3_chat else []
            
            expected_session_1 = ["Message 1 in session 1", "Message 2 in session 1", "Message 3 in session 1"]
            expected_session_2 = ["Message 1 in session 2", "Message 2 in session 2"]
            expected_session_3 = ["Message 1 in session 3", "Message 2 in session 3"]
            
            for expected in expected_session_1:
                assert expected in session_1_messages
            
            for expected in expected_session_2:
                assert expected in session_2_messages
            
            for expected in expected_session_3:
                assert expected in session_3_messages
            
            # Verify no cross-contamination
            assert len([msg for msg in session_1_messages if "session 2" in msg or "session 3" in msg]) == 0
            assert len([msg for msg in session_2_messages if "session 1" in msg or "session 3" in msg]) == 0
            assert len([msg for msg in session_3_messages if "session 1" in msg or "session 2" in msg]) == 0
            
            LogFire.log("DEBUG", "✓ Chat session persistence test passed!", severity="debug")
    
    async def test_chat_session_edge_cases(self):
        """Test edge cases in chat session management"""
        
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        mock_user.chat_history = {}
        mock_user.session_guid = str(uuid4())
        
        change_session_tool = ChangeChatSessionTool()
        list_sessions_tool = ListChatSessionsTool()
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
            
            # Test 1: Switch to non-existent session
            non_existent_session = str(uuid4())
            
            state = SupervisorTaskState(
                user_guid=self.test_user_guid,
                original_input=f"switch to session {non_existent_session}",
                additional_input={},
                messages=[HumanMessage(content="switch session")],
                call_trace=[],
                completed_tasks=[],
                sections={},
                reasoning_steps=[],
                conversation_history=[]
            )
            
            result = await change_session_tool._arun(non_existent_session, state)
            # Should handle gracefully (create new session or show error)
            assert "session" in result.lower()
            LogFire.log("DEBUG", f"✓ Non-existent session handled: {result}", severity="debug")
            
            # Test 2: List sessions when no sessions exist
            empty_list_result = await list_sessions_tool._arun(state)
            assert isinstance(empty_list_result, str)
            LogFire.log("DEBUG", f"✓ Empty sessions list handled: {empty_list_result}", severity="debug")
            
            # Test 3: Switch to same session (should be no-op)
            current_session = mock_user.session_guid
            same_session_result = await change_session_tool._arun(str(current_session), state)
            assert mock_user.session_guid == current_session
            LogFire.log("DEBUG", f"✓ Same session switch handled: {same_session_result}", severity="debug")
            
            LogFire.log("DEBUG", "✓ Chat session edge cases test passed!", severity="debug")


@pytest.mark.integration
@pytest.mark.asyncio  
class TestChatSessionIntegrationWithSupervisors:
    """Integration tests for chat sessions with supervisor workflows"""
    
    async def test_chat_session_with_supervisor_workflow(self):
        """Test chat session switching within a complete supervisor workflow"""
        
        test_user_guid = str(uuid4())
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = test_user_guid
        mock_user.chat_history = {}
        mock_user.session_guid = str(uuid4())
        mock_user.platform = "test"
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
            with patch('managers.manager_supervisors.SupervisorManager.get_instance') as mock_supervisor_manager:
                
                supervisor_manager_instance = AsyncMock()
                mock_supervisor_manager.return_value = supervisor_manager_instance
                
                # Mock supervisor task that uses chat sessions
                mock_chat_task = AsyncMock()
                mock_chat_task.name = "chat_session_task"
                
                async def mock_chat_workflow(state: SupervisorTaskState):
                    # Simulate a workflow that uses chat sessions
                    new_session_tool = NewChatSessionTool()
                    change_session_tool = ChangeChatSessionTool()
                    
                    # Create new session within workflow
                    new_session_result = await new_session_tool._arun(state)
                    
                    # Add some messages
                    session_guid = str(uuid4())  # Simulated new session
                    mock_user.session_guid = session_guid
                    
                    # Simulate adding messages during workflow
                    from userprofiles.ZairaChat import ZairaChat
                    from userprofiles.ZairaMessage import ZairaMessage
                    
                    if session_guid not in mock_user.chat_history:
                        mock_user.chat_history[session_guid] = ZairaChat(
                            session_guid=session_guid,
                            user_guid=mock_user.user_guid
                        )
                    
                    workflow_messages = [
                        ZairaMessage.create_user_message("Workflow message 1", None, session_guid),
                        ZairaMessage.create_assistant_message("Workflow response 1", None, session_guid),
                        ZairaMessage.create_user_message("Workflow message 2", None, session_guid),
                        ZairaMessage.create_assistant_message("Workflow response 2", None, session_guid),
                    ]
                    
                    for msg in workflow_messages:
                        mock_user.chat_history[session_guid].add_message(msg)
                    
                    return SupervisorTaskState(
                        user_guid=state.user_guid,
                        original_input=state.original_input,
                        additional_input=state.additional_input,
                        messages=state.messages + [SystemMessage(content="Chat session workflow completed")],
                        call_trace=state.call_trace + ["chat_session_workflow"],
                        completed_tasks=state.completed_tasks + ["chat_session_task"],
                        sections=state.sections,
                        reasoning_steps=state.reasoning_steps,
                        conversation_history=state.conversation_history
                    )
                
                mock_chat_task.llm_call_internal = mock_chat_workflow
                
                # Execute the workflow
                initial_state = SupervisorTaskState(
                    user_guid=test_user_guid,
                    original_input="start chat workflow",
                    additional_input={},
                    messages=[HumanMessage(content="start chat workflow")],
                    call_trace=[],
                    completed_tasks=[],
                    sections={},
                    reasoning_steps=[],
                    conversation_history=[]
                )
                
                result_state = await mock_chat_task.llm_call_internal(initial_state)
                
                # Verify workflow completed and chat history was maintained
                assert "chat_session_workflow" in result_state.call_trace
                assert len(mock_user.chat_history) > 0
                
                # Verify at least one session has the workflow messages
                total_messages = sum(chat.message_count for chat in mock_user.chat_history.values())
                assert total_messages >= 4  # At least 4 workflow messages
                
                LogFire.log("DEBUG", "✓ Chat session integration with supervisor workflow test passed!", severity="debug")
                LogFire.log("DEBUG", f"Total sessions: {len(mock_user.chat_history)}", severity="debug")
                LogFire.log("DEBUG", f"Total messages: {total_messages}", severity="debug")