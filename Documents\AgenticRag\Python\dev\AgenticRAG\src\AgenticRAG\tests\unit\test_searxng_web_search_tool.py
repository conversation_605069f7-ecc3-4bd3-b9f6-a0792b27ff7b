"""
Unit tests for SearXNG WebSearchTool
Tests the WebSearchTool class with SearXNG backend
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
import httpx
import json
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4

from managers.manager_supervisors import SupervisorTaskState
from tasks.inputs.task_retrieval import WebSearchTool
from langchain_core.messages import HumanMessage


class TestSearXNGWebSearchTool:
    """Unit tests for the WebSearchTool with SearXNG backend"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.web_search_tool = WebSearchTool()
        
        # Standard test state
        self.test_state = SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input="test search query",
            additional_input={},
            messages=[HumanMessage(content="test search")],
            call_trace=[],
            completed_tasks=[],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
    
    def test_web_search_tool_initialization(self):
        """Test WebSearchTool initialization and properties"""
        tool = WebSearchTool()
        
        assert tool.name == "web_search_tool"
        assert "SearXNG" in tool.description
        assert "search engine" in tool.description.lower()
        
        # Test sync method raises NotImplementedError
        with pytest.raises(NotImplementedError):
            tool._run("test query", self.test_state)
        
        print("✓ WebSearchTool initialization test passed")
    
    @pytest.mark.asyncio
    async def test_web_search_tool_successful_search(self):
        """Test successful web search with mocked SearXNG response"""
        
        # Mock SearXNG response
        mock_response_data = {
            "query": "test query",
            "number_of_results": 2,
            "results": [
                {
                    "title": "Test Result 1",
                    "url": "https://example.com/1",
                    "content": "This is test content for result 1",
                    "engine": "google"
                },
                {
                    "title": "Test Result 2", 
                    "url": "https://example.com/2",
                    "content": "This is test content for result 2",
                    "engine": "bing"
                }
            ]
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            # Mock successful HTTP response
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            
            # Execute search
            result = await self.web_search_tool._arun("test query", self.test_state)
            
            # Verify result format
            assert isinstance(result, str)
            assert "Web search results for:" in result
            assert "Test Result 1" in result
            assert "Test Result 2" in result
            assert "https://example.com/1" in result
            assert "https://example.com/2" in result
            assert "Found 2 total results via SearXNG" in result
            
            # Verify HTTP call was made correctly
            mock_client_instance.get.assert_called_once()
            call_args = mock_client_instance.get.call_args
            
            # Check endpoint
            assert call_args[0][0] == "http://dns.askzaira.com:8081/search"
            
            # Check parameters
            params = call_args[1]['params']
            assert params['q'] == "test query"
            assert params['format'] == "json"
            assert params['engines'] == "google,bing,duckduckgo"
            assert params['categories'] == "general"
            assert params['safesearch'] == 1
            assert params['pageno'] == 1
            
            print("✓ Successful search test passed")
    
    @pytest.mark.asyncio
    async def test_web_search_tool_request_error(self):
        """Test web search with network request error"""
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.side_effect = httpx.RequestError("Network error")
            
            result = await self.web_search_tool._arun("test query", self.test_state)
            
            assert "Failed to connect to SearXNG" in result
            assert "dns.askzaira.com:8081" in result
            assert "Network error" in result
            
            print("✓ Request error test passed")
    
    @pytest.mark.asyncio
    async def test_web_search_tool_http_status_error(self):
        """Test web search with HTTP status error"""
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 404
            mock_response.text = "Not Found"
            
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            mock_client_instance.get.return_value.raise_for_status.side_effect = httpx.HTTPStatusError(
                "404 Not Found", request=MagicMock(), response=mock_response
            )
            
            result = await self.web_search_tool._arun("test query", self.test_state)
            
            assert "SearXNG search failed with HTTP 404" in result
            assert "Not Found" in result
            
            print("✓ HTTP status error test passed")
    
    @pytest.mark.asyncio
    async def test_web_search_tool_json_decode_error(self):
        """Test web search with JSON decode error"""
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            
            result = await self.web_search_tool._arun("test query", self.test_state)
            
            assert "Invalid JSON response from SearXNG" in result
            assert "Invalid JSON" in result
            
            print("✓ JSON decode error test passed")
    
    @pytest.mark.asyncio
    async def test_web_search_tool_empty_results(self):
        """Test web search with empty results"""
        
        empty_response = {
            "query": "test query",
            "number_of_results": 0,
            "results": []
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = empty_response
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            
            result = await self.web_search_tool._arun("test query", self.test_state)
            
            assert "No search results found for query" in result
            assert "test query" in result
            
            print("✓ Empty results test passed")
    
    @pytest.mark.asyncio
    async def test_web_search_tool_content_truncation(self):
        """Test web search content truncation for long content"""
        
        long_content = "This is very long content. " * 50  # Make it over 300 chars
        
        response_with_long_content = {
            "query": "test query",
            "number_of_results": 1,
            "results": [
                {
                    "title": "Long Content Result",
                    "url": "https://example.com/long",
                    "content": long_content,
                    "engine": "google"
                }
            ]
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = response_with_long_content
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            
            result = await self.web_search_tool._arun("test query", self.test_state)
            
            # Verify content is truncated
            assert "Long Content Result" in result
            assert "..." in result  # Should end with ellipsis
            
            # Find the content line
            lines = result.split('\n')
            content_line = None
            for line in lines:
                if "Content:" in line and len(line) > 50:
                    content_line = line
                    break
            
            if content_line:
                # Content should be limited to around 300 chars + "Content: " prefix
                # Allow some flexibility for formatting
                assert len(content_line) < 350
                assert content_line.strip().endswith("...")
            
            print("✓ Content truncation test passed")
    
    @pytest.mark.asyncio
    async def test_web_search_tool_exception_handling(self):
        """Test web search with unexpected exception"""
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.side_effect = Exception("Unexpected error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                result = await self.web_search_tool._arun("test query", self.test_state)
                
                assert "Error performing SearXNG web search" in result
                assert "Unexpected error" in result
                
                # Verify exception was logged
                mock_exception.assert_called_once()
                call_args = mock_exception.call_args[0]
                assert isinstance(call_args[0], Exception)
                assert call_args[1] == "web_search_tool"
                assert call_args[2] == self.test_user_guid
            
            print("✓ Exception handling test passed")
    
    def test_web_search_tool_parameter_configuration(self):
        """Test that SearXNG parameters are configured correctly"""
        
        # This is more of a validation test to ensure our configuration is sensible
        tool = WebSearchTool()
        
        # The tool should use the correct endpoint
        # We can't directly test this without mocking, but we can verify
        # the endpoint string is correctly formatted
        expected_base_url = "http://dns.askzaira.com:8081"
        expected_endpoint = f"{expected_base_url}/search"
        
        # These are the expected parameters that should be sent to SearXNG
        expected_params = {
            "format": "json",
            "engines": "google,bing,duckduckgo",
            "categories": "general",
            "safesearch": 1,
            "pageno": 1
        }
        
        # We can't test these directly without running the tool,
        # but we can verify they are reasonable values
        assert expected_base_url.startswith("http://")
        assert ":8081" in expected_base_url
        assert "dns.askzaira.com" in expected_base_url
        
        print("✓ Parameter configuration validation passed")


if __name__ == "__main__":
    # Run tests if called directly
    pytest.main([__file__, "-v"])