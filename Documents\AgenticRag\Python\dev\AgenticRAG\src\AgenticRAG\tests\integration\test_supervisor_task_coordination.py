"""
Comprehensive integration test for supervisor task coordination and workflow management.
This test covers the complete supervisor system including task registration, routing,
execution, and coordination between different task types.
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_supervisors import (
    SupervisorManager, SupervisorTaskState, SupervisorTask_Base,
    SupervisorTask_SingleAgent, SupervisorTask_Create_agent,
    SupervisorTask_ChainOfThought, SupervisorSupervisor,
    SupervisorSupervisor_ChainOfThought
)
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import ZairaUser
from userprofiles.permission_levels import PERMISSION_LEVELS
from tasks.etc.task_chat_session import (
    create_task_manage_chat_sessions, SupervisorTask_ChangeSession,
    new_chat_session_tool, change_chat_session_tool, list_chat_sessions_tool
)
from tasks.inputs.task_scheduled_task_manager import SupervisorTask_ScheduledTaskManager
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.tools import BaseTool
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
import asyncio

class TestSupervisorTaskCoordination:
    """Integration test for supervisor task coordination"""
    
    def setup_method(self):
        """Set up supervisor test environment"""
        # Reset singleton instances
        SupervisorManager._instance = None
        ZairaUserManager._instance = None
        
        # Set up test data
        self.test_user_guid = str(uuid4())
        self.test_user = MagicMock(spec=ZairaUser)
        self.test_user.GUID = uuid4()
        self.test_user.username = "test_supervisor_user"
        self.test_user.sessionGUID = uuid4()
        self.test_user.conversationGUID = uuid4()
        self.test_user.chat_history = {self.test_user.sessionGUID: []}
    
    def teardown_method(self):
        """Clean up after tests"""
        SupervisorManager._instance = None
        ZairaUserManager._instance = None
    
    @pytest.mark.asyncio
    async def test_complete_supervisor_workflow(self):
        """Test complete supervisor workflow from task registration to execution"""
        with patch('managers.manager_supervisors.LogFire.log'), \
             patch('managers.manager_users.ZairaUserManager.find_user', new_callable=AsyncMock) as mock_find_user:
            
            mock_find_user.return_value = self.test_user
            
            # Step 1: Initialize supervisor manager
            supervisor_manager = SupervisorManager.get_instance()
            assert supervisor_manager is not None
            assert len(supervisor_manager.tasks) == 0
            
            # Step 2: Create and register a custom task
            class TestCustomTool(BaseTool):
                name: str = "test_custom_tool"
                description: str = "A test tool for integration testing"
                
                def _run(self, query: str, state: SupervisorTaskState) -> str:
                    raise NotImplementedError("Use async version")
                
                async def _arun(self, query: str, state: SupervisorTaskState) -> str:
                    return f"Processed query: {query} for user {state.user_guid}"
            
            test_tool = TestCustomTool()
            
            class TestSupervisorTask(SupervisorTask_SingleAgent):
                async def llm_call(self, state: SupervisorTaskState):
                    # Mock LLM response that uses the tool
                    state.messages.append(AIMessage(content=f"Using test tool for: {state.original_input}"))
                    
                    # Simulate tool usage
                    tool_result = await test_tool._arun(state.original_input, state)
                    state.messages.append(AIMessage(content=f"Tool result: {tool_result}"))
                    
                    return state
            
            test_task = TestSupervisorTask(
                name="test_custom_task",
                tools=[test_tool],
                prompt_id="Test_Custom_Task"
            )
            
            # Register the task
            registered_task = supervisor_manager.register_task(test_task)
            assert registered_task == test_task
            assert "test_custom_task" in supervisor_manager.tasks
            assert len(supervisor_manager.tasks) == 1
            
            # Step 3: Create and test SupervisorTaskState
            initial_state = SupervisorTaskState(
                user_guid=self.test_user_guid,
                original_input="Test query for custom task",
                additional_input={},
                messages=[HumanMessage(content="Test query for custom task")]
            )
            
            # Step 4: Execute the task
            final_state = await test_task.llm_call(initial_state)
            
            # Verify task execution
            assert final_state.user_guid == self.test_user_guid
            assert final_state.original_input == "Test query for custom task"
            assert len(final_state.messages) >= 3  # Initial + 2 added by task
            assert "Using test tool for:" in final_state.messages[1].content
            assert "Tool result:" in final_state.messages[2].content
            assert "Processed query:" in final_state.messages[2].content
            
            # Step 5: Test task retrieval
            retrieved_task = supervisor_manager.get_task("test_custom_task")
            assert retrieved_task == test_task
            
            non_existent = supervisor_manager.get_task("non_existent_task")
            assert non_existent is None
    
    @pytest.mark.asyncio
    async def test_supervisor_hierarchy_coordination(self):
        """Test hierarchical supervisor coordination with multiple task types"""
        with patch('managers.manager_supervisors.LogFire.log'), \
             patch('managers.manager_users.ZairaUserManager.find_user', new_callable=AsyncMock) as mock_find_user:
            
            mock_find_user.return_value = self.test_user
            
            supervisor_manager = SupervisorManager.get_instance()
            
            # Step 1: Register multiple task types
            
            # Single Agent Task
            class SingleAgentTask(SupervisorTask_SingleAgent):
                async def llm_call(self, state: SupervisorTaskState):
                    state.messages.append(AIMessage(content="Single agent response"))
                    return state
            
            single_agent_task = SingleAgentTask(
                name="single_agent_task",
                tools=[],
                prompt_id="Single_Agent_Test"
            )
            
            # Create Agent Task (ReAct pattern)
            class CreateAgentTask(SupervisorTask_Create_agent):
                async def llm_call(self, state: SupervisorTaskState):
                    state.messages.append(AIMessage(content="Create agent response"))
                    return state
            
            create_agent_task = CreateAgentTask(
                name="create_agent_task",
                tools=[],
                prompt_id="Create_Agent_Test"
            )
            
            # Chain of Thought Task
            class ChainOfThoughtTask(SupervisorTask_ChainOfThought):
                async def llm_call(self, state: SupervisorTaskState):
                    state.messages.append(AIMessage(content="Chain of thought response"))
                    state.reasoning_steps.append("Step 1: Analyze input")
                    state.reasoning_steps.append("Step 2: Process information")
                    state.reasoning_steps.append("Step 3: Generate response")
                    return state
            
            cot_task = ChainOfThoughtTask(
                name="cot_task",
                tools=[],
                prompt_id="CoT_Test"
            )
            
            # Register all tasks
            supervisor_manager.register_task(single_agent_task)
            supervisor_manager.register_task(create_agent_task)
            supervisor_manager.register_task(cot_task)
            
            assert len(supervisor_manager.tasks) == 3
            
            # Step 2: Create a supervisor to coordinate tasks
            class TestSupervisorSupervisor(SupervisorSupervisor):
                def __init__(self):
                    super().__init__()
                    self.add_task("single_agent_task", "Handle simple single agent queries")
                    self.add_task("create_agent_task", "Handle complex agent creation tasks")
                    self.add_task("cot_task", "Handle tasks requiring step-by-step reasoning")
                
                async def route_to_task(self, state: SupervisorTaskState) -> str:
                    query = state.original_input.lower()
                    if "simple" in query:
                        return "single_agent_task"
                    elif "create" in query or "complex" in query:
                        return "create_agent_task"
                    elif "reason" in query or "step" in query:
                        return "cot_task"
                    else:
                        return "single_agent_task"  # Default
            
            test_supervisor = TestSupervisorSupervisor()
            
            # Step 3: Test routing to different task types
            test_cases = [
                ("This is a simple query", "single_agent_task", "Single agent response"),
                ("Create a complex agent", "create_agent_task", "Create agent response"),
                ("I need step by step reasoning", "cot_task", "Chain of thought response")
            ]
            
            for input_query, expected_task, expected_response in test_cases:
                state = SupervisorTaskState(
                    user_guid=self.test_user_guid,
                    original_input=input_query,
                    additional_input={},
                    messages=[HumanMessage(content=input_query)]
                )
                
                # Test routing
                routed_task_name = await test_supervisor.route_to_task(state)
                assert routed_task_name == expected_task
                
                # Get and execute the task
                task = supervisor_manager.get_task(routed_task_name)
                assert task is not None
                
                final_state = await task.llm_call(state)
                
                # Verify execution
                assert len(final_state.messages) >= 2
                assert expected_response in final_state.messages[-1].content
                
                # For CoT task, verify reasoning steps
                if expected_task == "cot_task":
                    assert len(final_state.reasoning_steps) == 3
                    assert "Step 1: Analyze input" in final_state.reasoning_steps
    
    @pytest.mark.asyncio
    async def test_real_world_task_integration(self):
        """Test integration with real-world tasks like chat session and scheduled tasks"""
        with patch('managers.manager_supervisors.LogFire.log'), \
             patch('managers.manager_users.ZairaUserManager.find_user', new_callable=AsyncMock) as mock_find_user, \
             patch('tasks.etc.task_chat_session.ZairaUserManager.find_user', new_callable=AsyncMock) as mock_chat_find_user:
            
            mock_find_user.return_value = self.test_user
            mock_chat_find_user.return_value = self.test_user
            
            supervisor_manager = SupervisorManager.get_instance()
            
            # Step 1: Register real chat session task
            chat_session_task = await create_task_manage_chat_sessions()
            assert chat_session_task is not None
            assert chat_session_task.name == "manage_sessions_task"
            assert len(chat_session_task.get_tools()) == 3
            
            # Verify task was registered
            retrieved_chat_task = supervisor_manager.get_task("manage_sessions_task")
            assert retrieved_chat_task == chat_session_task
            
            # Step 2: Test chat session tools directly
            state = SupervisorTaskState(
                user_guid=self.test_user_guid,
                original_input="Create new chat session",
                additional_input={},
                messages=[HumanMessage(content="Create new chat session")]
            )
            
            # Test new chat session tool
            new_session_result = await new_chat_session_tool._arun(state)
            assert "New chat history session has been started with GUID" in new_session_result
            
            # Test list chat sessions tool
            list_result = await list_chat_sessions_tool._arun(state)
            assert "Current session:" in list_result or "No chat sessions found" in list_result
            
            # Step 3: Register scheduled task manager
            scheduled_task_manager = SupervisorTask_ScheduledTaskManager()
            supervisor_manager.register_task(scheduled_task_manager)
            
            retrieved_scheduled = supervisor_manager.get_task("scheduled_task_manager")
            assert retrieved_scheduled == scheduled_task_manager
            assert len(retrieved_scheduled._tasks) == 4  # create, list, cancel, info
            
            # Test individual scheduled task sub-tasks
            create_task = scheduled_task_manager.get_task("create_scheduled_task")
            assert create_task is not None
            assert create_task.name == "create_scheduled_task"
            
            list_task = scheduled_task_manager.get_task("list_scheduled_tasks")
            assert list_task is not None
            assert list_task.name == "list_scheduled_tasks"
            
            cancel_task = scheduled_task_manager.get_task("cancel_scheduled_task")
            assert cancel_task is not None
            assert cancel_task.name == "cancel_scheduled_task"
            
            info_task = scheduled_task_manager.get_task("scheduled_task_info")
            assert info_task is not None
            assert info_task.name == "scheduled_task_info"
    
    @pytest.mark.asyncio
    async def test_supervisor_error_handling_and_recovery(self):
        """Test supervisor system error handling and recovery mechanisms"""
        with patch('managers.manager_supervisors.LogFire.log'), \
             patch('etc.helper_functions.exception_triggered') as mock_exception:
            
            supervisor_manager = SupervisorManager.get_instance()
            
            # Step 1: Test task registration with invalid data
            class InvalidTask:
                """Invalid task that doesn't inherit from SupervisorTask_Base"""
                pass
            
            # This should not break the system
            try:
                supervisor_manager.register_task(InvalidTask())
            except Exception:
                pass  # Expected to fail gracefully
            
            # System should still be functional
            assert supervisor_manager is not None
            
            # Step 2: Test task with failing llm_call
            class FailingTask(SupervisorTask_Base):
                async def llm_call(self, state: SupervisorTaskState):
                    raise Exception("Simulated task failure")
            
            failing_task = FailingTask(name="failing_task", prompt_id="Failing_Test")
            supervisor_manager.register_task(failing_task)
            
            state = SupervisorTaskState(
                user_guid=self.test_user_guid,
                original_input="Test failing task",
                additional_input={},
                messages=[HumanMessage(content="Test failing task")]
            )
            
            # Execute failing task
            try:
                await failing_task.llm_call(state)
            except Exception as e:
                assert "Simulated task failure" in str(e)
            
            # Step 3: Test supervisor with missing tasks
            class TestSupervisorWithMissingTasks(SupervisorSupervisor):
                def __init__(self):
                    super().__init__()
                    self.add_task("non_existent_task", "This task doesn't exist")
                
                async def route_to_task(self, state: SupervisorTaskState) -> str:
                    return "non_existent_task"
            
            supervisor_with_missing = TestSupervisorWithMissingTasks()
            
            # This should handle missing task gracefully
            routed_task_name = await supervisor_with_missing.route_to_task(state)
            assert routed_task_name == "non_existent_task"
            
            # Getting the task should return None
            missing_task = supervisor_manager.get_task("non_existent_task")
            assert missing_task is None
            
            # Step 4: Test state corruption recovery
            corrupted_state = SupervisorTaskState(
                user_guid="invalid-guid-format",  # Invalid GUID
                original_input="",  # Empty input
                additional_input=None,  # None instead of dict
                messages=[]  # Empty messages
            )
            
            # System should handle corrupted state gracefully
            class RobustTask(SupervisorTask_Base):
                async def llm_call(self, state: SupervisorTaskState):
                    # Validate and fix state
                    if not state.original_input:
                        state.original_input = "Default input"
                    if state.additional_input is None:
                        state.additional_input = {}
                    if not state.messages:
                        state.messages = [HumanMessage(content=state.original_input)]
                    
                    state.messages.append(AIMessage(content="State recovered and processed"))
                    return state
            
            robust_task = RobustTask(name="robust_task", prompt_id="Robust_Test")
            supervisor_manager.register_task(robust_task)
            
            recovered_state = await robust_task.llm_call(corrupted_state)
            assert recovered_state.original_input == "Default input"
            assert recovered_state.additional_input == {}
            assert len(recovered_state.messages) == 2
            assert "State recovered and processed" in recovered_state.messages[-1].content
    
    @pytest.mark.asyncio
    async def test_concurrent_supervisor_operations(self):
        """Test concurrent supervisor operations and thread safety"""
        with patch('managers.manager_supervisors.LogFire.log'):
            
            supervisor_manager = SupervisorManager.get_instance()
            
            # Step 1: Create multiple tasks for concurrent testing
            class ConcurrentTask(SupervisorTask_Base):
                def __init__(self, task_id: str):
                    super().__init__(name=f"concurrent_task_{task_id}", prompt_id=f"Concurrent_{task_id}")
                    self.task_id = task_id
                    self.execution_count = 0
                
                async def llm_call(self, state: SupervisorTaskState):
                    self.execution_count += 1
                    await asyncio.sleep(0.01)  # Simulate some processing time
                    state.messages.append(AIMessage(content=f"Task {self.task_id} executed"))
                    return state
            
            # Step 2: Register multiple tasks concurrently
            async def register_task(task_id):
                task = ConcurrentTask(task_id)
                supervisor_manager.register_task(task)
                return task
            
            registration_tasks = [register_task(str(i)) for i in range(10)]
            registered_tasks = await asyncio.gather(*registration_tasks)
            
            assert len(registered_tasks) == 10
            assert len(supervisor_manager.tasks) >= 10
            
            # Step 3: Execute tasks concurrently
            async def execute_task(task):
                state = SupervisorTaskState(
                    user_guid=str(uuid4()),
                    original_input=f"Test for {task.name}",
                    additional_input={},
                    messages=[HumanMessage(content=f"Test for {task.name}")]
                )
                return await task.llm_call(state)
            
            execution_tasks = [execute_task(task) for task in registered_tasks]
            execution_results = await asyncio.gather(*execution_tasks)
            
            assert len(execution_results) == 10
            
            # Verify all tasks executed successfully
            for i, (task, result) in enumerate(zip(registered_tasks, execution_results)):
                assert task.execution_count == 1
                assert len(result.messages) == 2
                assert f"Task {task.task_id} executed" in result.messages[-1].content
            
            # Step 4: Test concurrent task retrieval
            async def retrieve_task(task_name):
                return supervisor_manager.get_task(task_name)
            
            retrieval_tasks = [retrieve_task(f"concurrent_task_{i}") for i in range(10)]
            retrieved_tasks = await asyncio.gather(*retrieval_tasks)
            
            assert len(retrieved_tasks) == 10
            assert all(task is not None for task in retrieved_tasks)