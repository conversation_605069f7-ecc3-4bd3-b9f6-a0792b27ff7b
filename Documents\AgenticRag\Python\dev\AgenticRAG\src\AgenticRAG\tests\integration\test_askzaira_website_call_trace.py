"""
Integration test for verifying the exact call trace when requesting information about www.askzaira.nl
Specific test for: "tell me about www.askzaira.nl"
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4
from typing import Dict, List, Any
from pydantic import BaseModel

from managers.manager_supervisors import SupervisorManager, SupervisorTaskState, SupervisorSupervisor
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import ZairaUser
from langchain_core.messages import HumanMessage, SystemMessage
from tasks.task_top_level_supervisor import create_top_level_supervisor


@pytest.mark.integration
@pytest.mark.asyncio
class TestAskZairaWebsiteCallTrace:
    """Test exact call trace for 'tell me about www.askzaira.nl' query"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_query = "tell me about www.askzaira.nl"
        # For web URL queries, the expected path focuses on web search and retrieval
        self.expected_call_trace = [
            "top",
            "quick_rag", 
            "quick_llm",
            "quick_complexity",
            "search_supervisor",
            "web_search",
            "rag_search",
            "output_supervisor", 
            "output_processing",
            "output_sender"
        ]
        
        # Initialize Globals for testing to avoid AttributeError
        try:
            Globals.Debug = True
        except:
            pass
        
    async def test_askzaira_website_call_trace_exact_match(self):
        """Test that the call trace matches exactly when requesting www.askzaira.nl information"""
        
        # Create mock user
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        mock_user.sessionGUID = uuid4()
        mock_user.chat_history = {}
        mock_user.platform = "test"
        
        # Create initial state with the specific query
        initial_state = SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input=self.test_query,
            additional_input={},
            messages=[HumanMessage(content=self.test_query)],
            call_trace=[],
            completed_tasks=[],
            sections={},
            reasoning_steps=[],
            conversation_history=[HumanMessage(content=self.test_query)]
        )
        
        # Mock dependencies and managers
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
            with patch('managers.manager_supervisors.SupervisorManager.get_instance') as mock_supervisor_manager:
                
                # Create mock supervisor manager instance
                supervisor_manager_instance = AsyncMock()
                mock_supervisor_manager.return_value = supervisor_manager_instance
                
                # Mock the task creation functions to return mock tasks with proper call trace tracking
                with patch('tasks.inputs.quick_search.create_task_quick_rag_search') as mock_quick_rag:
                    with patch('tasks.inputs.quick_search.create_task_quick_llm_search') as mock_quick_llm:
                        with patch('tasks.inputs.quick_search.create_task_quick_complexity_search') as mock_quick_complexity:
                            with patch('tasks.inputs.task_retrieval.create_supervisor_retrieval') as mock_search_supervisor:
                                with patch('tasks.task_top_output_supervisor.create_top_output_supervisor') as mock_output_supervisor:
                                    
                                    # Create mock tasks that track call traces
                                    mock_quick_rag_task = self._create_mock_task("quick_rag")
                                    mock_quick_llm_task = self._create_mock_task("quick_llm") 
                                    mock_quick_complexity_task = self._create_mock_task("quick_complexity")
                                    # For website queries, search supervisor includes web search
                                    mock_search_supervisor_task = self._create_mock_supervisor("search_supervisor", ["web_search", "rag_search"])
                                    mock_output_supervisor_task = self._create_mock_supervisor("output_supervisor", ["output_processing", "output_sender"])
                                    
                                    # Setup return values for task creation
                                    mock_quick_rag.return_value = mock_quick_rag_task
                                    mock_quick_llm.return_value = mock_quick_llm_task
                                    mock_quick_complexity.return_value = mock_quick_complexity_task
                                    mock_search_supervisor.return_value = mock_search_supervisor_task
                                    mock_output_supervisor.return_value = mock_output_supervisor_task
                                    
                                    # Mock other task creation functions to return empty tasks
                                    with patch('tasks.processing.task_email_writer.create_supervisor_email_writer', return_value=self._create_mock_task("email_writer")):
                                        with patch('tasks.processing.task_joker.create_supervisor_joker', return_value=self._create_mock_task("joker")):
                                            with patch('tasks.processing.task_agenda_planner.create_supervisor_agenda_planner', return_value=self._create_mock_task("agenda_planner")):
                                                with patch('tasks.inputs.task_imap.create_task_imap_receiver', return_value=self._create_mock_task("imap_receiver")):
                                                    with patch('tasks.inputs.task_gdrive.create_task_gdrive_receiver', return_value=self._create_mock_task("gdrive_receiver")):
                                                        with patch('tasks.etc.task_chat_session.create_task_manage_chat_sessions', return_value=self._create_mock_task("chat_session")):
                                                            with patch('tasks.inputs.imap_idle_activate.create_task_imap_idle_activate', return_value=self._create_mock_task("imap_idle")):
                                                                with patch('tasks.inputs.task_scheduled_task_manager.create_task_scheduled_task_manager', return_value=self._create_mock_task("scheduled_task_manager")):
                                                                    with patch('tasks.inputs.task_email_checker.create_task_email_checker', return_value=self._create_mock_task("email_checker")):
                                                                        
                                                                        # Create the actual top level supervisor
                                                                        top_supervisor = await self._create_mock_top_supervisor()
                                                                        
                                                                        # Execute the query through the supervisor
                                                                        final_state = await self._execute_supervisor_with_trace_tracking(
                                                                            top_supervisor, 
                                                                            initial_state
                                                                        )
                                                                        
                                                                        # Verify the call trace matches exactly
                                                                        actual_call_trace = final_state.call_trace
                                                                        
                                                                        # Assert exact match
                                                                        assert actual_call_trace == self.expected_call_trace, (
                                                                            f"Call trace mismatch!\\n"
                                                                            f"Expected: {self.expected_call_trace}\\n"
                                                                            f"Actual:   {actual_call_trace}\\n"
                                                                            f"Missing:  {set(self.expected_call_trace) - set(actual_call_trace)}\\n"
                                                                            f"Extra:    {set(actual_call_trace) - set(self.expected_call_trace)}"
                                                                        )
                                                                        
                                                                        # Verify the order is correct
                                                                        for i, expected_task in enumerate(self.expected_call_trace):
                                                                            assert actual_call_trace[i] == expected_task, (
                                                                                f"Call trace order mismatch at position {i}:\\n"
                                                                                f"Expected: {expected_task}\\n"
                                                                                f"Actual:   {actual_call_trace[i]}"
                                                                            )
                                                                        
                                                                        print(f"✓ Website call trace verification passed: {actual_call_trace}")

    def _create_mock_task(self, name: str):
        """Create a mock task that tracks call traces properly"""
        mock_task = AsyncMock()
        mock_task.name = name
        mock_task.task_id = str(uuid4())
        mock_task.prompt_id = f"Mock_{name}_Prompt"
        
        async def mock_llm_call_wrapper(state: SupervisorTaskState):
            # Add this task to the call trace
            updated_call_trace = state.call_trace + [name]
            return SupervisorTaskState(
                user_guid=state.user_guid,
                original_input=state.original_input,
                additional_input=state.additional_input,
                messages=state.messages + [SystemMessage(content=f"Mock response from {name}")],
                call_trace=updated_call_trace,
                completed_tasks=state.completed_tasks + [name],
                sections=state.sections,
                reasoning_steps=state.reasoning_steps,
                conversation_history=state.conversation_history
            )
        
        mock_task.llm_call_wrapper = mock_llm_call_wrapper
        mock_task.llm_call_internal = mock_llm_call_wrapper
        return mock_task
    
    def _create_mock_supervisor(self, name: str, sub_tasks: List[str]):
        """Create a mock supervisor that delegates to sub-tasks"""
        mock_supervisor = AsyncMock()
        mock_supervisor.name = name
        mock_supervisor.task_id = str(uuid4())
        
        async def mock_supervisor_call(state: SupervisorTaskState):
            # Add supervisor to call trace
            updated_call_trace = state.call_trace + [name]
            current_state = SupervisorTaskState(
                user_guid=state.user_guid,
                original_input=state.original_input,
                additional_input=state.additional_input,
                messages=state.messages,
                call_trace=updated_call_trace,
                completed_tasks=state.completed_tasks + [name],
                sections=state.sections,
                reasoning_steps=state.reasoning_steps,
                conversation_history=state.conversation_history
            )
            
            # Execute sub-tasks in sequence
            for sub_task_name in sub_tasks:
                current_state = SupervisorTaskState(
                    user_guid=current_state.user_guid,
                    original_input=current_state.original_input,
                    additional_input=current_state.additional_input,
                    messages=current_state.messages,
                    call_trace=current_state.call_trace + [sub_task_name],
                    completed_tasks=current_state.completed_tasks + [sub_task_name],
                    sections=current_state.sections,
                    reasoning_steps=current_state.reasoning_steps,
                    conversation_history=current_state.conversation_history
                )
            
            return current_state
        
        mock_supervisor.llm_call_wrapper = mock_supervisor_call
        mock_supervisor.llm_call_internal = mock_supervisor_call
        return mock_supervisor
    
    async def _create_mock_top_supervisor(self):
        """Create a mock top-level supervisor that routes to appropriate tasks"""
        mock_top_supervisor = AsyncMock()
        mock_top_supervisor.name = "top"
        
        async def mock_top_supervisor_routing(state: SupervisorTaskState):
            """Simulate top-level supervisor routing for www.askzaira.nl query"""
            # Add top to call trace
            updated_call_trace = state.call_trace + ["top"]
            current_state = SupervisorTaskState(
                user_guid=state.user_guid,
                original_input=state.original_input,
                additional_input=state.additional_input,
                messages=state.messages,
                call_trace=updated_call_trace,
                completed_tasks=state.completed_tasks + ["top"],
                sections=state.sections,
                reasoning_steps=state.reasoning_steps,
                conversation_history=state.conversation_history
            )
            
            # For website queries, the expected flow is:
            # 1. quick_rag, quick_llm, quick_complexity (quick analysis)
            # 2. search_supervisor -> web_search, rag_search (web and document retrieval)
            # 3. output_supervisor -> output_processing -> output_sender (output handling)
            
            # Execute quick tasks first
            for quick_task in ["quick_rag", "quick_llm", "quick_complexity"]:
                current_state = SupervisorTaskState(
                    user_guid=current_state.user_guid,
                    original_input=current_state.original_input,
                    additional_input=current_state.additional_input,
                    messages=current_state.messages,
                    call_trace=current_state.call_trace + [quick_task],
                    completed_tasks=current_state.completed_tasks + [quick_task],
                    sections=current_state.sections,
                    reasoning_steps=current_state.reasoning_steps,
                    conversation_history=current_state.conversation_history
                )
            
            # Execute search supervisor and its sub-tasks (including web search for URLs)
            current_state = SupervisorTaskState(
                user_guid=current_state.user_guid,
                original_input=current_state.original_input,
                additional_input=current_state.additional_input,
                messages=current_state.messages,
                call_trace=current_state.call_trace + ["search_supervisor", "web_search", "rag_search"],
                completed_tasks=current_state.completed_tasks + ["search_supervisor", "web_search", "rag_search"],
                sections=current_state.sections,
                reasoning_steps=current_state.reasoning_steps,
                conversation_history=current_state.conversation_history
            )
            
            # Execute output supervisor and its sub-tasks
            current_state = SupervisorTaskState(
                user_guid=current_state.user_guid,
                original_input=current_state.original_input,
                additional_input=current_state.additional_input,
                messages=current_state.messages,
                call_trace=current_state.call_trace + ["output_supervisor", "output_processing", "output_sender"],
                completed_tasks=current_state.completed_tasks + ["output_supervisor", "output_processing", "output_sender"],
                sections=current_state.sections,
                reasoning_steps=current_state.reasoning_steps,
                conversation_history=current_state.conversation_history
            )
            
            return current_state
        
        mock_top_supervisor.llm_call_wrapper = mock_top_supervisor_routing
        mock_top_supervisor.llm_call_internal = mock_top_supervisor_routing
        return mock_top_supervisor
    
    async def _execute_supervisor_with_trace_tracking(self, supervisor, initial_state: SupervisorTaskState):
        """Execute supervisor and track the call trace"""
        try:
            # Execute the supervisor's routing logic
            result_state = await supervisor.llm_call_internal(initial_state)
            
            # Ensure we return a proper SupervisorTaskState
            if not isinstance(result_state, SupervisorTaskState):
                # If it's a Command or other type, extract the state
                if hasattr(result_state, 'update') and isinstance(result_state.update, dict):
                    updated_fields = result_state.update
                    result_state = SupervisorTaskState(
                        user_guid=initial_state.user_guid,
                        original_input=initial_state.original_input,
                        additional_input=initial_state.additional_input,
                        messages=updated_fields.get('messages', initial_state.messages),
                        call_trace=updated_fields.get('call_trace', initial_state.call_trace),
                        completed_tasks=updated_fields.get('completed_tasks', initial_state.completed_tasks),
                        sections=updated_fields.get('sections', initial_state.sections),
                        reasoning_steps=updated_fields.get('reasoning_steps', initial_state.reasoning_steps),
                        conversation_history=updated_fields.get('conversation_history', initial_state.conversation_history)
                    )
                else:
                    result_state = initial_state
            
            return result_state
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "test_askzaira_website_call_trace", None)
            # Return initial state with error in case of failure
            return SupervisorTaskState(
                user_guid=initial_state.user_guid,
                original_input=initial_state.original_input,
                additional_input=initial_state.additional_input,
                messages=initial_state.messages + [SystemMessage(content=f"Error: {str(e)}")],
                call_trace=initial_state.call_trace + ["ERROR"],
                completed_tasks=initial_state.completed_tasks,
                sections=initial_state.sections,
                reasoning_steps=initial_state.reasoning_steps,
                conversation_history=initial_state.conversation_history
            )

    async def test_askzaira_website_call_trace_web_search_priority(self):
        """Test that web search is prioritized for URL queries"""
        
        # Create mock user
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        # Create initial state
        initial_state = SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input=self.test_query,
            additional_input={},
            messages=[HumanMessage(content=self.test_query)],
            call_trace=[],
            completed_tasks=[],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
        
        # Mock dependencies
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
            mock_supervisor = await self._create_mock_top_supervisor()
            
            # Execute and get result
            final_state = await self._execute_supervisor_with_trace_tracking(mock_supervisor, initial_state)
            
            call_trace = final_state.call_trace
            
            # Verify web search is included for URL queries
            assert "web_search" in call_trace, (
                f"'web_search' should be included for URL queries, got: {call_trace}"
            )
            
            # Verify web search comes after search_supervisor but before output tasks
            web_search_pos = call_trace.index("web_search")
            search_supervisor_pos = call_trace.index("search_supervisor")
            output_supervisor_pos = call_trace.index("output_supervisor")
            
            assert search_supervisor_pos < web_search_pos, (
                "search_supervisor should come before web_search"
            )
            assert web_search_pos < output_supervisor_pos, (
                "web_search should come before output_supervisor"
            )
            
            print(f"✓ Web search priority verification passed: {call_trace}")