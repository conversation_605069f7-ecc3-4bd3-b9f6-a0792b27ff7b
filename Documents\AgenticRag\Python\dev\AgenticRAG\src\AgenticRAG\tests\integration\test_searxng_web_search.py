"""
Integration test for SearXNG web search functionality
Tests: SearXNG connection, search execution, result formatting
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
import httpx
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import uuid4
from typing import Dict, List, Any, Optional

from managers.manager_supervisors import SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import ZairaUser
from tasks.inputs.task_retrieval import WebSearchTool
from langchain_core.messages import HumanMessage


@pytest.mark.integration
@pytest.mark.asyncio
class TestSearXNGWebSearch:
    """Integration tests for SearXNG web search functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.searxng_base_url = "http://dns.askzaira.com:8081"
        self.test_queries = [
            "Python programming tutorial",
            "machine learning basics",
            "how to configure SearXNG",
            "LangChain documentation",
            "artificial intelligence news"
        ]
        
        # Mock SearXNG response structure
        self.mock_searxng_response = {
            "query": "Python programming tutorial",
            "number_of_results": 3,
            "results": [
                {
                    "title": "Python Tutorial - Official Documentation",
                    "url": "https://docs.python.org/3/tutorial/",
                    "content": "The Python Tutorial — Python 3.12.0 documentation. Python is an easy to learn, powerful programming language. It has efficient high-level data structures and a simple but effective approach to object-oriented programming.",
                    "engine": "google",
                    "parsed_url": ["https", "docs.python.org", "/3/tutorial/", "", "", ""],
                    "template": "default.html",
                    "engines": ["google"],
                    "positions": [1]
                },
                {
                    "title": "Learn Python Programming",
                    "url": "https://www.programiz.com/python-programming",
                    "content": "Learn Python programming language with our comprehensive Python tutorial. Start from basics and go up to advanced concepts like OOP.",
                    "engine": "bing", 
                    "parsed_url": ["https", "www.programiz.com", "/python-programming", "", "", ""],
                    "template": "default.html",
                    "engines": ["bing"],
                    "positions": [2]
                },
                {
                    "title": "Python for Beginners",
                    "url": "https://www.python.org/about/gettingstarted/",
                    "content": "New to programming? Python is free and easy to learn if you know where to start! This guide will help you to get started quickly.",
                    "engine": "duckduckgo",
                    "parsed_url": ["https", "www.python.org", "/about/gettingstarted/", "", "", ""],
                    "template": "default.html", 
                    "engines": ["duckduckgo"],
                    "positions": [3]
                }
            ],
            "answers": [],
            "corrections": [],
            "infoboxes": [],
            "suggestions": ["python tutorial beginner", "python programming guide", "learn python online"],
            "unresponsive_engines": []
        }
        
        # Initialize Globals for testing
        try:
            Globals.Debug = True
        except:
            pass
    
    def teardown_method(self):
        """Clean up after test"""
        pass
    
    async def test_searxng_web_search_successful_query(self):
        """Test successful SearXNG web search with proper response parsing"""
        
        # Create mock user
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        # Create test state
        state = SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input="search for Python programming tutorial",
            additional_input={},
            messages=[HumanMessage(content="search for Python programming tutorial")],
            call_trace=[],
            completed_tasks=[],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
        
        # Mock successful HTTP response
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = self.mock_searxng_response
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                # Execute web search
                web_search_tool = WebSearchTool()
                result = await web_search_tool._arun("Python programming tutorial", state)
                
                # Verify request was made correctly
                mock_client_instance.get.assert_called_once()
                call_args = mock_client_instance.get.call_args
                
                # Check URL
                assert call_args[0][0] == f"{self.searxng_base_url}/search"
                
                # Check parameters
                params = call_args[1]['params']
                assert params['q'] == "Python programming tutorial"
                assert params['format'] == "json"
                assert "google" in params['engines']
                assert params['categories'] == "general"
                
                # Verify result format and content
                assert isinstance(result, str)
                assert "Web search results for:" in result
                assert "Python Tutorial - Official Documentation" in result
                assert "Learn Python Programming" in result
                assert "Python for Beginners" in result
                assert "https://docs.python.org/3/tutorial/" in result
                assert "Found 3 total results via SearXNG" in result
                assert "Showing top 3 results:" in result
                
                print(f"✓ SearXNG web search successful: {len(result)} characters returned")
                print(f"✓ Results contain expected Python tutorial content")
    
    async def test_searxng_web_search_connection_failure(self):
        """Test SearXNG web search connection failure handling"""
        
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        state = SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input="search for test query",
            additional_input={},
            messages=[HumanMessage(content="search for test query")],
            call_trace=[],
            completed_tasks=[],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
        
        # Mock connection failure
        with patch('httpx.AsyncClient') as mock_client:
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.side_effect = httpx.RequestError("Connection failed")
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                web_search_tool = WebSearchTool()
                result = await web_search_tool._arun("test query", state)
                
                # Verify error handling
                assert "Failed to connect to SearXNG" in result
                assert "dns.askzaira.com:8081" in result
                assert "Connection failed" in result
                
                print(f"✓ Connection failure handled gracefully: {result}")
    
    async def test_searxng_web_search_http_error(self):
        """Test SearXNG web search HTTP error handling"""
        
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        state = SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input="search for test query",
            additional_input={},
            messages=[HumanMessage(content="search for test query")],
            call_trace=[],
            completed_tasks=[],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
        
        # Mock HTTP error response
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 500
            mock_response.text = "Internal Server Error"
            
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            mock_client_instance.get.return_value.raise_for_status.side_effect = httpx.HTTPStatusError(
                "500 Internal Server Error", request=MagicMock(), response=mock_response
            )
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                web_search_tool = WebSearchTool()
                result = await web_search_tool._arun("test query", state)
                
                # Verify error handling
                assert "SearXNG search failed with HTTP 500" in result
                assert "Internal Server Error" in result
                
                print(f"✓ HTTP error handled gracefully: {result}")
    
    async def test_searxng_web_search_empty_results(self):
        """Test SearXNG web search with empty results"""
        
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        state = SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input="search for very obscure query",
            additional_input={},
            messages=[HumanMessage(content="search for very obscure query")],
            call_trace=[],
            completed_tasks=[],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
        
        # Mock empty results response
        empty_response = {
            "query": "very obscure query",
            "number_of_results": 0,
            "results": [],
            "answers": [],
            "corrections": [],
            "infoboxes": [],
            "suggestions": [],
            "unresponsive_engines": []
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = empty_response
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                web_search_tool = WebSearchTool()
                result = await web_search_tool._arun("very obscure query", state)
                
                # Verify empty results handling
                assert "No search results found for query" in result
                assert "very obscure query" in result
                
                print(f"✓ Empty results handled gracefully: {result}")
    
    async def test_searxng_web_search_malformed_json(self):
        """Test SearXNG web search with malformed JSON response"""
        
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        state = SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input="search for test query",
            additional_input={},
            messages=[HumanMessage(content="search for test query")],
            call_trace=[],
            completed_tasks=[],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
        
        # Mock malformed JSON response
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                web_search_tool = WebSearchTool()
                result = await web_search_tool._arun("test query", state)
                
                # Verify JSON error handling
                assert "Invalid JSON response from SearXNG" in result
                assert "Invalid JSON" in result
                
                print(f"✓ Malformed JSON handled gracefully: {result}")
    
    async def test_searxng_web_search_result_formatting(self):
        """Test SearXNG web search result formatting and content truncation"""
        
        # Create test data with very long content
        long_content_response = {
            "query": "test query",
            "number_of_results": 2,
            "results": [
                {
                    "title": "Very Long Article Title That Should Be Preserved",
                    "url": "https://example.com/long-article",
                    "content": "This is a very long piece of content that should be truncated because it exceeds the maximum length limit that we have set for search result content. " * 10,  # Very long content
                    "engine": "google"
                },
                {
                    "title": "Short Article",
                    "url": "https://example.com/short",
                    "content": "Short content",
                    "engine": "bing"
                }
            ]
        }
        
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.user_guid = self.test_user_guid
        
        state = SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input="test query",
            additional_input={},
            messages=[HumanMessage(content="test query")],
            call_trace=[],
            completed_tasks=[],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = long_content_response
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
                web_search_tool = WebSearchTool()
                result = await web_search_tool._arun("test query", state)
                
                # Verify formatting and truncation
                assert "Result 1:" in result
                assert "Result 2:" in result
                assert "Very Long Article Title That Should Be Preserved" in result
                assert "Short Article" in result
                assert "https://example.com/long-article" in result
                assert "https://example.com/short" in result
                
                # Verify content truncation (should end with ...)
                lines = result.split('\n')
                long_content_line = None
                for line in lines:
                    if "This is a very long piece" in line and len(line) > 300:
                        long_content_line = line
                        break
                
                if long_content_line:
                    assert long_content_line.endswith("...")
                
                # Verify short content is not truncated
                assert "Short content" in result
                
                print(f"✓ Result formatting and truncation working correctly")
                print(f"✓ Total result length: {len(result)} characters")


@pytest.mark.integration
@pytest.mark.asyncio
@pytest.mark.manual
class TestSearXNGRealIntegration:
    """
    Manual integration tests that connect to real SearXNG instance.
    These tests require the SearXNG service to be running at dns.askzaira.com:8081
    """
    
    async def test_real_searxng_connection(self):
        """
        MANUAL TEST: Real SearXNG connection test.
        
        To run this test:
        1. Ensure SearXNG is running at dns.askzaira.com:8081
        2. Run with: pytest -m manual tests/integration/test_searxng_web_search.py::TestSearXNGRealIntegration::test_real_searxng_connection
        """
        pytest.skip("Manual test - requires real SearXNG instance")
        
        # This would contain the actual integration test code
        # that connects to the real SearXNG server
        web_search_tool = WebSearchTool()
        
        state = SupervisorTaskState(
            user_guid=str(uuid4()),
            original_input="test search query",
            additional_input={},
            messages=[HumanMessage(content="test search")],
            call_trace=[],
            completed_tasks=[],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
        
        # This would make a real API call
        result = await web_search_tool._arun("Python programming", state)
        
        # Verify real results
        assert "Web search results for:" in result
        assert len(result) > 100  # Should have substantial content