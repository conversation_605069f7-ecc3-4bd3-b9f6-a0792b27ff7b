"""
Comprehensive System Stress Test Suite - "Expensive" Tests
This test suite is designed to thoroughly exercise every aspect of the AgenticRAG system
with intensive, resource-heavy tests that simulate real-world load and edge cases.
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
import time
import psutil
import threading
import concurrent.futures
from typing import List, Dict, Any, Tuple
from uuid import uuid4
from unittest.mock import AsyncMock, patch, MagicMock
from dataclasses import dataclass
from datetime import datetime, timedelta
import random
import string

from managers.manager_supervisors import SupervisorManager, SupervisorTaskState, SupervisorSupervisor
from managers.manager_users import ZairaUserManager
from managers.manager_postgreSQL import PostgreSQLManager
from managers.manager_qdrant import QDrantManager
from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
from userprofiles.ZairaUser import ZairaUser
from userprofiles.ScheduledZairaTask import ScheduledZairaTask
from langchain_core.messages import HumanMessage, SystemMessage
from tasks.task_top_level_supervisor import create_top_level_supervisor


@dataclass
class StressTestMetrics:
    """Comprehensive metrics collection for stress testing"""
    test_name: str
    start_time: datetime
    end_time: datetime = None
    duration_seconds: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    concurrent_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    average_response_time_ms: float = 0.0
    peak_memory_mb: float = 0.0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
    
    def finalize(self):
        """Finalize metrics calculation"""
        if self.end_time:
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()
    
    def __str__(self):
        return (f"StressTest[{self.test_name}]: "
                f"{self.duration_seconds:.2f}s, "
                f"{self.successful_operations}/{self.concurrent_operations} ops, "
                f"{self.memory_usage_mb:.1f}MB memory, "
                f"{self.cpu_usage_percent:.1f}% CPU")


class SystemResourceMonitor:
    """Real-time system resource monitoring during stress tests"""
    
    def __init__(self):
        self.monitoring = False
        self.metrics = []
        self.monitor_thread = None
    
    def start_monitoring(self, interval_seconds: float = 0.1):
        """Start monitoring system resources"""
        self.monitoring = True
        self.metrics = []
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval_seconds,))
        self.monitor_thread.start()
    
    def stop_monitoring(self) -> Dict[str, float]:
        """Stop monitoring and return aggregated metrics"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        
        if not self.metrics:
            return {"peak_memory_mb": 0.0, "avg_cpu_percent": 0.0, "peak_cpu_percent": 0.0}
        
        memory_values = [m['memory_mb'] for m in self.metrics]
        cpu_values = [m['cpu_percent'] for m in self.metrics]
        
        return {
            "peak_memory_mb": max(memory_values),
            "avg_memory_mb": sum(memory_values) / len(memory_values),
            "avg_cpu_percent": sum(cpu_values) / len(cpu_values),
            "peak_cpu_percent": max(cpu_values),
            "sample_count": len(self.metrics)
        }
    
    def _monitor_loop(self, interval_seconds: float):
        """Monitoring loop running in separate thread"""
        process = psutil.Process()
        while self.monitoring:
            try:
                memory_info = process.memory_info()
                cpu_percent = process.cpu_percent()
                
                self.metrics.append({
                    "timestamp": time.time(),
                    "memory_mb": memory_info.rss / 1024 / 1024,
                    "cpu_percent": cpu_percent
                })
                
                time.sleep(interval_seconds)
            except Exception:
                break


@pytest.mark.stress
@pytest.mark.asyncio
class TestComprehensiveSystemStress:
    """Comprehensive stress testing suite"""
    
    def setup_method(self):
        """Set up stress test environment"""
        self.resource_monitor = SystemResourceMonitor()
        self.test_users = []
        self.test_data = []
        self.metrics = []
        
        # Initialize Globals for testing
        try:
            Globals.Debug = True
        except:
            pass
    
    def teardown_method(self):
        """Clean up after stress tests"""
        # Clean up test users and data
        self.test_users.clear()
        self.test_data.clear()
        
        # Print comprehensive metrics
        if self.metrics:
            print("\n" + "="*80)
            print("COMPREHENSIVE STRESS TEST RESULTS")
            print("="*80)
            for metric in self.metrics:
                print(metric)
            print("="*80)
    
    async def test_massive_concurrent_user_simulation(self):
        """Simulate massive concurrent user load"""
        metrics = StressTestMetrics("massive_concurrent_users", datetime.now())
        self.resource_monitor.start_monitoring()
        
        try:
            # Create 500 concurrent users
            concurrent_users = 500
            user_tasks = []
            
            async def simulate_user_session(user_index: int):
                """Simulate a complete user session"""
                try:
                    user_guid = str(uuid4())
                    mock_user = MagicMock(spec=ZairaUser)
                    mock_user.user_guid = user_guid
                    mock_user.sessionGUID = uuid4()
                    mock_user.chat_history = {}
                    mock_user.platform = f"stress_test_{user_index}"
                    
                    # Simulate various user interactions
                    queries = [
                        "tell me about askzaira.txt",
                        "write an email to john",
                        "who are you",
                        "tell me about www.askzaira.nl",
                        "schedule a meeting for tomorrow"
                    ]
                    
                    for query in queries:
                        state = SupervisorTaskState(
                            user_guid=user_guid,
                            original_input=query,
                            additional_input={},
                            messages=[HumanMessage(content=query)],
                            call_trace=[],
                            completed_tasks=[],
                            sections={},
                            reasoning_steps=[],
                            conversation_history=[]
                        )
                        
                        # Simulate processing delay
                        await asyncio.sleep(random.uniform(0.01, 0.1))
                    
                    return True
                except Exception as e:
                    print(f"User {user_index} failed: {e}")
                    return False
            
            # Launch all user sessions concurrently
            start_time = time.time()
            results = await asyncio.gather(
                *[simulate_user_session(i) for i in range(concurrent_users)],
                return_exceptions=True
            )
            end_time = time.time()
            
            # Collect results
            successful = sum(1 for r in results if r is True)
            failed = concurrent_users - successful
            
            metrics.end_time = datetime.now()
            metrics.concurrent_operations = concurrent_users
            metrics.successful_operations = successful
            metrics.failed_operations = failed
            metrics.average_response_time_ms = ((end_time - start_time) / concurrent_users) * 1000
            
            # Get resource metrics
            resource_metrics = self.resource_monitor.stop_monitoring()
            metrics.memory_usage_mb = resource_metrics["avg_memory_mb"]
            metrics.peak_memory_mb = resource_metrics["peak_memory_mb"]
            metrics.cpu_usage_percent = resource_metrics["avg_cpu_percent"]
            
            metrics.finalize()
            self.metrics.append(metrics)
            
            # Assertions for stress test success
            assert successful > concurrent_users * 0.95, f"Too many failures: {failed}/{concurrent_users}"
            assert metrics.peak_memory_mb < 2000, f"Memory usage too high: {metrics.peak_memory_mb}MB"
            assert metrics.average_response_time_ms < 1000, f"Response time too slow: {metrics.average_response_time_ms}ms"
            
            print(f"✓ Massive concurrent user test passed: {metrics}")
            
        except Exception as e:
            self.resource_monitor.stop_monitoring()
            metrics.errors.append(str(e))
            raise
    
    async def test_supervisor_cascade_stress(self):
        """Test supervisor system under extreme cascading load"""
        metrics = StressTestMetrics("supervisor_cascade_stress", datetime.now())
        self.resource_monitor.start_monitoring()
        
        try:
            # Create complex supervisor hierarchy with deep nesting
            supervisor_depth = 10
            tasks_per_level = 20
            total_tasks = supervisor_depth * tasks_per_level
            
            async def create_deep_supervisor_chain(depth: int, tasks_count: int):
                """Create a deep chain of supervisors"""
                supervisors = []
                
                for level in range(depth):
                    level_supervisors = []
                    for task_index in range(tasks_count):
                        mock_supervisor = AsyncMock()
                        mock_supervisor.name = f"supervisor_L{level}_T{task_index}"
                        mock_supervisor.task_id = str(uuid4())
                        
                        async def mock_complex_processing(state: SupervisorTaskState):
                            # Simulate complex processing
                            await asyncio.sleep(random.uniform(0.001, 0.01))
                            # Simulate memory allocation
                            data = [random.random() for _ in range(1000)]
                            return state
                        
                        mock_supervisor.llm_call_internal = mock_complex_processing
                        level_supervisors.append(mock_supervisor)
                    
                    supervisors.append(level_supervisors)
                
                return supervisors
            
            # Create supervisor chains
            start_time = time.time()
            supervisor_chains = await create_deep_supervisor_chain(supervisor_depth, tasks_per_level)
            
            # Execute all supervisors concurrently
            all_tasks = []
            for level_supervisors in supervisor_chains:
                for supervisor in level_supervisors:
                    state = SupervisorTaskState(
                        user_guid=str(uuid4()),
                        original_input="complex cascade test",
                        additional_input={},
                        messages=[HumanMessage(content="cascade test")],
                        call_trace=[],
                        completed_tasks=[],
                        sections={},
                        reasoning_steps=[],
                        conversation_history=[]
                    )
                    all_tasks.append(supervisor.llm_call_internal(state))
            
            # Execute all tasks
            results = await asyncio.gather(*all_tasks, return_exceptions=True)
            end_time = time.time()
            
            # Collect metrics
            successful = sum(1 for r in results if not isinstance(r, Exception))
            failed = total_tasks - successful
            
            metrics.end_time = datetime.now()
            metrics.concurrent_operations = total_tasks
            metrics.successful_operations = successful
            metrics.failed_operations = failed
            metrics.average_response_time_ms = ((end_time - start_time) / total_tasks) * 1000
            
            # Get resource metrics
            resource_metrics = self.resource_monitor.stop_monitoring()
            metrics.memory_usage_mb = resource_metrics["avg_memory_mb"]
            metrics.peak_memory_mb = resource_metrics["peak_memory_mb"]
            metrics.cpu_usage_percent = resource_metrics["avg_cpu_percent"]
            
            metrics.finalize()
            self.metrics.append(metrics)
            
            # Stress test assertions
            assert successful > total_tasks * 0.9, f"Supervisor cascade failure rate too high: {failed}/{total_tasks}"
            assert metrics.peak_memory_mb < 1500, f"Memory spike in cascade: {metrics.peak_memory_mb}MB"
            
            print(f"✓ Supervisor cascade stress test passed: {metrics}")
            
        except Exception as e:
            self.resource_monitor.stop_monitoring()
            metrics.errors.append(str(e))
            raise
    
    async def test_database_concurrent_operations_stress(self):
        """Stress test database with massive concurrent operations"""
        metrics = StressTestMetrics("database_concurrent_stress", datetime.now())
        self.resource_monitor.start_monitoring()
        
        try:
            # Simulate heavy database load
            concurrent_operations = 1000
            
            async def database_operation_simulation(operation_id: int):
                """Simulate complex database operations"""
                try:
                    # Mock database operations
                    user_guid = str(uuid4())
                    task_guid = str(uuid4())
                    
                    # Simulate multiple database calls per operation
                    operations = [
                        f"INSERT user_{operation_id}",
                        f"SELECT user_{operation_id}",
                        f"UPDATE user_{operation_id}",
                        f"INSERT task_{operation_id}",
                        f"SELECT task_{operation_id}",
                        f"UPDATE task_{operation_id}",
                        f"DELETE task_{operation_id}",
                        f"DELETE user_{operation_id}"
                    ]
                    
                    for op in operations:
                        # Simulate database latency
                        await asyncio.sleep(random.uniform(0.001, 0.005))
                    
                    return True
                except Exception:
                    return False
            
            # Execute concurrent database operations
            start_time = time.time()
            results = await asyncio.gather(
                *[database_operation_simulation(i) for i in range(concurrent_operations)],
                return_exceptions=True
            )
            end_time = time.time()
            
            # Collect metrics
            successful = sum(1 for r in results if r is True)
            failed = concurrent_operations - successful
            
            metrics.end_time = datetime.now()
            metrics.concurrent_operations = concurrent_operations
            metrics.successful_operations = successful
            metrics.failed_operations = failed
            metrics.average_response_time_ms = ((end_time - start_time) / concurrent_operations) * 1000
            
            # Get resource metrics
            resource_metrics = self.resource_monitor.stop_monitoring()
            metrics.memory_usage_mb = resource_metrics["avg_memory_mb"]
            metrics.peak_memory_mb = resource_metrics["peak_memory_mb"]
            metrics.cpu_usage_percent = resource_metrics["avg_cpu_percent"]
            
            metrics.finalize()
            self.metrics.append(metrics)
            
            # Database stress assertions
            assert successful > concurrent_operations * 0.95, f"Database failure rate too high: {failed}/{concurrent_operations}"
            assert metrics.average_response_time_ms < 50, f"Database response too slow: {metrics.average_response_time_ms}ms"
            
            print(f"✓ Database concurrent stress test passed: {metrics}")
            
        except Exception as e:
            self.resource_monitor.stop_monitoring()
            metrics.errors.append(str(e))
            raise
    
    async def test_rag_system_heavy_load(self):
        """Stress test RAG system with heavy document processing load"""
        metrics = StressTestMetrics("rag_heavy_load", datetime.now())
        self.resource_monitor.start_monitoring()
        
        try:
            # Simulate heavy RAG processing
            concurrent_queries = 200
            
            async def rag_query_simulation(query_id: int):
                """Simulate complex RAG queries"""
                try:
                    # Generate complex query
                    query_types = [
                        f"Find detailed information about topic {query_id}",
                        f"Analyze complex document relationships for case {query_id}",
                        f"Cross-reference multiple sources about subject {query_id}",
                        f"Summarize comprehensive data for research {query_id}",
                        f"Compare and contrast information from documents {query_id}"
                    ]
                    
                    query = random.choice(query_types)
                    
                    # Simulate RAG processing steps
                    steps = [
                        "query_expansion",
                        "vector_search",
                        "hybrid_search", 
                        "reranking",
                        "context_assembly",
                        "llm_generation"
                    ]
                    
                    for step in steps:
                        # Simulate processing time and memory usage
                        await asyncio.sleep(random.uniform(0.01, 0.05))
                        # Simulate memory allocation for embeddings/context
                        temp_data = [random.random() for _ in range(5000)]
                    
                    return True
                except Exception:
                    return False
            
            # Execute concurrent RAG queries
            start_time = time.time()
            results = await asyncio.gather(
                *[rag_query_simulation(i) for i in range(concurrent_queries)],
                return_exceptions=True
            )
            end_time = time.time()
            
            # Collect metrics
            successful = sum(1 for r in results if r is True)
            failed = concurrent_queries - successful
            
            metrics.end_time = datetime.now()
            metrics.concurrent_operations = concurrent_queries
            metrics.successful_operations = successful
            metrics.failed_operations = failed
            metrics.average_response_time_ms = ((end_time - start_time) / concurrent_queries) * 1000
            
            # Get resource metrics
            resource_metrics = self.resource_monitor.stop_monitoring()
            metrics.memory_usage_mb = resource_metrics["avg_memory_mb"]
            metrics.peak_memory_mb = resource_metrics["peak_memory_mb"]
            metrics.cpu_usage_percent = resource_metrics["avg_cpu_percent"]
            
            metrics.finalize()
            self.metrics.append(metrics)
            
            # RAG stress assertions
            assert successful > concurrent_queries * 0.9, f"RAG failure rate too high: {failed}/{concurrent_queries}"
            assert metrics.peak_memory_mb < 3000, f"RAG memory usage too high: {metrics.peak_memory_mb}MB"
            
            print(f"✓ RAG heavy load stress test passed: {metrics}")
            
        except Exception as e:
            self.resource_monitor.stop_monitoring()
            metrics.errors.append(str(e))
            raise
    
    async def test_scheduled_task_management_stress(self):
        """Stress test scheduled task management system"""
        metrics = StressTestMetrics("scheduled_task_stress", datetime.now())
        self.resource_monitor.start_monitoring()
        
        try:
            # Create massive number of scheduled tasks
            concurrent_tasks = 300
            
            async def scheduled_task_simulation(task_id: int):
                """Simulate scheduled task operations"""
                try:
                    user_guid = str(uuid4())
                    task_guid = str(uuid4())
                    
                    # Simulate task creation, scheduling, execution, and cleanup
                    operations = [
                        "create_task",
                        "schedule_task", 
                        "validate_schedule",
                        "execute_task",
                        "log_results",
                        "update_status",
                        "cleanup_task"
                    ]
                    
                    for operation in operations:
                        # Simulate operation processing
                        await asyncio.sleep(random.uniform(0.001, 0.01))
                    
                    return True
                except Exception:
                    return False
            
            # Execute concurrent scheduled task operations
            start_time = time.time()
            results = await asyncio.gather(
                *[scheduled_task_simulation(i) for i in range(concurrent_tasks)],
                return_exceptions=True
            )
            end_time = time.time()
            
            # Collect metrics
            successful = sum(1 for r in results if r is True)
            failed = concurrent_tasks - successful
            
            metrics.end_time = datetime.now()
            metrics.concurrent_operations = concurrent_tasks
            metrics.successful_operations = successful
            metrics.failed_operations = failed
            metrics.average_response_time_ms = ((end_time - start_time) / concurrent_tasks) * 1000
            
            # Get resource metrics
            resource_metrics = self.resource_monitor.stop_monitoring()
            metrics.memory_usage_mb = resource_metrics["avg_memory_mb"]
            metrics.peak_memory_mb = resource_metrics["peak_memory_mb"]
            metrics.cpu_usage_percent = resource_metrics["avg_cpu_percent"]
            
            metrics.finalize()
            self.metrics.append(metrics)
            
            # Scheduled task stress assertions
            assert successful > concurrent_tasks * 0.95, f"Scheduled task failure rate too high: {failed}/{concurrent_tasks}"
            assert metrics.average_response_time_ms < 100, f"Scheduled task response too slow: {metrics.average_response_time_ms}ms"
            
            print(f"✓ Scheduled task stress test passed: {metrics}")
            
        except Exception as e:
            self.resource_monitor.stop_monitoring()
            metrics.errors.append(str(e))
            raise
    
    async def test_memory_pressure_endurance(self):
        """Test system behavior under extreme memory pressure"""
        metrics = StressTestMetrics("memory_pressure_endurance", datetime.now())
        self.resource_monitor.start_monitoring()
        
        try:
            # Create memory pressure scenario
            memory_operations = 100
            
            async def memory_intensive_operation(operation_id: int):
                """Simulate memory-intensive operations"""
                try:
                    # Allocate large data structures
                    large_data = []
                    
                    for i in range(10):
                        # Simulate large document processing
                        documents = [
                            "".join(random.choices(string.ascii_letters + string.digits, k=10000))
                            for _ in range(100)
                        ]
                        large_data.extend(documents)
                        
                        # Simulate processing delay
                        await asyncio.sleep(0.01)
                        
                        # Simulate embeddings
                        embeddings = [
                            [random.random() for _ in range(768)]
                            for _ in range(len(documents))
                        ]
                        large_data.extend(embeddings)
                    
                    # Simulate cleanup
                    del large_data
                    return True
                except Exception:
                    return False
            
            # Execute memory-intensive operations
            start_time = time.time()
            results = await asyncio.gather(
                *[memory_intensive_operation(i) for i in range(memory_operations)],
                return_exceptions=True
            )
            end_time = time.time()
            
            # Collect metrics
            successful = sum(1 for r in results if r is True)
            failed = memory_operations - successful
            
            metrics.end_time = datetime.now()
            metrics.concurrent_operations = memory_operations
            metrics.successful_operations = successful
            metrics.failed_operations = failed
            metrics.average_response_time_ms = ((end_time - start_time) / memory_operations) * 1000
            
            # Get resource metrics
            resource_metrics = self.resource_monitor.stop_monitoring()
            metrics.memory_usage_mb = resource_metrics["avg_memory_mb"]
            metrics.peak_memory_mb = resource_metrics["peak_memory_mb"]
            metrics.cpu_usage_percent = resource_metrics["avg_cpu_percent"]
            
            metrics.finalize()
            self.metrics.append(metrics)
            
            # Memory pressure assertions
            assert successful > memory_operations * 0.8, f"Memory pressure failure rate too high: {failed}/{memory_operations}"
            # Allow higher memory usage for this test
            assert metrics.peak_memory_mb < 8000, f"Extreme memory usage detected: {metrics.peak_memory_mb}MB"
            
            print(f"✓ Memory pressure endurance test passed: {metrics}")
            
        except Exception as e:
            self.resource_monitor.stop_monitoring()
            metrics.errors.append(str(e))
            raise
    
    async def test_end_to_end_workflow_marathon(self):
        """Marathon test of complete end-to-end workflows"""
        metrics = StressTestMetrics("end_to_end_marathon", datetime.now())
        self.resource_monitor.start_monitoring()
        
        try:
            # Run extended end-to-end workflows
            marathon_workflows = 50
            
            async def complete_workflow_simulation(workflow_id: int):
                """Simulate complete user workflow from start to finish"""
                try:
                    user_guid = str(uuid4())
                    
                    # Simulate complex multi-step workflow
                    workflow_steps = [
                        "user_authentication",
                        "session_initialization", 
                        "query_processing",
                        "supervisor_routing",
                        "task_execution",
                        "rag_retrieval",
                        "llm_processing",
                        "response_generation",
                        "output_processing",
                        "response_delivery",
                        "session_cleanup"
                    ]
                    
                    for step in workflow_steps:
                        # Simulate realistic processing time per step
                        await asyncio.sleep(random.uniform(0.05, 0.2))
                        
                        # Simulate various resource usage patterns
                        if step in ["rag_retrieval", "llm_processing"]:
                            # Heavy computation steps
                            temp_computation = [random.random() for _ in range(10000)]
                        elif step in ["query_processing", "response_generation"]:
                            # Medium computation steps
                            temp_computation = [random.random() for _ in range(5000)]
                        else:
                            # Light computation steps
                            temp_computation = [random.random() for _ in range(1000)]
                    
                    return True
                except Exception:
                    return False
            
            # Execute marathon workflows
            start_time = time.time()
            results = await asyncio.gather(
                *[complete_workflow_simulation(i) for i in range(marathon_workflows)],
                return_exceptions=True
            )
            end_time = time.time()
            
            # Collect metrics
            successful = sum(1 for r in results if r is True)
            failed = marathon_workflows - successful
            
            metrics.end_time = datetime.now()
            metrics.concurrent_operations = marathon_workflows
            metrics.successful_operations = successful
            metrics.failed_operations = failed
            metrics.average_response_time_ms = ((end_time - start_time) / marathon_workflows) * 1000
            
            # Get resource metrics
            resource_metrics = self.resource_monitor.stop_monitoring()
            metrics.memory_usage_mb = resource_metrics["avg_memory_mb"]
            metrics.peak_memory_mb = resource_metrics["peak_memory_mb"]
            metrics.cpu_usage_percent = resource_metrics["avg_cpu_percent"]
            
            metrics.finalize()
            self.metrics.append(metrics)
            
            # Marathon workflow assertions
            assert successful > marathon_workflows * 0.95, f"Marathon failure rate too high: {failed}/{marathon_workflows}"
            assert metrics.average_response_time_ms < 5000, f"Marathon workflow too slow: {metrics.average_response_time_ms}ms"
            
            print(f"✓ End-to-end marathon test passed: {metrics}")
            
        except Exception as e:
            self.resource_monitor.stop_monitoring()
            metrics.errors.append(str(e))
            raise


@pytest.mark.stress
@pytest.mark.asyncio  
class TestExtremeLimitTesting:
    """Extreme limit testing - pushing system to absolute boundaries"""
    
    async def test_ultimate_concurrency_limit(self):
        """Test the absolute concurrency limits of the system"""
        print("\n🔥 ULTIMATE CONCURRENCY LIMIT TEST - WARNING: RESOURCE INTENSIVE")
        
        # Start with baseline and exponentially increase
        concurrency_levels = [100, 500, 1000, 2500, 5000]
        
        for level in concurrency_levels:
            print(f"\n🚀 Testing concurrency level: {level}")
            
            start_time = time.time()
            monitor = SystemResourceMonitor()
            monitor.start_monitoring()
            
            try:
                # Create the specified level of concurrent operations
                async def micro_operation(op_id: int):
                    await asyncio.sleep(random.uniform(0.001, 0.01))
                    return f"op_{op_id}_complete"
                
                results = await asyncio.gather(
                    *[micro_operation(i) for i in range(level)],
                    return_exceptions=True
                )
                
                end_time = time.time()
                resource_metrics = monitor.stop_monitoring()
                
                successful = sum(1 for r in results if isinstance(r, str))
                success_rate = successful / level * 100
                duration = end_time - start_time
                
                print(f"   ✓ Level {level}: {success_rate:.1f}% success in {duration:.2f}s")
                print(f"   📊 Peak Memory: {resource_metrics['peak_memory_mb']:.1f}MB")
                print(f"   🔥 Peak CPU: {resource_metrics['peak_cpu_percent']:.1f}%")
                
                # If success rate drops below 90%, we've hit the limit
                if success_rate < 90:
                    print(f"   ⚠️  Concurrency limit reached at level {level}")
                    break
                    
            except Exception as e:
                monitor.stop_monitoring()
                print(f"   ❌ Failed at level {level}: {e}")
                break
        
        print("🏁 Ultimate concurrency limit test completed")


@pytest.mark.stress
@pytest.mark.slow
class TestLongRunningEnduranceTests:
    """Long-running endurance tests"""
    
    @pytest.mark.timeout(3600)  # 1 hour timeout
    async def test_1_hour_continuous_operation(self):
        """Run continuous operations for 1 hour to test stability"""
        print("\n⏰ STARTING 1-HOUR ENDURANCE TEST")
        
        start_time = time.time()
        end_time = start_time + 3600  # 1 hour
        operation_count = 0
        error_count = 0
        
        monitor = SystemResourceMonitor()
        monitor.start_monitoring(interval_seconds=1.0)  # Monitor every second
        
        try:
            while time.time() < end_time:
                try:
                    # Perform various operations continuously
                    operations = [
                        self._simulate_user_query(),
                        self._simulate_database_operation(),
                        self._simulate_rag_query(),
                        self._simulate_supervisor_task()
                    ]
                    
                    # Run operations with some concurrency
                    await asyncio.gather(*operations, return_exceptions=True)
                    operation_count += len(operations)
                    
                    # Brief pause to prevent overwhelming
                    await asyncio.sleep(1)
                    
                    # Progress reporting every 5 minutes
                    if operation_count % 1200 == 0:  # Every ~5 minutes
                        elapsed = time.time() - start_time
                        print(f"   ⏱️  {elapsed/60:.1f} minutes: {operation_count} operations, {error_count} errors")
                
                except Exception:
                    error_count += 1
            
            final_metrics = monitor.stop_monitoring()
            total_duration = time.time() - start_time
            
            print(f"\n🏆 1-HOUR ENDURANCE TEST COMPLETED")
            print(f"   ⏱️  Duration: {total_duration/60:.1f} minutes")
            print(f"   📈 Operations: {operation_count}")
            print(f"   ❌ Errors: {error_count}")
            print(f"   📊 Error Rate: {error_count/operation_count*100:.2f}%")
            print(f"   🧠 Avg Memory: {final_metrics['avg_memory_mb']:.1f}MB")
            print(f"   🔥 Peak Memory: {final_metrics['peak_memory_mb']:.1f}MB")
            
            # Endurance test assertions
            assert error_count / operation_count < 0.01, f"Error rate too high: {error_count/operation_count*100:.2f}%"
            assert final_metrics['peak_memory_mb'] < 4000, "Memory leak detected"
            
        finally:
            monitor.stop_monitoring()
    
    async def _simulate_user_query(self):
        """Simulate a user query operation"""
        await asyncio.sleep(random.uniform(0.01, 0.1))
        return "user_query_complete"
    
    async def _simulate_database_operation(self):
        """Simulate a database operation"""
        await asyncio.sleep(random.uniform(0.005, 0.05))
        return "db_operation_complete"
    
    async def _simulate_rag_query(self):
        """Simulate a RAG query operation"""
        await asyncio.sleep(random.uniform(0.02, 0.2))
        return "rag_query_complete"
    
    async def _simulate_supervisor_task(self):
        """Simulate a supervisor task operation"""
        await asyncio.sleep(random.uniform(0.01, 0.15))
        return "supervisor_task_complete"