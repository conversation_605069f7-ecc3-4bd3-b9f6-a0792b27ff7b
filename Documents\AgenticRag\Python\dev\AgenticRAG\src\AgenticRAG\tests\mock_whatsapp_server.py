"""
Mock WhatsApp API server for testing
"""
import asyncio
import json
import time
from aiohttp import web, ClientSession
from typing import Dict, List, Optional
import logging

class MockWhatsAppServer:
    """Mock WhatsApp API server for testing purposes"""
    
    def __init__(self, port: int = 8080):
        self.port = port
        self.app = web.Application()
        self.messages_sent: List[Dict] = []
        self.webhook_url: Optional[str] = None
        self.verify_token: str = "test_verify_token"
        self.access_tokens: Dict[str, bool] = {
            "valid_token_123": True,
            "expired_token_456": False
        }
        self.phone_numbers: Dict[str, Dict] = {
            "***************": {
                "display_name": "Test Business",
                "verified": True
            }
        }
        self.setup_routes()
    
    def setup_routes(self):
        """Setup mock API routes"""
        # WhatsApp Cloud API routes
        self.app.router.add_post('/v18.0/{phone_number_id}/messages', self.send_message)
        self.app.router.add_get('/v18.0/{phone_number_id}', self.get_phone_info)
        
        # Webhook management routes
        self.app.router.add_post('/v18.0/{phone_number_id}/webhooks', self.set_webhook)
        self.app.router.add_get('/v18.0/{phone_number_id}/webhooks', self.get_webhook)
        
        # Test utility routes
        self.app.router.add_get('/test/messages', self.get_sent_messages)
        self.app.router.add_post('/test/simulate_incoming', self.simulate_incoming_message)
        self.app.router.add_delete('/test/reset', self.reset_state)
        
        # Health check
        self.app.router.add_get('/health', self.health_check)
    
    async def send_message(self, request: web.Request) -> web.Response:
        """Mock message sending endpoint"""
        phone_number_id = request.match_info['phone_number_id']
        
        # Validate authorization
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return web.json_response(
                {"error": {"message": "Invalid access token"}}, 
                status=401
            )
        
        token = auth_header.replace('Bearer ', '')
        if token not in self.access_tokens or not self.access_tokens[token]:
            return web.json_response(
                {"error": {"message": "Invalid or expired access token"}}, 
                status=401
            )
        
        # Validate phone number ID
        if phone_number_id not in self.phone_numbers:
            return web.json_response(
                {"error": {"message": "Invalid phone number ID"}}, 
                status=400
            )
        
        try:
            data = await request.json()
        except json.JSONDecodeError:
            return web.json_response(
                {"error": {"message": "Invalid JSON payload"}}, 
                status=400
            )
        
        # Validate required fields
        required_fields = ['messaging_product', 'to', 'type']
        for field in required_fields:
            if field not in data:
                return web.json_response(
                    {"error": {"message": f"Missing required field: {field}"}}, 
                    status=400
                )
        
        if data['messaging_product'] != 'whatsapp':
            return web.json_response(
                {"error": {"message": "Invalid messaging product"}}, 
                status=400
            )
        
        # Validate phone number format
        to_number = data['to']
        if not to_number.isdigit() or len(to_number) < 10:
            return web.json_response(
                {"error": {"message": "Invalid phone number format"}}, 
                status=400
            )
        
        # Simulate rate limiting
        if len(self.messages_sent) > 100:
            return web.json_response(
                {"error": {"message": "Rate limit exceeded"}}, 
                status=429
            )
        
        # Store message
        message_id = f"wamid.mock_{int(time.time())}_{len(self.messages_sent)}"
        message_record = {
            "id": message_id,
            "phone_number_id": phone_number_id,
            "to": to_number,
            "data": data,
            "timestamp": time.time(),
            "status": "sent"
        }
        self.messages_sent.append(message_record)
        
        # Simulate delivery status
        asyncio.create_task(self._simulate_delivery_status(message_id, to_number))
        
        return web.json_response({
            "messaging_product": "whatsapp",
            "contacts": [{"input": to_number, "wa_id": to_number}],
            "messages": [{"id": message_id}]
        })
    
    async def _simulate_delivery_status(self, message_id: str, to_number: str):
        """Simulate message delivery status updates"""
        if not self.webhook_url:
            return
        
        # Simulate delivery after 1 second
        await asyncio.sleep(1)
        
        delivery_webhook = {
            "object": "whatsapp_business_account",
            "entry": [{
                "id": "mock_business_account",
                "changes": [{
                    "value": {
                        "messaging_product": "whatsapp",
                        "metadata": {
                            "display_phone_number": "***********",
                            "phone_number_id": "***************"
                        },
                        "statuses": [{
                            "id": message_id,
                            "status": "delivered",
                            "timestamp": str(int(time.time())),
                            "recipient_id": to_number
                        }]
                    },
                    "field": "messages"
                }]
            }]
        }
        
        try:
            async with ClientSession() as session:
                await session.post(self.webhook_url, json=delivery_webhook)
        except Exception as e:
            logging.warning(f"Failed to send delivery webhook: {e}")
    
    async def get_phone_info(self, request: web.Request) -> web.Response:
        """Get phone number information"""
        phone_number_id = request.match_info['phone_number_id']
        
        if phone_number_id not in self.phone_numbers:
            return web.json_response(
                {"error": {"message": "Phone number not found"}}, 
                status=404
            )
        
        return web.json_response({
            "id": phone_number_id,
            "display_phone_number": "+***********",
            "verified_name": self.phone_numbers[phone_number_id]["display_name"],
            "status": "CONNECTED"
        })
    
    async def set_webhook(self, request: web.Request) -> web.Response:
        """Set webhook URL"""
        try:
            data = await request.json()
            self.webhook_url = data.get('callback_url')
            return web.json_response({"success": True})
        except json.JSONDecodeError:
            return web.json_response(
                {"error": {"message": "Invalid JSON"}}, 
                status=400
            )
    
    async def get_webhook(self, request: web.Request) -> web.Response:
        """Get current webhook URL"""
        return web.json_response({
            "callback_url": self.webhook_url,
            "verify_token": self.verify_token
        })
    
    async def simulate_incoming_message(self, request: web.Request) -> web.Response:
        """Simulate incoming message from WhatsApp user"""
        try:
            data = await request.json()
        except json.JSONDecodeError:
            return web.json_response(
                {"error": {"message": "Invalid JSON"}}, 
                status=400
            )
        
        from_number = data.get('from', '***********')
        message_text = data.get('message', 'Test message')
        message_type = data.get('type', 'text')
        
        webhook_data = {
            "object": "whatsapp_business_account",
            "entry": [{
                "id": "mock_business_account",
                "changes": [{
                    "value": {
                        "messaging_product": "whatsapp",
                        "metadata": {
                            "display_phone_number": "***********",
                            "phone_number_id": "***************"
                        },
                        "contacts": [{
                            "profile": {"name": "Test User"},
                            "wa_id": from_number
                        }],
                        "messages": [{
                            "from": from_number,
                            "id": f"wamid.mock_incoming_{int(time.time())}",
                            "timestamp": str(int(time.time())),
                            "type": message_type
                        }]
                    },
                    "field": "messages"
                }]
            }]
        }
        
        # Add message content based on type
        if message_type == 'text':
            webhook_data["entry"][0]["changes"][0]["value"]["messages"][0]["text"] = {
                "body": message_text
            }
        
        # Send to webhook if configured
        if self.webhook_url:
            try:
                async with ClientSession() as session:
                    await session.post(self.webhook_url, json=webhook_data)
                return web.json_response({"success": True, "webhook_sent": True})
            except Exception as e:
                return web.json_response({
                    "success": True, 
                    "webhook_sent": False, 
                    "error": str(e)
                })
        
        return web.json_response({"success": True, "webhook_sent": False})
    
    async def get_sent_messages(self, request: web.Request) -> web.Response:
        """Get all sent messages for testing"""
        return web.json_response({
            "messages": self.messages_sent,
            "count": len(self.messages_sent)
        })
    
    async def reset_state(self, request: web.Request) -> web.Response:
        """Reset server state for testing"""
        self.messages_sent.clear()
        self.webhook_url = None
        return web.json_response({"success": True})
    
    async def health_check(self, request: web.Request) -> web.Response:
        """Health check endpoint"""
        return web.json_response({
            "status": "healthy",
            "messages_sent": len(self.messages_sent),
            "webhook_configured": self.webhook_url is not None
        })
    
    async def start(self):
        """Start the mock server"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, 'localhost', self.port)
        await site.start()
        print(f"Mock WhatsApp server started on http://localhost:{self.port}")
        return runner
    
    async def stop(self, runner):
        """Stop the mock server"""
        await runner.cleanup()

# Utility functions for testing
async def start_mock_server(port: int = 8080) -> MockWhatsAppServer:
    """Start mock WhatsApp server for testing"""
    server = MockWhatsAppServer(port)
    await server.start()
    return server

async def send_test_message(server_port: int = 8080, to_number: str = "***********", message: str = "Test message"):
    """Send a test message through the mock server"""
    url = f"http://localhost:{server_port}/test/simulate_incoming"
    data = {
        "from": to_number,
        "message": message,
        "type": "text"
    }
    
    async with ClientSession() as session:
        async with session.post(url, json=data) as response:
            return await response.json()

if __name__ == "__main__":
    # Run mock server standalone for manual testing
    async def main():
        server = MockWhatsAppServer(8080)
        runner = await server.start()
        
        print("Mock WhatsApp server running on http://localhost:8080")
        print("Available endpoints:")
        print("  POST /v18.0/{phone_id}/messages - Send message")
        print("  GET  /test/messages - View sent messages")
        print("  POST /test/simulate_incoming - Simulate incoming message")
        print("  GET  /health - Health check")
        
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("Shutting down...")
            await server.stop(runner)
    
    asyncio.run(main())
