#!/usr/bin/env python3
"""
Demo script showing multimodal Discord bot capabilities.
This demonstrates how images and text are processed together.
"""

from imports import *
import asyncio
from pathlib import Path
import tempfile
from unittest.mock import MagicMock, AsyncMock
from userprofiles.ZairaUser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from userprofiles.permission_levels import PERMISSION_LEVELS
from endpoints.mybot_generic import MyBot_Generic
from uuid import uuid4

async def demo_multimodal_processing():
    """Demonstrate multimodal processing capabilities."""
    print("Discord Multimodal Bot Demo")
    print("=" * 50)
    
    # Create a test user
    user = <PERSON>airaUser(
        username="demo_user",
        rank=PERMISSION_LEVELS.USER,
        guid=uuid4(),
        device_guid=uuid4()
    )
    
    # Create mock bot
    mock_bot = MagicMock(spec=MyBot_Generic)
    mock_bot.send_reply = AsyncMock()
    
    print(f"Created user: {user.username}")
    print(f"Permission level: {user.rank}")
    
    # Check multimodal capabilities
    multimodal_enabled = await user.is_multimodal_enabled()
    print(f"Multimodal enabled: {multimodal_enabled}")
    
    # Test image file detection
    print("\\nTesting image file detection:")
    test_files = [
        "photo.jpg",
        "diagram.png", 
        "document.pdf",
        "chart.gif",
        "report.docx"
    ]
    
    for file in test_files:
        is_image = await user._is_image_file(file)
        icon = "[IMG]" if is_image else "[DOC]"
        print(f"  {icon} {file}: {'Image' if is_image else 'Document'}")
    
    # Get supported formats
    supported_formats = await user.get_supported_image_formats()
    print(f"\\nSupported image formats: {', '.join(supported_formats)}")
    
    # Simulate processing a message with image
    print("\\nSimulating Discord message with image attachment...")
    
    # Create a temporary test image file
    with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as f:
        f.write(b"fake_image_data")
        temp_image_path = f.name
    
    try:
        # Mock the actual image processing to avoid API calls in demo
        async def mock_process_image(image_path):
            filename = Path(image_path).name
            return f"**Image: {filename}**\\nThis appears to be a photograph showing a landscape with mountains and a clear blue sky. The image has good composition and natural lighting."
        
        # Replace the actual method with our mock for demo
        user._process_image_attachment = mock_process_image
        
        # Process attachments
        processed_content = await user._process_attachments_multimodal([temp_image_path])
        
        print("\\nProcessed attachment content:")
        print("-" * 30)
        print(processed_content)
        print("-" * 30)
        
        # Simulate full message processing
        original_message = "Can you tell me what's in this image?"
        
        # Mock start_task to capture the enhanced message
        enhanced_message = None
        async def mock_start_task(complete_message, **kwargs):
            nonlocal enhanced_message
            enhanced_message = complete_message
            print("\\nEnhanced message that would be sent to AI:")
            print("=" * 50)
            print(enhanced_message)
            print("=" * 50)
        
        user.start_task = mock_start_task
        
        # Process the complete message
        await user.on_message(
            complete_message=original_message,
            calling_bot=mock_bot,
            attachments=[temp_image_path]
        )
        
        print("\\nDemo completed successfully!")
        print("\\nKey features demonstrated:")
        print("• Automatic image detection")
        print("• Multimodal AI processing") 
        print("• Enhanced message creation")
        print("• Permission-based access control")
        print("• Error handling and fallbacks")
        
    finally:
        # Cleanup
        try:
            Path(temp_image_path).unlink()
        except:
            pass

async def demo_error_handling():
    """Demonstrate error handling in multimodal processing."""
    print("\\nError Handling Demo")
    print("=" * 30)
    
    user = ZairaUser(
        username="test_user",
        rank=PERMISSION_LEVELS.USER,
        guid=uuid4(),
        device_guid=uuid4()
    )
    
    # Test with non-existent file
    print("Testing with non-existent image file...")
    result = await user._process_image_attachment("/non/existent/file.jpg")
    print(f"Result: {result[:100]}...")
    
    # Test with guest user (should be disabled)
    guest_user = ZairaUser(
        username="guest",
        rank=PERMISSION_LEVELS.GUEST,
        guid=uuid4(),
        device_guid=uuid4()
    )
    
    multimodal_enabled = await guest_user.is_multimodal_enabled()
    print(f"\\nGuest user multimodal enabled: {multimodal_enabled}")
    
    print("\\nError handling tests completed!")

if __name__ == "__main__":
    asyncio.run(demo_multimodal_processing())
    asyncio.run(demo_error_handling())