from imports import *

from managers.manager_supervisors import <PERSON><PERSON><PERSON><PERSON><PERSON>, SupervisorSupervisor, SupervisorSupervisor_ChainOfThought, SupervisorTask_Base

from tasks.inputs.quick_search import create_task_quick_rag_search, create_task_quick_llm_search, create_task_quick_complexity_search
from tasks.processing.task_email_generator import create_task_email_generator
from tasks.inputs.task_retrieval import create_supervisor_retrieval
from tasks.inputs.task_imap import create_task_imap_receiver
from tasks.inputs.task_gdrive import create_task_gdrive_receiver
from tasks.etc.task_chat_session import create_task_manage_chat_sessions
from tasks.processing.task_agenda_planner import create_supervisor_agenda_planner
from tasks.inputs.task_scheduled_request_manager import create_task_scheduled_request_manager
from tasks.inputs.task_language_detector import create_task_language_detector
from tasks.inputs.imap_idle_activate import create_task_imap_idle_activate

from tasks.task_top_output_supervisor import create_top_output_supervisor

# Publishing team tools
# def review_content(input: str):
#     "Reviews the content"
#     prompt = f"Review the given content {input}, make necessary corrections and rewrite in a positive tone"
#     return model.invoke(prompt)

# def writer(text_to_dump: str):
#     "Writes in txt file"
#     with open("final.txt", "w") as f:
#         f.write(text_to_dump)
#     return "Text has been written to final.txt"

async def create_top_level_supervisor() -> SupervisorSupervisor:
    class TaskCreator:
        quick_rag_search: SupervisorTask_Base = None
        quick_llm_search: SupervisorTask_Base = None
        quick_complexity: SupervisorTask_Base = None
        language_detector: SupervisorTask_Base = None

        search_supervisor: SupervisorSupervisor = None
        scheduled_task_manager: SupervisorTask_Base = None
        email_task: SupervisorSupervisor = None
        sql_excel_analyzer_task: SupervisorTask_Base = None
        calendar_supervisor: SupervisorSupervisor = None
        receipts_scanner_supervisor: SupervisorSupervisor = None
        #language_verifier_task: SupervisorTask_Base = None
        mail_receiver_task: SupervisorTask_Base = None
        gdrive_receiver_task: SupervisorTask_Base = None
        change_chat_session: SupervisorTask_Base = None
        imap_idle_activate: SupervisorTask_Base = None

        output_supervisor: SupervisorTask_Base = None

        # Created for manual calling procedure, not part of the top supervisor but absolutely mandatory
        imap_idle_task: SupervisorTask_Base = None

        async def create_small_tasks(self):
            # Language verifier?
            LogFire.log("DEBUG", "create_small_tasks called", severity="debug")
            # # Publishing team agents
            # reviewing_agent = create_react_agent(
            #     model=model,
            #     tools=[review_content],
            #     name="reviewing_expert",
            #     prompt="You are a world class reviewing expert. Don't use this tool to write the final output"
            # )

            # writing_agent = create_react_agent(
            #     model=model,
            #     tools=[writer],
            #     name="writing_expert",
            #     prompt="You are a world class writing expert. Your job is write the final content in a text file"
            # )

        async def create_supervisors(self):
            self.quick_rag_search = await create_task_quick_rag_search()
            self.quick_llm_search = await create_task_quick_llm_search()
            self.quick_complexity = await create_task_quick_complexity_search()
            self.language_detector = await create_task_language_detector()

            self.search_supervisor = await create_supervisor_retrieval()
            self.scheduled_task_manager = await create_task_scheduled_request_manager()
            self.email_task = await create_task_email_generator()
            
            self.calendar_supervisor = await create_supervisor_agenda_planner()
            self.mail_receiver_task = await create_task_imap_receiver()
            self.gdrive_receiver_task = await create_task_gdrive_receiver()
            self.change_chat_session = await create_task_manage_chat_sessions()
            self.output_supervisor = await create_top_output_supervisor()

            # # Publishing team supervisor
            # publishing_team = create_supervisor(
            #     [reviewing_agent, writing_agent],
            #     model=model,
            #     prompt=(
            #         "You are a team supervisor managing a reviewing expert and a writing expert. "
            #         "For making correction in the content, use reviewing_agent. "
            #         "For writing final content in a text file, make sure you only use writing_agent."
            #     )
            # ).compile(name="publishing_team")

            self.imap_idle_task = await create_task_imap_idle_activate()

        async def create_top_level_supervisor(self) -> SupervisorSupervisor:
            await self.create_small_tasks()
            await self.create_supervisors()

            # Create CoT-enhanced top level supervisor
            return SupervisorManager.register_supervisor(SupervisorSupervisor_ChainOfThought(name="top_level_supervisor",
                    prompt_id="Top_Supervisor_CoT_Prompt")) \
                             .add_task(self.quick_rag_search) \
                             .add_task(self.quick_llm_search) \
                             .add_task(self.quick_complexity) \
                             .add_task(self.language_detector) \
                             .add_task(self.scheduled_task_manager) \
                             .add_task(self.email_task) \
                             .add_task(self.sql_excel_analyzer_task) \
                             .add_task(self.search_supervisor) \
                             .add_task(self.calendar_supervisor) \
                             .add_task(self.mail_receiver_task) \
                             .add_task(self.gdrive_receiver_task) \
                             .add_task(self.change_chat_session) \
                             .add_task(self.output_supervisor) \
                             .add_task(self.imap_idle_task) \
                             .compile()
                             #.add_task(self.receipts_scanner_supervisor) \

    creator = TaskCreator()
    return await creator.create_top_level_supervisor()
