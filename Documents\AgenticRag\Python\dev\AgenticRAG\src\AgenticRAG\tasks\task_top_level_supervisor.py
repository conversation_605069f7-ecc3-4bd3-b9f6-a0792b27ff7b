from imports import *

from managers.manager_supervisors import <PERSON><PERSON><PERSON><PERSON><PERSON>, SupervisorSupervisor, SupervisorSupervisor_ChainOfThought, SupervisorTask_Base

from tasks.inputs.quick_search import create_task_quick_rag_search, create_task_quick_llm_search, create_task_quick_complexity_search
from tasks.processing.task_email_writer import create_supervisor_email_writer
from tasks.inputs.task_retrieval import create_supervisor_retrieval
from tasks.processing.task_joker import create_supervisor_joker
from tasks.processing.task_convert_to_language import create_task_language_verifier
from tasks.inputs.task_imap import create_task_imap_receiver
from tasks.inputs.task_gdrive import create_task_gdrive_receiver
from tasks.etc.task_chat_session import create_task_manage_chat_sessions
from tasks.processing.task_agenda_planner import create_supervisor_agenda_planner
#from tasks.processing.task_receipts_scanner import create_supervisor_receipts_scanner
from tasks.inputs.imap_idle_activate import create_task_imap_idle_activate
from tasks.inputs.task_scheduled_task_manager import create_task_scheduled_task_manager
#from tasks.inputs.task_email_checker import create_task_email_checker

from tasks.task_top_output_supervisor import create_top_output_supervisor

# Publishing team tools
# def review_content(input: str):
#     "Reviews the content"
#     prompt = f"Review the given content {input}, make necessary corrections and rewrite in a positive tone"
#     return model.invoke(prompt)

# def writer(text_to_dump: str):
#     "Writes in txt file"
#     with open("final.txt", "w") as f:
#         f.write(text_to_dump)
#     return "Text has been written to final.txt"

async def create_top_level_supervisor() -> SupervisorSupervisor:
    class TaskCreator:
        quick_rag_search: SupervisorTask_Base = None
        quick_llm_search: SupervisorTask_Base = None
        quick_complexity: SupervisorTask_Base = None

        email_supervisor: SupervisorSupervisor = None
        search_supervisor: SupervisorSupervisor = None
        joker_supervisor: SupervisorSupervisor = None
        calendar_supervisor: SupervisorSupervisor = None
        receipts_scanner_supervisor: SupervisorSupervisor = None
        #language_verifier_task: SupervisorTask_Base = None
        mail_receiver_task: SupervisorTask_Base = None
        gdrive_receiver_task: SupervisorTask_Base = None
        change_chat_session: SupervisorTask_Base = None
        imap_idle_activate: SupervisorTask_Base = None
        scheduled_task_manager: SupervisorTask_Base = None
        email_checker: SupervisorTask_Base = None

        output_supervisor: SupervisorTask_Base = None

        async def create_small_tasks(self):
            # Language verifier?
            print()
            # # Publishing team agents
            # reviewing_agent = create_react_agent(
            #     model=model,
            #     tools=[review_content],
            #     name="reviewing_expert",
            #     prompt="You are a world class reviewing expert. Don't use this tool to write the final output"
            # )

            # writing_agent = create_react_agent(
            #     model=model,
            #     tools=[writer],
            #     name="writing_expert",
            #     prompt="You are a world class writing expert. Your job is write the final content in a text file"
            # )

        async def create_supervisors(self):
            self.quick_rag_search = await create_task_quick_rag_search()
            self.quick_llm_search = await create_task_quick_llm_search()
            self.quick_complexity = await create_task_quick_complexity_search()

            self.email_supervisor = await create_supervisor_email_writer()
            self.search_supervisor = await create_supervisor_retrieval()
            self.joker_supervisor = await create_supervisor_joker()
            self.calendar_supervisor = await create_supervisor_agenda_planner()
            #self.language_verifier_task = await create_task_language_verifier()
            self.mail_receiver_task = await create_task_imap_receiver()
            self.gdrive_receiver_task = await create_task_gdrive_receiver()
            self.change_chat_session = await create_task_manage_chat_sessions()
            #self.receipts_scanner_supervisor = await create_supervisor_receipts_scanner()
            self.output_supervisor = await create_top_output_supervisor()
            self.imap_idle_activate = await create_task_imap_idle_activate()
            self.scheduled_task_manager = await create_task_scheduled_task_manager()
            #self.email_checker = await create_task_email_checker()

            # # Publishing team supervisor
            # publishing_team = create_supervisor(
            #     [reviewing_agent, writing_agent],
            #     model=model,
            #     prompt=(
            #         "You are a team supervisor managing a reviewing expert and a writing expert. "
            #         "For making correction in the content, use reviewing_agent. "
            #         "For writing final content in a text file, make sure you only use writing_agent."
            #     )
            # ).compile(name="publishing_team")

        async def create_top_level_supervisor(self) -> SupervisorSupervisor:
            await self.create_small_tasks()
            await self.create_supervisors()

            # Create CoT-enhanced top level supervisor
            return SupervisorManager.register_supervisor(SupervisorSupervisor_ChainOfThought(name="top_level_supervisor",
                    prompt_id="Top_Supervisor_CoT_Prompt")) \
                             .add_task(self.quick_rag_search) \
                             .add_task(self.quick_llm_search) \
                             .add_task(self.quick_complexity) \
                             .add_task(self.scheduled_task_manager) \
                             .add_task(self.email_supervisor) \
                             .add_task(self.search_supervisor) \
                             .add_task(self.calendar_supervisor) \
                             .add_task(self.mail_receiver_task) \
                             .add_task(self.gdrive_receiver_task) \
                             .add_task(self.change_chat_session) \
                             .add_task(self.output_supervisor) \
                             .add_task(self.imap_idle_activate) \
                             .compile()
                             #.add_task(self.joker_supervisor) \
                             #.add_task(self.receipts_scanner_supervisor) \

    creator = TaskCreator()
    return await creator.create_top_level_supervisor()
