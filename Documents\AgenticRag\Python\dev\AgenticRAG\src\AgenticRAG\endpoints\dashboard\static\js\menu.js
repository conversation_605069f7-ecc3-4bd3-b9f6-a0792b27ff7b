// Enhanced Dashboard Menu JavaScript
(function() {
    let menuOpen = false;
    let currentPage = 'overview';
    const menu = document.getElementById('enhancedRightMenu');
    const toggleBtn = document.getElementById('enhancedMenuToggle');
    const particlesContainer = document.getElementById('menuParticlesInside');
    
    // Initialize particles for menu
    function initializeMenuParticles() {
        const particleCount = 25;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'menu-particle';
            
            const size = Math.random() * 4 + 2;
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            const duration = Math.random() * 3 + 4;
            const delay = Math.random() * 2;
            
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';
            particle.style.left = x + '%';
            particle.style.top = y + '%';
            particle.style.animationDuration = duration + 's';
            particle.style.animationDelay = delay + 's';
            
            particlesContainer.appendChild(particle);
        }
    }
    
    function toggleMenu() {
        menuOpen = !menuOpen;
        
        if (menuOpen) {
            menu.classList.add('expanded');
            toggleBtn.textContent = 'X';
            toggleBtn.classList.add('active');
        } else {
            menu.classList.remove('expanded');
            toggleBtn.textContent = 'Menu';
            toggleBtn.classList.remove('active');
        }
    }
    
    // Content for different pages (JavaScript-style navigation)
    function getPageContent(page) {
        const pageDescriptions = {
            overview: 'The Overview page provides a comprehensive dashboard view of your AskZaira system. Here you can monitor system performance, view recent activity, and access quick statistics about your AI assistant usage. This is the main dashboard with all system metrics and controls.',
            profile: 'The Profile page allows you to manage your personal settings, preferences, and account information. You can update your display name, notification preferences, and customize your AskZaira experience to match your workflow.',
            zaira: 'The Zaira page provides advanced user management and system administration tools. Here you can view user activity, manage scheduled requests, review chat histories, and monitor system performance metrics for the AI assistant.',
            account: 'The Account page contains your subscription details, billing information, and account security settings. Manage your plan, update payment methods, configure security preferences, and view usage statistics.',
            connectors: 'The Connectors page allows you to manage and configure external service integrations, API connections, and third-party system links for enhanced AskZaira functionality.',
            chat: 'Direct Chat provides a real-time interface to communicate directly with AskZaira. This debug-enabled chat allows for advanced queries, system diagnostics, and direct AI interaction with enhanced capabilities.',
            system: 'System Information displays detailed technical metrics about your AskZaira deployment, including database status, API performance, system resource utilization, and infrastructure health monitoring.',
            subscription: 'Subscription management allows you to view and modify your AskZaira service plan, track usage limits, manage billing preferences, and access premium features based on your subscription tier.'
        };
        
        const pageIcons = {
            overview: '[Chart]',
            profile: '[User]', 
            zaira: '[AI]',
            account: '[Gear]',
            connectors: '[Link]',
            chat: '[Chat]',
            system: '[PC]',
            subscription: '[Card]'
        };
        
        return {
            title: page.charAt(0).toUpperCase() + page.slice(1),
            icon: pageIcons[page] || '[Page]',
            description: pageDescriptions[page] || `The ${page} page is currently being developed and will be available soon.`
        };
    }
    
    async function showPageContent(page) {
        // Special handling for zaira page - load actual content
        if (page === 'zaira') {
            try {
                const response = await fetch(`/dashboard/api/page-content?page=zaira&user_guid=system-user`);
                const data = await response.json();
                
                if (data.html) {
                    // Create a full-page overlay for Zaira content
                    const overlay = document.createElement('div');
                    overlay.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(15, 23, 42, 0.95);
                        backdrop-filter: blur(10px);
                        z-index: 10001;
                        overflow-y: auto;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    `;
                    
                    overlay.innerHTML = `
                        <div style="
                            min-height: 100%;
                            padding: 2rem;
                            position: relative;
                        ">
                            <button onclick="this.closest('div[style*=\\"z-index: 10001\\"]').remove()" style="
                                position: absolute;
                                top: 1rem;
                                right: 1rem;
                                background: rgba(239, 68, 68, 0.9);
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                font-weight: 500;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                z-index: 10;
                            " onmouseover="this.style.background='rgba(239, 68, 68, 1)'" onmouseout="this.style.background='rgba(239, 68, 68, 0.9)'">
                                Close
                            </button>
                            ${data.html}
                        </div>
                    `;
                    
                    document.body.appendChild(overlay);
                    
                    // Animate in
                    setTimeout(() => {
                        overlay.style.opacity = '1';
                    }, 10);
                    
                    // Close on ESC key
                    const escHandler = function(e) {
                        if (e.key === 'Escape') {
                            overlay.remove();
                            document.removeEventListener('keydown', escHandler);
                        }
                    };
                    document.addEventListener('keydown', escHandler);
                    
                    return;
                }
            } catch (error) {
                console.error('Failed to load Zaira page:', error);
            }
        }
        
        // Default behavior for other pages - show description overlay
        const content = getPageContent(page);
        
        // Create a temporary overlay to show page content
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 10001;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        overlay.innerHTML = `
            <div style="
                background: var(--menu-background);
                backdrop-filter: blur(20px);
                border: 1px solid var(--menu-border);
                border-radius: 16px;
                padding: 2rem;
                max-width: 600px;
                margin: 2rem;
                text-align: center;
                transform: translateY(20px);
                transition: transform 0.3s ease;
            ">
                <div style="font-size: 4rem; margin-bottom: 1rem;">${content.icon}</div>
                <h2 style="color: #ffffff; font-size: 2rem; margin-bottom: 1rem; background: linear-gradient(135deg, #60a5fa, #a78bfa); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">${content.title}</h2>
                <p style="color: #94a3b8; line-height: 1.6; margin-bottom: 2rem;">${content.description}</p>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: linear-gradient(135deg, #3b82f6, #6366f1);
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                    Close
                </button>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // Animate in
        setTimeout(() => {
            overlay.style.opacity = '1';
            overlay.querySelector('div > div').style.transform = 'translateY(0)';
        }, 10);
        
        // Close on outside click
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }
    
    // Handle menu item clicks with JavaScript navigation
    function handleMenuItemClick(e) {
        const clickedItem = e.currentTarget;
        const page = clickedItem.getAttribute('data-page');
        const url = clickedItem.getAttribute('data-url');
        const password = clickedItem.getAttribute('data-password');
        
        // Handle password protection
        if (password) {
            const userPassword = prompt('Enter password for Direct Chat:');
            if (userPassword !== password) {
                showNotification('Incorrect password', 'error');
                return;
            }
        }
        
        // Handle external URLs (like connectors)
        if (url) {
            window.location.href = url;
            return;
        }
        
        // Update active state with smooth transition
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.classList.remove('active');
        });
        
        clickedItem.classList.add('active');
        currentPage = page;
        
        // Show page content
        showPageContent(page);
        
        // Auto-close menu after selection (optional)
        setTimeout(() => {
            if (menuOpen) {
                toggleMenu();
            }
        }, 500);
    }
    
    // Initialize menu
    function initializeMenu() {
        if (toggleBtn) {
            toggleBtn.addEventListener('click', toggleMenu);
        }
        
        // Add click handlers to menu items
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', handleMenuItemClick);
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (menuOpen && menu && !menu.contains(e.target) && e.target !== toggleBtn) {
                toggleMenu();
            }
        });
        
        // Initialize particles
        if (particlesContainer) {
            initializeMenuParticles();
        }
        
        // Initialize system health monitoring
        initializeSystemHealth();
    }
    
    // System Health Monitoring for Header
    function initializeSystemHealth() {
        updateSystemHealthStatus();
        // Update every 30 seconds
        setInterval(updateSystemHealthStatus, 30000);
    }
    
    async function updateSystemHealthStatus() {
        const statusElement = document.getElementById('systemHealthStatus');
        if (!statusElement) return;
        
        try {
            const response = await fetch('/dashboard/api/system-health');
            const health = await response.json();
            
            if (health.error) {
                statusElement.textContent = 'System Status: Error';
                statusElement.style.color = '#ef4444';
                return;
            }
            
            // Format system health status for header display
            const dbStatus = health.database === 'healthy' ? 'DB-OK' : 'DB-ERR';
            const vectorStatus = health.vector_db === 'healthy' ? 'VDB-OK' : 'VDB-ERR';
            const activeTasks = health.active_tasks || 0;
            const systemLoad = health.system_load || 'Normal';
            const uptime = health.uptime || '0h 0m';
            
            // Create compact status display
            const statusText = `${dbStatus} | ${vectorStatus} | ${activeTasks} tasks | ${systemLoad} | Up: ${uptime}`;
            statusElement.textContent = statusText;
            
            // Color code based on overall system health
            if (health.database === 'healthy' && health.vector_db === 'healthy' && systemLoad === 'Normal') {
                statusElement.style.color = '#10b981'; // Green for healthy
            } else if (health.database === 'Error' || health.vector_db === 'Error') {
                statusElement.style.color = '#ef4444'; // Red for errors
            } else {
                statusElement.style.color = '#f59e0b'; // Yellow for warnings
            }
            
        } catch (error) {
            statusElement.textContent = 'System Status: Connection Error';
            statusElement.style.color = '#ef4444';
            console.error('Failed to update system health status:', error);
        }
    }
    
    // Notification system (if not already available)
    function showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            background: ${type === 'error' ? '#dc2626' : '#3b82f6'};
            color: white;
            border-radius: 8px;
            z-index: 10002;
            font-weight: 500;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // Initialize everything when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeMenu);
    } else {
        initializeMenu();
    }
    
    // Expose functions globally if needed
    window.dashboardMenu = {
        toggleMenu,
        showPageContent,
        showNotification
    };
})();