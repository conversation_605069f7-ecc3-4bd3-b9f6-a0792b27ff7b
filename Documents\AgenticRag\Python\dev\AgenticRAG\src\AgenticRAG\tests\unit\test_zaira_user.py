from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from userprofiles.ZairaUser import ZairaUser
from userprofiles.permission_levels import PERMISSION_LEVELS
from userprofiles.ZairaMessage import ZairaMessage
from endpoints.mybot_generic import MyBot_Generic
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4, UUID
from pydantic import ValidationError
import asyncio

class TestZairaUser:
    """Test class for ZairaUser"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_username = "test_user"
        self.test_rank = PERMISSION_LEVELS.USER
        self.test_guid = uuid4()
        self.test_device_guid = uuid4()
        self.mock_bot = MagicMock(spec=MyBot_Generic)
        self.mock_bot.parent_instance = None
        self.mock_bot.send_reply = AsyncMock()
    
    def test_zaira_user_creation(self):
        """Test successful ZairaUser creation"""
        with patch('userprofiles.ZairaUser.LogFire.log') as mock_log:
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            assert user.username == self.test_username
            assert user.rank == self.test_rank
            assert user.GUID == self.test_guid
            assert user.DeviceGUID == self.test_device_guid
            assert user.sessionGUID is not None
            assert user.conversationGUID is not None
            assert isinstance(user.sessionGUID, UUID)
            assert isinstance(user.conversationGUID, UUID)
            assert user.sessionGUID in user.chat_history
            assert user.chat_history[user.sessionGUID] == []
            assert user.my_task is None
            assert user.platform == ""
            assert user.is_system_user is False
            
            # Verify logging was called
            mock_log.assert_called_once()
    
    def test_user_guid_property(self):
        """Test user_guid property returns string"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            assert user.user_guid == str(self.test_guid)
            assert isinstance(user.user_guid, str)
    
    def test_get_available_vector_stores_none_permission(self):
        """Test getAvailableVectorStores with NONE permission"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=PERMISSION_LEVELS.NONE,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            assert user.getAvailableVectorStores() is None
    
    def test_get_available_vector_stores_guest_permission(self):
        """Test getAvailableVectorStores with GUEST permission"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=PERMISSION_LEVELS.GUEST,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            assert user.getAvailableVectorStores() is None
    
    def test_get_available_vector_stores_user_permission(self):
        """Test getAvailableVectorStores with USER permission"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=PERMISSION_LEVELS.USER,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            result = user.getAvailableVectorStores()
            assert result == ["user_vectors"]
    
    def test_get_available_vector_stores_admin_permission(self):
        """Test getAvailableVectorStores with ADMIN permission"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=PERMISSION_LEVELS.ADMIN,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            result = user.getAvailableVectorStores()
            assert result == ["user_vectors", "admin_vectors"]
    
    def test_get_chat_history_empty(self):
        """Test get_chat_history when no messages exist"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            history = user.get_chat_history()
            assert history == []
    
    def test_get_chat_history_with_messages(self):
        """Test get_chat_history with existing messages"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Add mock message
            mock_message = MagicMock(spec=ZairaMessage)
            mock_message.to_langchain.return_value = MagicMock()
            user.chat_history[user.sessionGUID].append(mock_message)
            
            history = user.get_chat_history(typing="LangChain")
            assert len(history) == 1
            mock_message.to_langchain.assert_called_once()
    
    def test_get_chat_history_non_langchain_typing(self):
        """Test get_chat_history with non-langchain typing"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Add mock message
            mock_message = MagicMock(spec=ZairaMessage)
            user.chat_history[user.sessionGUID].append(mock_message)
            
            history = user.get_chat_history(typing="Other")
            assert history == []
    
    @pytest.mark.asyncio
    async def test_on_message_with_attachments(self):
        """Test on_message with attachments"""
        with patch('userprofiles.ZairaUser.LogFire.log'), \
             patch('unstructured.partition.auto.partition') as mock_partition, \
             patch('userprofiles.ZairaUser.ZairaMessage.create_user_message') as mock_create_message:
            
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            mock_partition.return_value = "Processed attachment content"
            mock_message = MagicMock(spec=ZairaMessage)
            mock_create_message.return_value = mock_message
            
            with patch.object(user, 'start_task') as mock_start_task:
                mock_start_task.return_value = asyncio.coroutine(lambda: None)()
                await user.on_message(
                    complete_message="Test message",
                    calling_bot=self.mock_bot,
                    attachments=["attachment1.pdf", "attachment2.txt"]
                )
                
                # Verify partition was called for each attachment
                assert mock_partition.call_count == 2
                mock_start_task.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_on_message_without_attachments(self):
        """Test on_message without attachments"""
        with patch('userprofiles.ZairaUser.LogFire.log'), \
             patch('userprofiles.ZairaUser.ZairaMessage.create_user_message') as mock_create_message:
            
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            mock_message = MagicMock(spec=ZairaMessage)
            mock_create_message.return_value = mock_message
            
            with patch.object(user, 'start_task') as mock_start_task:
                mock_start_task.return_value = asyncio.coroutine(lambda: None)()
                await user.on_message(
                    complete_message="Test message",
                    calling_bot=self.mock_bot,
                    attachments=[]
                )
                
                mock_start_task.assert_called_once()
                # Verify message was added to chat history
                mock_create_message.assert_called_once_with(
                    "Test message", 
                    user.conversationGUID, 
                    user.sessionGUID
                )
    
    @pytest.mark.asyncio
    async def test_on_message_with_existing_task(self):
        """Test on_message when user already has a running task"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Set up existing task
            mock_task = MagicMock()
            mock_task.on_message = AsyncMock()
            mock_task.human_in_the_loop_callback = None
            user.my_task = mock_task
            
            await user.on_message(
                complete_message="Test message",
                calling_bot=self.mock_bot,
                attachments=[]
            )
            
            # Verify existing task's on_message was called
            mock_task.on_message.assert_called_once_with(
                complete_message="Test message",
                calling_bot=self.mock_bot,
                original_message=None
            )
    
    @pytest.mark.asyncio
    async def test_on_message_hitl_response(self):
        """Test on_message with human-in-the-loop response"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Set up task with HITL callback
            mock_task = MagicMock()
            mock_task.on_message = AsyncMock()
            mock_task.human_in_the_loop_callback = MagicMock()  # Has HITL callback
            user.my_task = mock_task
            
            # Store initial chat history length
            initial_length = len(user.chat_history[user.sessionGUID])
            
            await user.on_message(
                complete_message="HITL response",
                calling_bot=self.mock_bot,
                attachments=[]
            )
            
            # Verify message was NOT added to chat history (HITL response)
            assert len(user.chat_history[user.sessionGUID]) == initial_length
            mock_task.on_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_on_message_empty_message(self):
        """Test on_message with empty message"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            initial_length = len(user.chat_history[user.sessionGUID])
            
            with patch.object(user, 'start_task') as mock_start_task:
                mock_start_task.return_value = asyncio.coroutine(lambda: None)()
                await user.on_message(
                    complete_message="",
                    calling_bot=self.mock_bot,
                    attachments=[]
                )
                
                # Verify empty message was not added to chat history
                assert len(user.chat_history[user.sessionGUID]) == initial_length
                mock_start_task.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_task_debug_mode(self):
        """Test start_task in debug mode"""
        with patch('userprofiles.ZairaUser.LogFire.log'), \
             patch('userprofiles.ZairaUser.LongRunningZairaTask') as mock_task_class, \
             patch('userprofiles.ZairaUser.Globals.is_debug', return_value=True):
            
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            mock_task = MagicMock()
            mock_task.run_task = AsyncMock()
            mock_task.await_status_complete = AsyncMock()
            mock_task_class.return_value = mock_task
            
            await user.start_task(
                complete_message="Test message",
                calling_bot=self.mock_bot,
                original_message=None
            )
            
            # Verify task was created and run_task was called directly
            mock_task_class.assert_called_once()
            mock_task.run_task.assert_called_once()
            mock_task.await_status_complete.assert_called_once_with(wait_on_complete=False)
            assert user.my_task == mock_task
    
    @pytest.mark.asyncio
    async def test_start_task_production_mode(self):
        """Test start_task in production mode"""
        with patch('userprofiles.ZairaUser.LogFire.log'), \
             patch('userprofiles.ZairaUser.LongRunningZairaTask') as mock_task_class, \
             patch('userprofiles.ZairaUser.Globals.is_debug', return_value=False), \
             patch('asyncio.create_task') as mock_create_task, \
             patch('userprofiles.ZairaUser.etc.helper_functions.handle_asyncio_task_result_errors') as mock_handler:
            
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            mock_task = MagicMock()
            mock_task.run_task = AsyncMock()
            mock_task.await_status_complete = AsyncMock()
            mock_task_class.return_value = mock_task
            
            mock_asyncio_task = MagicMock()
            mock_asyncio_task.add_done_callback = MagicMock()
            mock_create_task.return_value = mock_asyncio_task
            
            await user.start_task(
                complete_message="Test message",
                calling_bot=self.mock_bot,
                original_message=None
            )
            
            # Verify task was created and asyncio.create_task was called
            mock_task_class.assert_called_once()
            mock_create_task.assert_called_once_with(mock_task.run_task())
            mock_asyncio_task.add_done_callback.assert_called_once()
            mock_task.await_status_complete.assert_called_once_with(wait_on_complete=False)
            assert user.my_task == mock_task
            assert user.asyncio_Task == mock_asyncio_task
    
    @pytest.mark.asyncio
    async def test_start_task_exception_handling(self):
        """Test start_task exception handling"""
        with patch('userprofiles.ZairaUser.LogFire.log'), \
             patch('userprofiles.ZairaUser.LongRunningZairaTask') as mock_task_class, \
             patch('userprofiles.ZairaUser.etc.helper_functions.exception_triggered') as mock_exception:
            
            user = ZairaUser(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Add the missing attribute that the error handler tries to access
            user.original_physical_message = MagicMock()
            
            mock_task = MagicMock()
            mock_task.await_status_complete = AsyncMock(side_effect=Exception("Test error"))
            mock_task_class.return_value = mock_task
            
            await user.start_task(
                complete_message="Test message",
                calling_bot=self.mock_bot,
                original_message=None
            )
            
            # Verify exception was handled
            mock_exception.assert_called_once()
            self.mock_bot.send_reply.assert_called_once()
            assert user.my_task is None

class TestZairaUserEdgeCases:
    """Test edge cases for ZairaUser"""
    
    def test_pydantic_model_validation(self):
        """Test Pydantic model validation"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            # Test valid creation
            user = ZairaUser(
                username="test",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            assert user.username == "test"
    
    @pytest.mark.asyncio
    async def test_model_config_arbitrary_types(self):
        """Test model config allows arbitrary types"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username="test",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            
            # Should allow arbitrary types like asyncio.Task
            user.asyncio_Task = asyncio.create_task(asyncio.sleep(0))
            assert user.asyncio_Task is not None
            user.asyncio_Task.cancel()  # Clean up

class TestZairaUserProperties:
    """Test ZairaUser property behaviors"""
    
    def test_default_values(self):
        """Test default field values"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username="test",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            
            assert user.real_name == ""
            assert user.email == ""
            assert user.platform == ""
            assert user.is_system_user is False
            assert user.my_task is None
            assert user.asyncio_Task is None
            assert isinstance(user.chat_history, dict)
    
    def test_field_exclusions(self):
        """Test that excluded fields work correctly"""
        with patch('userprofiles.ZairaUser.LogFire.log'):
            user = ZairaUser(
                username="test",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            
            # These fields should be excluded from serialization
            assert hasattr(user, 'GUID')
            assert hasattr(user, 'DeviceGUID')
            assert hasattr(user, 'sessionGUID')
            assert hasattr(user, 'conversationGUID')
            assert hasattr(user, 'asyncio_Task')