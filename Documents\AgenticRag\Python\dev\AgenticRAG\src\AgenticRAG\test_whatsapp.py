#!/usr/bin/env python3
"""
Test script to verify WhatsApp credentials are working
"""

import asyncio
import httpx
from config import WHATSAPP_PHONE_NUMBER_ID, WHATSAPP_ACCESS_TOKEN

async def test_whatsapp_credentials():
    """Test if WhatsApp credentials work by sending a test message"""

    # According to Meta docs, test numbers can only send to verified recipients
    # We need to check if the recipient number is verified in Meta Business Manager

    # Target phone number (your personal number) - trying different formats
    phone_formats = [
        "***********",      # Without +
        "+***********",     # With +
        "31 6 11 23 94 87"  # With spaces (this will likely fail)
    ]

    # Test message
    message = "Test message from WhatsApp bot - checking if recipient is verified"

    # Test each phone format
    for to_number in phone_formats:
        print(f"\n--- Testing phone format: {to_number} ---")

        try:
            # Prepare the API endpoint
            url = f"https://graph.facebook.com/v18.0/{WHATSAPP_PHONE_NUMBER_ID}/messages"

            # Prepare the message payload
            payload = {
                "messaging_product": "whatsapp",
                "to": to_number,
                "type": "text",
                "text": {
                    "body": message
                }
            }

            # Set up headers with the access token
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {WHATSAPP_ACCESS_TOKEN}"
            }

            print(f"Testing WhatsApp API with:")
            print(f"Phone Number ID: {WHATSAPP_PHONE_NUMBER_ID}")
            print(f"Access Token: {WHATSAPP_ACCESS_TOKEN[:20]}...")
            print(f"Target Number: {to_number}")
            print(f"URL: {url}")

            # Send the message
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload, headers=headers)

                print(f"Response Status: {response.status_code}")
                print(f"Response Body: {response.text}")

                if response.status_code == 200:
                    print(f"✅ WhatsApp message sent successfully to {to_number}!")
                    return True
                else:
                    print(f"❌ Failed to send WhatsApp message to {to_number}. Status: {response.status_code}")

        except Exception as e:
            print(f"❌ Error testing WhatsApp with {to_number}: {e}")

    return False

async def test_whatsapp_bot_integration():
    """Test WhatsApp bot integration without sending actual messages"""
    try:
        from endpoints.whatsapp_endpoint import MyWhatsappBot

        print("Testing WhatsApp bot initialization...")

        # Test bot creation
        bot = MyWhatsappBot()
        print("✅ WhatsApp bot instance created successfully")

        # Test singleton pattern
        bot2 = MyWhatsappBot()
        assert bot is bot2, "Bot should be singleton"
        print("✅ Singleton pattern working correctly")

        # Test configuration loading
        print(f"Phone Number ID: {WHATSAPP_PHONE_NUMBER_ID}")
        print(f"Access Token configured: {'Yes' if WHATSAPP_ACCESS_TOKEN else 'No'}")

        return True

    except Exception as e:
        print(f"❌ Error testing WhatsApp bot integration: {e}")
        return False

if __name__ == "__main__":
    async def main():
        print("=== WhatsApp Bot Testing Suite ===\n")

        # Test 1: Bot integration (safe)
        print("1. Testing bot integration...")
        integration_result = await test_whatsapp_bot_integration()

        # Test 2: Credentials (sends actual message)
        print("\n2. Testing WhatsApp credentials (will send actual message)...")
        user_input = input("Do you want to test sending actual messages? (y/N): ")

        if user_input.lower() in ['y', 'yes']:
            credentials_result = await test_whatsapp_credentials()
        else:
            print("Skipping credential test (actual message sending)")
            credentials_result = True

        # Summary
        print("\n=== Test Results ===")
        print(f"Bot Integration: {'✅ PASS' if integration_result else '❌ FAIL'}")
        print(f"Credentials: {'✅ PASS' if credentials_result else '❌ FAIL'}")

        if integration_result and credentials_result:
            print("\n🎉 All tests passed! WhatsApp bot is ready for use.")
        else:
            print("\n⚠️  Some tests failed. Check configuration and setup.")

    asyncio.run(main())