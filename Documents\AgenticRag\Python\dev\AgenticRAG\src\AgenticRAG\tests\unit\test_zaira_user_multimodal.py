# tests/unit/test_zaira_user_multimodal.py
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from userprofiles.ZairaUser import ZairaUser
from userprofiles.permission_levels import PERMISSION_LEVELS
from endpoints.mybot_generic import MyBot_Generic
from managers.manager_multimodal import MultimodalManager
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from pathlib import Path
import tempfile
import os
import asyncio

class TestZairaUserMultimodal:
    """Test multimodal functionality in ZairaUser class."""
    
    @pytest.fixture
    def zaira_user(self):
        """Create a ZairaUser instance for testing."""
        return <PERSON>airaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
    
    @pytest.fixture
    def mock_bot(self):
        """Create a mock bot for testing."""
        mock_bot = MagicMock(spec=MyBot_Generic)
        mock_bot.send_reply = AsyncMock()
        return mock_bot
    
    @pytest.fixture
    def temp_image_file(self):
        """Create a temporary image file for testing."""
        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as f:
            # Create a minimal JPEG file content
            f.write(b'\xFF\xD8\xFF\xE0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xFF\xDB\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xFF\xC0\x00\x11\x08\x00\x08\x00\x08\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xFF\xC4\x00\x1F\x00\x00\x01\x05\x01\x01\x01\x01\x01\x01\x00\x00\x00\x00\x00\x00\x00\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\xFF\xC4\x00\xB5\x10\x00\x02\x01\x03\x03\x02\x04\x03\x05\x05\x04\x04\x00\x00\x01}\x01\x02\x03\x00\x04\x11\x05\x12!1A\x06\x13Qa\x07"q\x142\x81\x91\xa1\x08#B\xb1\xc1\x15R\xd1\xf0$3br\x82\t\n\x16\x17\x18\x19\x1a%&\'()*456789:CDEFGHIJSTUVWXYZcdefghijstuvwxyz\x83\x84\x85\x86\x87\x88\x89\x8a\x92\x93\x94\x95\x96\x97\x98\x99\x9a\xa2\xa3\xa4\xa5\xa6\xa7\xa8\xa9\xaa\xb2\xb3\xb4\xb5\xb6\xb7\xb8\xb9\xba\xc2\xc3\xc4\xc5\xc6\xc7\xc8\xc9\xca\xd2\xd3\xd4\xd5\xd6\xd7\xd8\xd9\xda\xe1\xe2\xe3\xe4\xe5\xe6\xe7\xe8\xe9\xea\xf1\xf2\xf3\xf4\xf5\xf6\xf7\xf8\xf9\xfa\xFF\xDA\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xf7\xfa(\xa2\x80\x0f\xff\xd9')
            temp_path = f.name
        yield temp_path
        # Cleanup
        try:
            os.unlink(temp_path)
        except:
            pass
    
    @pytest.fixture
    def temp_text_file(self):
        """Create a temporary text file for testing."""
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as f:
            f.write(b"Test document content")
            temp_path = f.name
        yield temp_path
        # Cleanup
        try:
            os.unlink(temp_path)
        except:
            pass
    
    def test_is_image_file_valid_extensions(self, zaira_user):
        """Test image file detection with valid extensions."""
        # Test various image extensions
        image_paths = [
            "/path/to/image.jpg",
            "/path/to/image.jpeg",
            "/path/to/image.png",
            "/path/to/image.gif",
            "/path/to/image.bmp",
            "/path/to/image.tiff",
            "/path/to/image.webp",
            "/path/to/IMAGE.JPG",  # Test case insensitive
        ]
        
        for path in image_paths:
            result = asyncio.run(zaira_user._is_image_file(path))
            assert result is True, f"Should detect {path} as image"
    
    def test_is_image_file_invalid_extensions(self, zaira_user):
        """Test image file detection with invalid extensions."""
        non_image_paths = [
            "/path/to/document.pdf",
            "/path/to/document.txt",
            "/path/to/document.doc",
            "/path/to/document.docx",
            "/path/to/data.csv",
            "/path/to/file",  # No extension
        ]
        
        for path in non_image_paths:
            result = asyncio.run(zaira_user._is_image_file(path))
            assert result is False, f"Should not detect {path} as image"
    
    def test_is_image_file_invalid_path(self, zaira_user):
        """Test image file detection with invalid path."""
        result = asyncio.run(zaira_user._is_image_file(""))
        assert result is False
    
    @pytest.mark.asyncio
    async def test_get_supported_image_formats(self, zaira_user):
        """Test getting supported image formats."""
        formats = await zaira_user.get_supported_image_formats()
        expected_formats = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".webp"]
        assert formats == expected_formats
    
    @pytest.mark.asyncio
    async def test_is_multimodal_enabled_user_rank(self, zaira_user):
        """Test multimodal capability check with different user ranks."""
        # Test with USER rank (should be enabled)
        zaira_user.rank = PERMISSION_LEVELS.USER
        with patch.object(MultimodalManager, 'setup', new_callable=AsyncMock):
            result = await zaira_user.is_multimodal_enabled()
            assert result is True
        
        # Test with ADMIN rank (should be enabled)
        zaira_user.rank = PERMISSION_LEVELS.ADMIN
        with patch.object(MultimodalManager, 'setup', new_callable=AsyncMock):
            result = await zaira_user.is_multimodal_enabled()
            assert result is True
        
        # Test with GUEST rank (should be disabled)
        zaira_user.rank = PERMISSION_LEVELS.GUEST
        result = await zaira_user.is_multimodal_enabled()
        assert result is False
        
        # Test with NONE rank (should be disabled)
        zaira_user.rank = PERMISSION_LEVELS.NONE
        result = await zaira_user.is_multimodal_enabled()
        assert result is False
    
    @pytest.mark.asyncio
    async def test_is_multimodal_enabled_exception_handling(self, zaira_user):
        """Test multimodal capability check with exception handling."""
        zaira_user.rank = PERMISSION_LEVELS.USER
        
        # Mock MultimodalManager.setup to raise an exception
        with patch.object(MultimodalManager, 'setup', side_effect=Exception("Setup failed")):
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                result = await zaira_user.is_multimodal_enabled()
                assert result is False
                mock_exception.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_image_attachment_success(self, zaira_user, temp_image_file):
        """Test successful image attachment processing."""
        expected_summary = "This is a test image showing a sample scene."
        
        with patch.object(MultimodalManager, 'generate_image_summary', return_value=expected_summary) as mock_generate:
            result = await zaira_user._process_image_attachment(temp_image_file)
            
            # Verify the result format
            assert "**Image:" in result
            assert Path(temp_image_file).name in result
            assert expected_summary in result
            
            # Verify the multimodal manager was called with correct parameters
            mock_generate.assert_called_once()
            call_args = mock_generate.call_args
            assert call_args[1]['image_path'] == temp_image_file
            assert "Discord chat" in call_args[1]['context']
    
    @pytest.mark.asyncio
    async def test_process_image_attachment_failure(self, zaira_user, temp_image_file):
        """Test image attachment processing with failure."""
        with patch.object(MultimodalManager, 'generate_image_summary', side_effect=Exception("API error")):
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                result = await zaira_user._process_image_attachment(temp_image_file)
                
                # Verify error handling
                assert "**Image:" in result
                assert "Could not analyze image content" in result
                mock_exception.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_attachments_multimodal_images_only(self, zaira_user, temp_image_file):
        """Test processing attachments with only images."""
        attachments = [temp_image_file]
        expected_summary = "This is a test image."
        
        with patch.object(zaira_user, '_process_image_attachment', return_value=f"**Image: test.jpg**\\n{expected_summary}") as mock_process:
            result = await zaira_user._process_attachments_multimodal(attachments)
            
            assert expected_summary in result
            mock_process.assert_called_once_with(temp_image_file)
    
    @pytest.mark.asyncio
    async def test_process_attachments_multimodal_mixed_files(self, zaira_user, temp_image_file, temp_text_file):
        """Test processing attachments with mixed file types."""
        attachments = [temp_image_file, temp_text_file]
        
        with patch.object(zaira_user, '_process_image_attachment', return_value="**Image: test.jpg**\\nImage analysis") as mock_image:
            with patch('unstructured.partition.auto.partition', return_value="Document content") as mock_partition:
                result = await zaira_user._process_attachments_multimodal(attachments)
                
                # Verify both types were processed
                assert "**Image: test.jpg**" in result
                assert "**Document:" in result
                assert "Document content" in result
                mock_image.assert_called_once()
                mock_partition.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_attachments_multimodal_document_error(self, zaira_user, temp_text_file):
        """Test processing attachments with document processing error."""
        attachments = [temp_text_file]
        
        with patch('unstructured.partition.auto.partition', side_effect=Exception("Processing error")):
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                result = await zaira_user._process_attachments_multimodal(attachments)
                
                assert "Could not process content" in result
                mock_exception.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_attachments_multimodal_empty_list(self, zaira_user):
        """Test processing empty attachments list."""
        result = await zaira_user._process_attachments_multimodal([])
        assert result == ""
    
    @pytest.mark.asyncio
    async def test_process_attachments_multimodal_exception_handling(self, zaira_user, temp_image_file):
        """Test exception handling in attachment processing."""
        attachments = [temp_image_file]
        
        with patch.object(zaira_user, '_is_image_file', side_effect=Exception("Unexpected error")):
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                result = await zaira_user._process_attachments_multimodal(attachments)
                
                assert result == "Error processing attachments"
                mock_exception.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_on_message_with_image_attachments(self, zaira_user, mock_bot, temp_image_file):
        """Test on_message method with image attachments."""
        message = "What's in this image?"
        attachments = [temp_image_file]
        
        with patch.object(zaira_user, '_process_attachments_multimodal', return_value="**Image: test.jpg**\\nImage shows a cat") as mock_process:
            with patch.object(zaira_user, 'start_task', new_callable=AsyncMock) as mock_start:
                await zaira_user.on_message(message, mock_bot, attachments)
                
                # Verify attachment processing was called
                mock_process.assert_called_once_with(attachments)
                
                # Verify the message was enhanced with attachment analysis
                mock_start.assert_called_once()
                call_args = mock_start.call_args
                enhanced_message = call_args[1]['complete_message']
                assert "What's in this image?" in enhanced_message
                assert "Attachment Analysis:" in enhanced_message
                assert "Image shows a cat" in enhanced_message
    
    @pytest.mark.asyncio
    async def test_on_message_with_no_attachments(self, zaira_user, mock_bot):
        """Test on_message method with no attachments."""
        message = "Hello, how are you?"
        
        with patch.object(zaira_user, 'start_task', new_callable=AsyncMock) as mock_start:
            await zaira_user.on_message(message, mock_bot, [])
            
            # Verify the message was not modified
            mock_start.assert_called_once()
            call_args = mock_start.call_args
            enhanced_message = call_args[1]['complete_message']
            assert enhanced_message == message
    
    @pytest.mark.asyncio
    async def test_on_message_with_empty_attachment_analysis(self, zaira_user, mock_bot, temp_image_file):
        """Test on_message method when attachment analysis returns empty."""
        message = "Test message"
        attachments = [temp_image_file]
        
        with patch.object(zaira_user, '_process_attachments_multimodal', return_value="") as mock_process:
            with patch.object(zaira_user, 'start_task', new_callable=AsyncMock) as mock_start:
                await zaira_user.on_message(message, mock_bot, attachments)
                
                # Verify the message was not modified when no analysis is available
                mock_start.assert_called_once()
                call_args = mock_start.call_args
                enhanced_message = call_args[1]['complete_message']
                assert enhanced_message == message
    
    @pytest.mark.asyncio
    async def test_on_message_with_existing_task(self, zaira_user, mock_bot, temp_image_file):
        """Test on_message method when user already has an active task."""
        message = "Follow up message"
        attachments = [temp_image_file]
        
        # Set up existing task
        mock_task = MagicMock()
        mock_task.on_message = AsyncMock()
        zaira_user.my_task = mock_task
        
        with patch.object(zaira_user, '_process_attachments_multimodal', return_value="**Image: test.jpg**\\nImage analysis") as mock_process:
            await zaira_user.on_message(message, mock_bot, attachments)
            
            # Verify attachment processing was called
            mock_process.assert_called_once_with(attachments)
            
            # Verify existing task received the enhanced message
            mock_task.on_message.assert_called_once()
            call_args = mock_task.on_message.call_args
            enhanced_message = call_args[1]['complete_message']
            assert "Follow up message" in enhanced_message
            assert "Attachment Analysis:" in enhanced_message
            assert "Image analysis" in enhanced_message