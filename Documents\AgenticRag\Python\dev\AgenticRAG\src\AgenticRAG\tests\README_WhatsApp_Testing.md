# WhatsApp Bot Testing Guide

This document provides comprehensive guidance for testing the WhatsApp bot implementation, including setup procedures, test execution, and validation criteria.

## Table of Contents

1. [Testing Overview](#testing-overview)
2. [Test Environment Setup](#test-environment-setup)
3. [Test Categories](#test-categories)
4. [Running Tests](#running-tests)
5. [Mock Services](#mock-services)
6. [Validation Criteria](#validation-criteria)
7. [Troubleshooting](#troubleshooting)
8. [Performance Benchmarks](#performance-benchmarks)

## Testing Overview

The WhatsApp bot testing suite includes:

- **Unit Tests**: Individual component testing with mocks
- **Integration Tests**: Component interaction testing
- **End-to-End Tests**: Complete workflow testing
- **Performance Tests**: Load and scalability testing
- **Security Tests**: Vulnerability and attack prevention testing

### Test Structure

```
tests/
├── conftest.py                    # Test fixtures and configuration
├── unit/
│   └── test_whatsapp_components.py    # Unit tests
├── integration/
│   ├── test_whatsapp_integration.py   # Integration tests
│   └── test_whatsapp_e2e.py          # End-to-end tests
├── performance/
│   └── test_whatsapp_performance.py  # Performance tests
├── security/
│   └── test_whatsapp_security.py     # Security tests
├── mock_whatsapp_server.py        # Mock WhatsApp API server
└── README_WhatsApp_Testing.md     # This documentation
```

## Test Environment Setup

### Prerequisites

1. **Python Dependencies**:
   ```bash
   pip install pytest pytest-asyncio httpx aiohttp psutil
   ```

2. **Environment Variables**:
   ```bash
   export WHATSAPP_PHONE_NUMBER_ID="your_phone_number_id"
   export WHATSAPP_ACCESS_TOKEN="your_access_token"
   export WHATSAPP_VERIFY_TOKEN="your_verify_token"
   ```

3. **Configuration**:
   - Ensure `config.py` has valid WhatsApp credentials
   - Set up test database (if using real database)
   - Configure RAG components for testing

### Local Development Setup

1. **Start Mock Services**:
   ```bash
   # Start mock WhatsApp API server
   python tests/mock_whatsapp_server.py
   ```

2. **Verify Configuration**:
   ```bash
   # Run basic configuration test
   python test_whatsapp.py
   ```

## Test Categories

### Unit Tests (`test_whatsapp_components.py`)

Tests individual components in isolation:

- **Bot Singleton Pattern**: Ensures single instance
- **Message Processing**: Text extraction and validation
- **API Payload Construction**: Request formatting
- **Webhook Verification**: Token validation
- **Error Handling**: Exception management

**Run Unit Tests**:
```bash
pytest tests/unit/test_whatsapp_components.py -v
```

### Integration Tests (`test_whatsapp_integration.py`)

Tests component interactions:

- **Bot Initialization**: Complete setup process
- **User Management Integration**: User creation and lookup
- **RAG System Integration**: Query processing
- **OAuth Configuration**: Authentication flow
- **Database Integration**: Data persistence

**Run Integration Tests**:
```bash
pytest tests/integration/test_whatsapp_integration.py -v
```

### End-to-End Tests (`test_whatsapp_e2e.py`)

Tests complete workflows:

- **Message Flow**: Webhook to response
- **RAG Query Workflow**: Complete question-answer cycle
- **Error Recovery**: Graceful failure handling
- **Concurrent Processing**: Multiple simultaneous users

**Run E2E Tests**:
```bash
pytest tests/integration/test_whatsapp_e2e.py -v
```

### Performance Tests (`test_whatsapp_performance.py`)

Tests system performance:

- **Message Latency**: Single message processing time
- **Throughput**: Messages per second capacity
- **Memory Usage**: Resource consumption under load
- **Scalability**: Multi-user handling

**Run Performance Tests**:
```bash
pytest tests/performance/test_whatsapp_performance.py -v
```

### Security Tests (`test_whatsapp_security.py`)

Tests security measures:

- **Input Validation**: Malformed data handling
- **Injection Prevention**: SQL/XSS/Code injection protection
- **Authentication**: Token validation
- **Rate Limiting**: Abuse prevention

**Run Security Tests**:
```bash
pytest tests/security/test_whatsapp_security.py -v
```

## Running Tests

### All Tests
```bash
# Run all WhatsApp tests
pytest tests/ -k "whatsapp" -v

# Run with coverage
pytest tests/ -k "whatsapp" --cov=endpoints.whatsapp_endpoint --cov-report=html
```

### Specific Test Categories
```bash
# Unit tests only
pytest tests/unit/ -v

# Integration tests only
pytest tests/integration/ -v

# Performance tests only
pytest tests/performance/ -v -m performance

# Security tests only
pytest tests/security/ -v -m security

# End-to-end tests only
pytest tests/integration/test_whatsapp_e2e.py -v -m e2e
```

### Test Markers
```bash
# Run tests by marker
pytest -m unit          # Unit tests
pytest -m integration   # Integration tests
pytest -m e2e           # End-to-end tests
pytest -m performance   # Performance tests
pytest -m security      # Security tests
```

## Mock Services

### Mock WhatsApp API Server

The mock server (`mock_whatsapp_server.py`) provides:

- **Message Sending Simulation**: Mock API endpoints
- **Webhook Simulation**: Incoming message generation
- **Rate Limiting Simulation**: API quota testing
- **Error Condition Simulation**: Failure scenarios

**Start Mock Server**:
```bash
python tests/mock_whatsapp_server.py
```

**Mock Server Endpoints**:
- `POST /v18.0/{phone_id}/messages` - Send message
- `GET /test/messages` - View sent messages
- `POST /test/simulate_incoming` - Simulate incoming message
- `GET /health` - Health check

### Using Mock Server in Tests

```python
@pytest.fixture
async def mock_whatsapp_server():
    from tests.mock_whatsapp_server import MockWhatsAppServer
    server = MockWhatsAppServer(port=8081)
    runner = await server.start()
    yield server
    await server.stop(runner)
```

## Validation Criteria

### Functional Validation

- ✅ **Message Processing**: All message types handled correctly
- ✅ **User Management**: Users created and managed properly
- ✅ **RAG Integration**: Queries processed and responses generated
- ✅ **Error Handling**: Graceful failure recovery
- ✅ **Webhook Verification**: Proper token validation

### Performance Validation

- ✅ **Latency**: Message processing < 100ms
- ✅ **Throughput**: > 25 messages/second
- ✅ **Memory Usage**: < 100MB increase under load
- ✅ **CPU Usage**: < 80% under sustained load
- ✅ **Concurrent Users**: Handle 100+ simultaneous users

### Security Validation

- ✅ **Input Sanitization**: No injection vulnerabilities
- ✅ **Authentication**: Proper token validation
- ✅ **Data Isolation**: User data properly separated
- ✅ **Error Disclosure**: No sensitive information leaked
- ✅ **Rate Limiting**: Protection against abuse

## Troubleshooting

### Common Issues

1. **Import Errors**:
   ```bash
   # Ensure PYTHONPATH includes src directory
   export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
   ```

2. **Mock Server Connection**:
   ```bash
   # Check if mock server is running
   curl http://localhost:8080/health
   ```

3. **Database Connection**:
   ```bash
   # Verify database is accessible
   python -c "from imports import *; print('Imports successful')"
   ```

4. **WhatsApp API Credentials**:
   ```bash
   # Test credentials
   python test_whatsapp.py
   ```

### Debug Mode

Run tests with debug output:
```bash
pytest tests/ -v -s --log-cli-level=DEBUG
```

### Test Data Cleanup

Reset test state:
```bash
# Clear mock server state
curl -X DELETE http://localhost:8080/test/reset

# Reset database (if using test database)
python -c "from tests.conftest import reset_test_database; reset_test_database()"
```

## Performance Benchmarks

### Expected Performance Metrics

| Metric | Target | Measurement |
|--------|--------|-------------|
| Message Latency | < 100ms | Single message processing |
| Throughput | > 25 msg/s | Concurrent message handling |
| Memory Usage | < 100MB | Increase under sustained load |
| CPU Usage | < 80% | Peak usage under load |
| Concurrent Users | 100+ | Simultaneous active users |
| API Call Latency | < 500ms | WhatsApp API response time |

### Performance Test Commands

```bash
# Run performance benchmarks
pytest tests/performance/ -v --benchmark-only

# Generate performance report
pytest tests/performance/ --benchmark-json=benchmark.json
```

### Monitoring During Tests

```bash
# Monitor system resources
htop

# Monitor network connections
netstat -an | grep :8080

# Monitor memory usage
watch -n 1 'ps aux | grep python'
```

## Continuous Integration

### GitHub Actions Example

```yaml
name: WhatsApp Bot Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run tests
        run: pytest tests/ -k "whatsapp" --cov=endpoints.whatsapp_endpoint
```

### Test Reports

Generate comprehensive test reports:
```bash
# HTML coverage report
pytest tests/ --cov=endpoints.whatsapp_endpoint --cov-report=html

# JUnit XML report
pytest tests/ --junitxml=test-results.xml

# Combined report
pytest tests/ --cov=endpoints.whatsapp_endpoint --cov-report=html --junitxml=test-results.xml
```
