# tests/unit/test_scheduled_task_manager.py
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
from tasks.inputs.task_scheduled_task_manager import (
    SupervisorTask_ScheduledTaskManager,
    CreateScheduledTaskTool,
    ListScheduledTasksTool,
    CancelScheduledTaskTool,
    ScheduledTaskCreateTask,
    ScheduledTaskListTask,
    ScheduledTaskCancelTask,
    ScheduledTaskInfoTask
)
from managers.manager_supervisors import SupervisorTaskState
from userprofiles.ZairaUser import Zaira<PERSON>ser
from endpoints.mybot_generic import MyBot_Generic
from langchain_core.messages import HumanMessage
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4


class TestScheduledTaskManager:
    """Test suite for SupervisorTask_ScheduledTaskManager"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.user_guid = str(uuid4())
        self.user = MagicMock(spec=<PERSON>airaUser)
        self.user.GUID = self.user_guid
        self.user.username = "test_user"
        self.user.get_chat_history.return_value = []
    
    def test_manager_initialization(self):
        """Test SupervisorTask_ScheduledTaskManager initialization"""
        manager = SupervisorTask_ScheduledTaskManager()
        assert manager.name == "scheduled_task_manager"
        assert len(manager._tasks) == 4  # Should have 4 tasks
        
        # Verify task types
        task_names = [task.name for _, task in manager._tasks]
        assert "create_scheduled_task" in task_names
        assert "list_scheduled_tasks" in task_names
        assert "cancel_scheduled_task" in task_names
        assert "scheduled_task_info" in task_names
    
    def test_task_retrieval(self):
        """Test task retrieval by name"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        create_task = manager.get_task("create_scheduled_task")
        assert create_task is not None
        assert isinstance(create_task, ScheduledTaskCreateTask)
        
        list_task = manager.get_task("list_scheduled_tasks")
        assert list_task is not None
        assert isinstance(list_task, ScheduledTaskListTask)
        
        cancel_task = manager.get_task("cancel_scheduled_task")
        assert cancel_task is not None
        assert isinstance(cancel_task, ScheduledTaskCancelTask)
        
        info_task = manager.get_task("scheduled_task_info")
        assert info_task is not None
        assert isinstance(info_task, ScheduledTaskInfoTask)
    
    def test_nonexistent_task_retrieval(self):
        """Test retrieval of non-existent task"""
        manager = SupervisorTask_ScheduledTaskManager()
        task = manager.get_task("nonexistent_task")
        assert task is None
    
    @pytest.mark.asyncio
    async def test_llm_call_structure(self):
        """Test llm_call method structure"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        state = SupervisorTaskState(
            original_input="test query",
            user_guid=self.user_guid,
            messages=[HumanMessage(content="test query")]
        )
        
        with patch('managers.manager_users.ZairaUserManager.find_user', new_callable=AsyncMock, return_value=self.user):
            with patch.object(manager, 'compiled_langgraph') as mock_compiled:
                mock_compiled.ainvoke = AsyncMock(return_value={
                    "messages": [MagicMock(content="Test response")],
                    "call_trace": ["test_trace"]
                })
                
                result = await manager.llm_call(state)
                
                assert result == "Test response"
    
    @pytest.mark.asyncio
    async def test_llm_call_error_handling(self):
        """Test error handling in llm_call"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        state = SupervisorTaskState(
            original_input="test query",
            user_guid=self.user_guid,
            messages=[HumanMessage(content="test query")]
        )
        
        with patch('managers.manager_users.ZairaUserManager.find_user', new_callable=AsyncMock, return_value=self.user):
            with patch('etc.helper_functions.exit') as mock_exit:  # Prevent exit() call
                with patch.object(manager, 'compiled_langgraph') as mock_compiled:
                    mock_compiled.ainvoke = AsyncMock(side_effect=Exception("Test error"))
                    
                    result = await manager.llm_call(state)
                    
                    assert "Test error" in result


class TestScheduledTaskTools:
    """Test suite for scheduled task tools"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.user_guid = str(uuid4())
        self.user = MagicMock(spec=ZairaUser)
        self.user.GUID = self.user_guid
        self.task_id = str(uuid4())
    
    @pytest.mark.asyncio
    async def test_create_scheduled_task_tool(self):
        """Test CreateScheduledTaskTool"""
        tool = CreateScheduledTaskTool()
        
        with patch('etc.helper_functions.exit') as mock_exit:  # Prevent exit() call
            with patch('userprofiles.ScheduledZairaTask.ScheduledZairaTask') as mock_task_class, \
                 patch('managers.manager_users.ZairaUserManager.find_user', new_callable=AsyncMock) as mock_find_user, \
                 patch('asyncio.create_task') as mock_create_task, \
                 patch('endpoints.mybot_generic.MyBot_Generic') as mock_bot:
                
                mock_find_user.return_value = self.user
                mock_task = MagicMock()
                mock_task.task_id = self.task_id
                mock_task.run_task = AsyncMock()  # Mock the async run_task method
                mock_task_class.return_value = mock_task
                
                result = await tool._arun(
                    schedule_prompt="Send me a reminder in 30 minutes",
                    user_guid=self.user_guid
                )
                
                assert f"Created scheduled task: {self.task_id}" in result
                mock_task_class.assert_called_once()
                mock_create_task.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_scheduled_task_tool_user_not_found(self):
        """Test CreateScheduledTaskTool with non-existent user"""
        tool = CreateScheduledTaskTool()
        
        with patch('managers.manager_users.ZairaUserManager.find_user', new_callable=AsyncMock) as mock_find_user:
            mock_find_user.return_value = None
            
            result = await tool._arun(
                schedule_prompt="Test prompt",
                user_guid=self.user_guid
            )
            
            assert f"User {self.user_guid} not found" in result
    
    @pytest.mark.asyncio
    async def test_list_scheduled_tasks_tool(self):
        """Test ListScheduledTasksTool"""
        tool = ListScheduledTasksTool()
        
        mock_tasks = [
            {
                'task_id': self.task_id,
                'schedule_prompt': 'Send reminder in 30 minutes',
                'target_prompt': 'Send reminder',
                'next_execution': '2024-01-01 10:00:00',
                'created_at': '2024-01-01 09:30:00'
            }
        ]
        
        with patch('tasks.inputs.task_scheduled_task_manager.ScheduledTaskPersistenceManager') as mock_persistence:
            mock_persistence.get_instance.return_value.get_active_tasks = AsyncMock(return_value=mock_tasks)
            
            result = await tool._arun(user_guid=self.user_guid)
            
            assert "Active scheduled tasks:" in result
            assert self.task_id in result
            assert "Send reminder in 30 minutes" in result
    
    @pytest.mark.asyncio
    async def test_list_scheduled_tasks_tool_no_tasks(self):
        """Test ListScheduledTasksTool with no active tasks"""
        tool = ListScheduledTasksTool()
        
        with patch('tasks.inputs.task_scheduled_task_manager.ScheduledTaskPersistenceManager') as mock_persistence:
            mock_persistence.get_instance.return_value.get_active_tasks = AsyncMock(return_value=[])
            
            result = await tool._arun(user_guid=self.user_guid)
            
            assert "No active scheduled tasks found" in result
    
    @pytest.mark.asyncio
    async def test_cancel_scheduled_task_tool(self):
        """Test CancelScheduledTaskTool"""
        tool = CancelScheduledTaskTool()
        
        with patch('tasks.inputs.task_scheduled_task_manager.ScheduledTaskPersistenceManager') as mock_persistence:
            mock_persistence.get_instance.return_value.cancel_task = AsyncMock(return_value=True)
            
            result = await tool._arun(task_id=self.task_id)
            
            assert f"Successfully cancelled task {self.task_id}" in result
    
    @pytest.mark.asyncio
    async def test_cancel_scheduled_task_tool_failure(self):
        """Test CancelScheduledTaskTool failure"""
        tool = CancelScheduledTaskTool()
        
        with patch('tasks.inputs.task_scheduled_task_manager.ScheduledTaskPersistenceManager') as mock_persistence:
            mock_persistence.get_instance.return_value.cancel_task = AsyncMock(return_value=False)
            
            result = await tool._arun(task_id=self.task_id)
            
            assert f"Failed to cancel task {self.task_id}" in result


class TestScheduledTaskTasks:
    """Test suite for scheduled task supervisor tasks"""
    
    def test_scheduled_task_create_task(self):
        """Test ScheduledTaskCreateTask initialization"""
        task = ScheduledTaskCreateTask()
        assert task.name == "create_scheduled_task"
        assert len(task.get_tools()) == 1
        assert isinstance(task.get_tools()[0], CreateScheduledTaskTool)
    
    def test_scheduled_task_list_task(self):
        """Test ScheduledTaskListTask initialization"""
        task = ScheduledTaskListTask()
        assert task.name == "list_scheduled_tasks"
        assert len(task.get_tools()) == 1
        assert isinstance(task.get_tools()[0], ListScheduledTasksTool)
    
    def test_scheduled_task_cancel_task(self):
        """Test ScheduledTaskCancelTask initialization"""
        task = ScheduledTaskCancelTask()
        assert task.name == "cancel_scheduled_task"
        assert len(task.get_tools()) == 1
        assert isinstance(task.get_tools()[0], CancelScheduledTaskTool)
    
    def test_scheduled_task_info_task(self):
        """Test ScheduledTaskInfoTask initialization"""
        task = ScheduledTaskInfoTask()
        assert task.name == "scheduled_task_info"
        assert len(task.get_tools()) == 0  # No tools needed for info task
    
    @pytest.mark.asyncio
    async def test_scheduled_task_info_task_response(self):
        """Test ScheduledTaskInfoTask response"""
        task = ScheduledTaskInfoTask()
        
        state = SupervisorTaskState(
            original_input="What can you do?",
            user_guid=str(uuid4()),
            messages=[HumanMessage(content="What can you do?")]
        )
        
        result = await task.llm_call(state)
        
        assert "Scheduled Tasks Information:" in result
        assert "Create Tasks" in result
        assert "List Tasks" in result
        assert "Cancel Tasks" in result
        assert "Task Types" in result


class TestScheduledTaskPersistenceIntegration:
    """Test integration with ScheduledTaskPersistenceManager"""
    
    def test_persistence_manager_singleton(self):
        """Test that persistence manager maintains singleton pattern"""
        manager1 = ScheduledTaskPersistenceManager.get_instance()
        manager2 = ScheduledTaskPersistenceManager.get_instance()
        assert manager1 is manager2
    
    @pytest.mark.asyncio
    async def test_tools_use_persistence_manager(self):
        """Test that tools properly use persistence manager"""
        tool = ListScheduledTasksTool()
        
        with patch('etc.helper_functions.exit') as mock_exit:  # Prevent exit() call
            with patch.object(ScheduledTaskPersistenceManager, 'get_instance') as mock_get_instance:
                mock_persistence = MagicMock()
                mock_persistence.get_active_tasks = AsyncMock(return_value=[])
                mock_get_instance.return_value = mock_persistence
                
                await tool._arun(user_guid=str(uuid4()))
                
                mock_get_instance.assert_called_once()
                mock_persistence.get_active_tasks.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cancel_tool_uses_persistence_manager(self):
        """Test that cancel tool properly uses persistence manager"""
        tool = CancelScheduledTaskTool()
        task_id = str(uuid4())
        
        with patch('etc.helper_functions.exit') as mock_exit:  # Prevent exit() call
            with patch.object(ScheduledTaskPersistenceManager, 'get_instance') as mock_get_instance:
                mock_persistence = MagicMock()
                mock_persistence.cancel_task = AsyncMock(return_value=True)
                mock_get_instance.return_value = mock_persistence
                
                result = await tool._arun(task_id=task_id)
                
                mock_get_instance.assert_called_once()
                mock_persistence.cancel_task.assert_called_once_with(task_id, "User requested cancellation")
                assert "Successfully cancelled" in result


class TestScheduledTaskManagerIntegration:
    """Integration tests for ScheduledTaskManager with full workflow"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.user_guid = str(uuid4())
        self.user = MagicMock(spec=ZairaUser)
        self.user.GUID = self.user_guid
        self.user.username = "test_user"
        self.user.get_chat_history.return_value = []
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow_simulation(self):
        """Test simulated end-to-end workflow"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        # Test different query types that should route to different tasks
        test_cases = [
            ("Create a reminder in 30 minutes", "create_scheduled_task"),
            ("Show me my scheduled tasks", "list_scheduled_tasks"),
            ("Cancel task 12345", "cancel_scheduled_task"),
            ("What can you do with scheduled tasks?", "scheduled_task_info")
        ]
        
        for query, expected_task in test_cases:
            task = manager.get_task(expected_task)
            assert task is not None, f"Task {expected_task} should exist"
            assert task.name == expected_task
    
    def test_no_hardcoded_query_comparisons(self):
        """Verify no hardcoded query word comparisons exist"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        # Check that the manager doesn't have hardcoded word comparisons
        import inspect
        source = inspect.getsource(manager.llm_call)
        
        # These patterns should NOT exist in the new implementation
        forbidden_patterns = [
            "if any(word in query for word in",
            "if 'list' in query.lower()",
            "if 'show' in query.lower()",
            "if 'cancel' in query.lower()",
            "if 'create' in query.lower()"
        ]
        
        for pattern in forbidden_patterns:
            assert pattern not in source, f"Found forbidden hardcoded pattern: {pattern}"
    
    @pytest.mark.asyncio
    async def test_supervisor_compilation(self):
        """Test that supervisor compiles correctly"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        # Compile the supervisor
        compiled_supervisor = manager.compile()
        
        assert compiled_supervisor is not None
        assert manager.compiled_langgraph is not None
        assert hasattr(manager.compiled_langgraph, 'ainvoke')