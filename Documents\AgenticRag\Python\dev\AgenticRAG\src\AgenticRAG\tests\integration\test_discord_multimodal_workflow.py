# tests/integration/test_discord_multimodal_workflow.py
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from userprofiles.ZairaUser import ZairaUser
from userprofiles.permission_levels import PERMISSION_LEVELS
from endpoints.mybot_generic import MyBot_Generic
from managers.manager_multimodal import MultimodalManager
from managers.manager_users import ZairaUserManager
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from uuid import uuid4
import tempfile
import os
import asyncio
import json

class TestDiscordMultimodalWorkflow:
    """Integration tests for Discord multimodal workflow."""
    
    @pytest.fixture
    def mock_discord_message(self):
        """Create a mock Discord message with attachments."""
        mock_message = MagicMock()
        mock_message.author = MagicMock()
        mock_message.author.id = 12345
        mock_message.content = "Can you analyze this image for me?"
        mock_message.channel = MagicMock()
        mock_message.channel.name = "zaira-test"
        mock_message.guild = MagicMock()
        mock_message.guild.id = 123456789
        
        # Mock attachment
        mock_attachment = MagicMock()
        mock_attachment.filename = "test_image.jpg"
        mock_attachment.read = AsyncMock(return_value=b"fake_image_data")
        mock_message.attachments = [mock_attachment]
        
        return mock_message
    
    @pytest.fixture
    def temp_image_file(self):
        """Create a temporary image file for testing."""
        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as f:
            # Write minimal JPEG header
            f.write(b'\xFF\xD8\xFF\xE0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00')
            temp_path = f.name
        yield temp_path
        # Cleanup
        try:
            os.unlink(temp_path)
        except:
            pass
    
    @pytest.mark.asyncio
    async def test_end_to_end_discord_image_processing(self, mock_discord_message, temp_image_file):
        """Test complete workflow from Discord message to AI response."""
        # Set up test user
        test_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Mock bot
        mock_bot = MagicMock(spec=MyBot_Generic)
        mock_bot.send_reply = AsyncMock()
        
        # Mock file download to use our temp file
        with patch('builtins.open', mock_open()) as mock_file:
            # Mock the vision API response
            mock_vision_response = MagicMock()
            mock_vision_response.choices = [MagicMock()]
            mock_vision_response.choices[0].message = MagicMock()
            mock_vision_response.choices[0].message.content = "This image shows a beautiful landscape with mountains and a lake."
            
            # Mock OpenAI client
            with patch('openai.OpenAI') as mock_openai:
                mock_client = MagicMock()
                mock_client.chat.completions.create.return_value = mock_vision_response
                mock_openai.return_value = mock_client
                
                # Mock base64 encoding
                with patch('base64.b64encode', return_value=b'fake_base64_data'):
                    with patch('base64.b64encode.decode', return_value='fake_base64_string'):
                        # Mock file operations
                        with patch('pathlib.Path.exists', return_value=True):
                            with patch('pathlib.Path.is_file', return_value=True):
                                # Mock task creation to avoid full system setup
                                with patch.object(test_user, 'start_task', new_callable=AsyncMock) as mock_start_task:
                                    # Test the message processing
                                    await test_user.on_message(
                                        complete_message="Can you analyze this image for me?",
                                        calling_bot=mock_bot,
                                        attachments=[temp_image_file]
                                    )
                                    
                                    # Verify task was started with enhanced message
                                    mock_start_task.assert_called_once()
                                    call_args = mock_start_task.call_args
                                    enhanced_message = call_args[1]['complete_message']
                                    
                                    # Verify message contains original text
                                    assert "Can you analyze this image for me?" in enhanced_message
                                    
                                    # Verify attachment analysis was added
                                    assert "Attachment Analysis:" in enhanced_message
                                    assert "Image:" in enhanced_message
                                    assert "beautiful landscape" in enhanced_message
    
    @pytest.mark.asyncio
    async def test_multimodal_manager_integration(self, temp_image_file):
        """Test integration with MultimodalManager."""
        # Set up MultimodalManager
        await MultimodalManager.setup()
        
        # Mock OpenAI API response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message = MagicMock()
        mock_response.choices[0].message.content = "Test image analysis result."
        
        with patch('openai.OpenAI') as mock_openai:
            mock_client = MagicMock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_openai.return_value = mock_client
            
            # Mock base64 encoding
            with patch('base64.b64encode', return_value=b'fake_base64_data'):
                with patch.object(MultimodalManager, '_encode_image_to_base64', return_value='fake_base64_string'):
                    # Test image summary generation
                    result = await MultimodalManager.generate_image_summary(
                        image_path=temp_image_file,
                        context="Test context"
                    )
                    
                    assert result == "Test image analysis result."
                    
                    # Verify API was called with correct parameters
                    mock_client.chat.completions.create.assert_called_once()
                    call_args = mock_client.chat.completions.create.call_args
                    assert call_args[1]['model'] == 'gpt-4o-mini'
                    assert call_args[1]['max_tokens'] == 500
    
    @pytest.mark.asyncio
    async def test_error_handling_in_multimodal_workflow(self, temp_image_file):
        """Test error handling throughout the multimodal workflow."""
        test_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        mock_bot = MagicMock(spec=MyBot_Generic)
        mock_bot.send_reply = AsyncMock()
        
        # Mock MultimodalManager to raise an exception
        with patch.object(MultimodalManager, 'generate_image_summary', side_effect=Exception("API error")):
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                with patch.object(test_user, 'start_task', new_callable=AsyncMock) as mock_start_task:
                    await test_user.on_message(
                        complete_message="Test message",
                        calling_bot=mock_bot,
                        attachments=[temp_image_file]
                    )
                    
                    # Verify exception was handled
                    mock_exception.assert_called()
                    
                    # Verify task was still started (with error message)
                    mock_start_task.assert_called_once()
                    call_args = mock_start_task.call_args
                    enhanced_message = call_args[1]['complete_message']
                    assert "Could not analyze image content" in enhanced_message
    
    @pytest.mark.asyncio
    async def test_permission_based_multimodal_access(self):
        """Test that multimodal features respect user permissions."""
        # Test with GUEST user (should be disabled)
        guest_user = ZairaUser(
            username="guest_user",
            rank=PERMISSION_LEVELS.GUEST,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        multimodal_enabled = await guest_user.is_multimodal_enabled()
        assert multimodal_enabled is False
        
        # Test with USER (should be enabled)
        user = ZairaUser(
            username="regular_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        with patch.object(MultimodalManager, 'setup', new_callable=AsyncMock):
            multimodal_enabled = await user.is_multimodal_enabled()
            assert multimodal_enabled is True
        
        # Test with ADMIN (should be enabled)
        admin_user = ZairaUser(
            username="admin_user",
            rank=PERMISSION_LEVELS.ADMIN,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        with patch.object(MultimodalManager, 'setup', new_callable=AsyncMock):
            multimodal_enabled = await admin_user.is_multimodal_enabled()
            assert multimodal_enabled is True
    
    @pytest.mark.asyncio
    async def test_mixed_attachment_types_processing(self, temp_image_file):
        """Test processing of mixed attachment types (images and documents)."""
        test_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Create a temporary text file
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False, mode='w') as f:
            f.write("This is a test document with important information.")
            temp_text_file = f.name
        
        try:
            mock_bot = MagicMock(spec=MyBot_Generic)
            mock_bot.send_reply = AsyncMock()
            
            # Mock image analysis
            with patch.object(MultimodalManager, 'generate_image_summary', return_value="Image analysis result"):
                # Mock document processing
                with patch('unstructured.partition.auto.partition', return_value="Document content extracted"):
                    with patch.object(test_user, 'start_task', new_callable=AsyncMock) as mock_start_task:
                        await test_user.on_message(
                            complete_message="Please analyze both files",
                            calling_bot=mock_bot,
                            attachments=[temp_image_file, temp_text_file]
                        )
                        
                        # Verify task was started
                        mock_start_task.assert_called_once()
                        call_args = mock_start_task.call_args
                        enhanced_message = call_args[1]['complete_message']
                        
                        # Verify both file types were processed
                        assert "Image analysis result" in enhanced_message
                        assert "Document content extracted" in enhanced_message
                        assert "Attachment Analysis:" in enhanced_message
        
        finally:
            # Cleanup
            try:
                os.unlink(temp_text_file)
            except:
                pass
    
    @pytest.mark.asyncio
    async def test_large_image_handling(self):
        """Test handling of large images (should be handled gracefully)."""
        test_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Create a mock large image file
        large_image_path = "/tmp/large_image.jpg"
        
        mock_bot = MagicMock(spec=MyBot_Generic)
        mock_bot.send_reply = AsyncMock()
        
        # Mock file size check and processing
        with patch('pathlib.Path.stat') as mock_stat:
            mock_stat.return_value.st_size = 10 * 1024 * 1024  # 10MB
            
            with patch.object(MultimodalManager, 'generate_image_summary', return_value="Large image processed"):
                with patch.object(test_user, 'start_task', new_callable=AsyncMock) as mock_start_task:
                    await test_user.on_message(
                        complete_message="Analyze this large image",
                        calling_bot=mock_bot,
                        attachments=[large_image_path]
                    )
                    
                    # Verify processing continued despite large file
                    mock_start_task.assert_called_once()
                    call_args = mock_start_task.call_args
                    enhanced_message = call_args[1]['complete_message']
                    assert "Large image processed" in enhanced_message
    
    @pytest.mark.asyncio
    async def test_concurrent_multimodal_processing(self, temp_image_file):
        """Test concurrent processing of multiple users with multimodal attachments."""
        # Create multiple users
        users = []
        for i in range(3):
            user = ZairaUser(
                username=f"user_{i}",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            users.append(user)
        
        mock_bot = MagicMock(spec=MyBot_Generic)
        mock_bot.send_reply = AsyncMock()
        
        # Mock image analysis
        with patch.object(MultimodalManager, 'generate_image_summary', return_value="Concurrent image analysis"):
            # Create tasks for concurrent processing
            tasks = []
            for i, user in enumerate(users):
                with patch.object(user, 'start_task', new_callable=AsyncMock) as mock_start_task:
                    task = user.on_message(
                        complete_message=f"Message from user {i}",
                        calling_bot=mock_bot,
                        attachments=[temp_image_file]
                    )
                    tasks.append((task, mock_start_task))
            
            # Execute all tasks concurrently
            for task, mock_start_task in tasks:
                await task
                # Verify each user's task was started
                mock_start_task.assert_called_once()