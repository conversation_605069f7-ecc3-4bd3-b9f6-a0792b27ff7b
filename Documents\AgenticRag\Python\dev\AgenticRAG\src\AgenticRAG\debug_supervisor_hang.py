#!/usr/bin/env python3
"""
Focused debug script to identify supervisor creation hang.
This script isolates the supervisor creation process.
"""

import asyncio
import sys
import os
import time
from typing import Optional

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def log_with_timestamp(message: str):
    """Log message with precise timestamp"""
    timestamp = time.strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] DEBUG_SUPERVISOR: {message}")
    sys.stdout.flush()

async def debug_supervisor_creation_detailed():
    """Debug supervisor creation with extreme detail"""
    log_with_timestamp("=== SUPERVISOR CREATION DEBUG STARTED ===")
    
    try:
        # Basic setup first
        log_with_timestamp("Setting up basic environment...")
        from imports import *
        await LogFire.setup()
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        log_with_timestamp("✅ Basic environment setup complete")
        
        # Setup minimal required managers
        log_with_timestamp("Setting up minimal required managers...")
        await setup_minimal_managers()
        log_with_timestamp("✅ Minimal managers setup complete")
        
        # Now test supervisor creation step by step
        log_with_timestamp("Starting detailed supervisor creation test...")
        await test_supervisor_creation_steps()
        log_with_timestamp("✅ Supervisor creation test complete")
        
        log_with_timestamp("=== SUPERVISOR CREATION DEBUG COMPLETED ===")
        
    except Exception as e:
        log_with_timestamp(f"❌ ERROR in supervisor debug: {str(e)}")
        import traceback
        log_with_timestamp(f"❌ TRACEBACK: {traceback.format_exc()}")
        raise

async def setup_minimal_managers():
    """Setup only the managers required for supervisor creation"""
    try:
        from managers.manager_supervisors import SupervisorManager
        from managers.manager_users import ZairaUserManager
        from managers.manager_prompts import PromptManager
        
        log_with_timestamp("Setting up SupervisorManager...")
        await SupervisorManager.setup()
        log_with_timestamp("✅ SupervisorManager setup complete")
        
        log_with_timestamp("Setting up PromptManager...")
        await PromptManager.loadDefaultPrompts()
        log_with_timestamp("✅ PromptManager setup complete")
        
        # Create a minimal user for testing
        log_with_timestamp("Creating test user...")
        from userprofiles.permission_levels import PERMISSION_LEVELS
        test_user = await ZairaUserManager.add_user("TestUser", PERMISSION_LEVELS.ADMIN, 
                                                   ZairaUserManager.create_guid(), 
                                                   ZairaUserManager.create_guid())
        log_with_timestamp("✅ Test user created")
        
    except Exception as e:
        log_with_timestamp(f"❌ ERROR in minimal manager setup: {str(e)}")
        raise

async def test_supervisor_creation_steps():
    """Test each step of supervisor creation"""
    try:
        # Test 1: Import supervisor creation functions
        log_with_timestamp("Test 1: Importing supervisor creation functions...")
        from tasks.task_top_level_supervisor import create_top_level_supervisor
        from tasks.inputs.quick_search import create_task_quick_rag_search, create_task_quick_llm_search, create_task_quick_complexity_search
        from tasks.inputs.task_language_detector import create_task_language_detector
        log_with_timestamp("✅ Test 1: Imports successful")
        
        # Test 2: Create individual tasks first
        log_with_timestamp("Test 2: Creating individual tasks...")
        
        log_with_timestamp("Test 2a: Creating quick RAG search task...")
        quick_rag_task = await create_task_quick_rag_search()
        log_with_timestamp("✅ Test 2a: Quick RAG search task created")
        
        log_with_timestamp("Test 2b: Creating quick LLM search task...")
        quick_llm_task = await create_task_quick_llm_search()
        log_with_timestamp("✅ Test 2b: Quick LLM search task created")
        
        log_with_timestamp("Test 2c: Creating quick complexity task...")
        quick_complexity_task = await create_task_quick_complexity_search()
        log_with_timestamp("✅ Test 2c: Quick complexity task created")
        
        log_with_timestamp("Test 2d: Creating language detector task...")
        language_detector_task = await create_task_language_detector()
        log_with_timestamp("✅ Test 2d: Language detector task created")
        
        # Test 3: Test task compilation
        log_with_timestamp("Test 3: Testing task compilation...")
        
        log_with_timestamp("Test 3a: Compiling quick RAG task...")
        if quick_rag_task.compiled_langgraph is None:
            quick_rag_task.compile_default()
        log_with_timestamp("✅ Test 3a: Quick RAG task compiled")
        
        log_with_timestamp("Test 3b: Compiling quick LLM task...")
        if quick_llm_task.compiled_langgraph is None:
            quick_llm_task.compile_default()
        log_with_timestamp("✅ Test 3b: Quick LLM task compiled")
        
        # Test 4: Create simple supervisor with minimal tasks
        log_with_timestamp("Test 4: Creating simple supervisor...")
        await test_simple_supervisor_creation()
        log_with_timestamp("✅ Test 4: Simple supervisor created")
        
        # Test 5: Try full supervisor creation
        log_with_timestamp("Test 5: Attempting full supervisor creation...")
        log_with_timestamp("WARNING: This is where the hang likely occurs...")
        
        # Set a timeout for this operation
        try:
            supervisor = await asyncio.wait_for(create_top_level_supervisor(), timeout=30.0)
            log_with_timestamp("✅ Test 5: Full supervisor creation successful!")
        except asyncio.TimeoutError:
            log_with_timestamp("❌ Test 5: TIMEOUT - Full supervisor creation hung after 30 seconds")
            log_with_timestamp("This confirms the hang is in create_top_level_supervisor()")
            raise
        
    except Exception as e:
        log_with_timestamp(f"❌ ERROR in supervisor creation steps: {str(e)}")
        raise

async def test_simple_supervisor_creation():
    """Test creating a simple supervisor with minimal tasks"""
    try:
        from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor
        
        log_with_timestamp("Creating minimal supervisor...")
        
        # Create a very simple supervisor
        simple_supervisor = SupervisorManager.register_supervisor(
            SupervisorSupervisor(name="test_supervisor", prompt="Test supervisor")
        )
        
        log_with_timestamp("Compiling minimal supervisor...")
        compiled_supervisor = simple_supervisor.compile()
        log_with_timestamp("✅ Minimal supervisor compiled successfully")
        
        return compiled_supervisor
        
    except Exception as e:
        log_with_timestamp(f"❌ ERROR in simple supervisor creation: {str(e)}")
        raise

async def test_individual_task_creation():
    """Test creating tasks one by one to identify problematic task"""
    log_with_timestamp("=== INDIVIDUAL TASK CREATION TEST ===")
    
    task_creators = [
        ("quick_rag_search", "tasks.inputs.quick_search", "create_task_quick_rag_search"),
        ("quick_llm_search", "tasks.inputs.quick_search", "create_task_quick_llm_search"),
        ("quick_complexity", "tasks.inputs.quick_search", "create_task_quick_complexity_search"),
        ("language_detector", "tasks.inputs.task_language_detector", "create_task_language_detector"),
        ("retrieval_supervisor", "tasks.inputs.task_retrieval", "create_supervisor_retrieval"),
        ("scheduled_task_manager", "tasks.inputs.task_scheduled_request_manager", "create_task_scheduled_request_manager"),
        ("email_generator", "tasks.processing.task_email_generator", "create_task_email_generator"),
    ]
    
    for task_name, module_name, function_name in task_creators:
        try:
            log_with_timestamp(f"Testing {task_name}...")
            
            # Import the module and function
            module = __import__(module_name, fromlist=[function_name])
            create_function = getattr(module, function_name)
            
            # Create the task with timeout
            task = await asyncio.wait_for(create_function(), timeout=10.0)
            log_with_timestamp(f"✅ {task_name} created successfully")
            
        except asyncio.TimeoutError:
            log_with_timestamp(f"❌ {task_name} TIMED OUT - This task is causing the hang!")
            break
        except Exception as e:
            log_with_timestamp(f"❌ {task_name} failed: {str(e)}")
            break

if __name__ == "__main__":
    log_with_timestamp("Starting supervisor creation debug...")
    try:
        asyncio.run(debug_supervisor_creation_detailed())
        log_with_timestamp("Supervisor creation debug completed successfully!")
    except KeyboardInterrupt:
        log_with_timestamp("Debug interrupted by user")
    except Exception as e:
        log_with_timestamp(f"Debug failed: {str(e)}")
        
        # Also run individual task test
        log_with_timestamp("Running individual task creation test...")
        try:
            asyncio.run(test_individual_task_creation())
        except Exception as e2:
            log_with_timestamp(f"Individual task test also failed: {str(e2)}")
        
        sys.exit(1)
