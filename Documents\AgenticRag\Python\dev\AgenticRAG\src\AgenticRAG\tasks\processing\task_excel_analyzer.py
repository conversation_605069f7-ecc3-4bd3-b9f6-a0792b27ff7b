from imports import *

from langchain_core.tools import BaseTool
from langchain_experimental.agents.agent_toolkits import create_pandas_dataframe_agent
from langchain_openai import ChatOpenAI
from langchain.agents import AgentType
from typing import Optional, Any
import pandas as pd
import json
from datetime import datetime

from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTask_Create_agent, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from managers.manager_logfire import LogFire

class ExcelAnalyzerTool(BaseTool):
    """Tool for analyzing Excel/CSV data using pandas and LangChain agents"""
    name: str = "excel_analyzer_tool"
    description: str = "Analyzes Excel/CSV data and answers questions about the dataset"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.df = None
        self.agent = None
    
    def _run(self, query: str, file_path: str = None, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, query: str, file_path: str = None, state: SupervisorTaskState = None) -> str:
        """Analyze data and answer questions"""
        try:
            # Handle case where state might be passed as dict
            if isinstance(state, dict):
                from managers.manager_supervisors import SupervisorTaskState
                state = SupervisorTaskState(**state)
            
            # Load data if file path provided
            if file_path and self.df is None:
                await self._load_data(file_path)
            
            # Use default dataset if no data loaded
            if self.df is None:
                await self._load_default_data()
            
            # Create agent if not exists
            if self.agent is None:
                await self._create_agent()
            
            # Execute query
            result = await self._execute_query(query)
            
            LogFire.log("TASK", f"Excel analysis completed: {query[:50]}...")
            return result
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "excel_analyzer_tool", state)
            return f"Error analyzing data: {str(e)}"
    
    async def _load_data(self, file_path: str):
        """Load data from Excel or CSV file"""
        try:
            if file_path.endswith('.csv'):
                self.df = pd.read_csv(file_path)
            elif file_path.endswith(('.xlsx', '.xls')):
                self.df = pd.read_excel(file_path)
            else:
                raise ValueError("Unsupported file format. Use CSV or Excel files.")
            
            LogFire.log("INIT", f"Loaded data from {file_path}: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "load_data", None)
            raise
    
    async def _load_default_data(self):
        """Load default dataset for demonstration"""
        try:
            # Use a safer default path or create sample data
            sample_data = {
                'name': ['Alice', 'Bob', 'Charlie', 'Diana'],
                'age': [25, 30, 35, 28],
                'salary': [50000, 60000, 70000, 55000],
                'department': ['Engineering', 'Sales', 'Marketing', 'Engineering']
            }
            self.df = pd.DataFrame(sample_data)
            LogFire.log("INIT", "Loaded default sample dataset")
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "load_default_data", None)
            raise
    
    async def _create_agent(self):
        """Create pandas dataframe agent"""
        try:
            self.agent = create_pandas_dataframe_agent(
                ChatOpenAI(temperature=0, model="gpt-4o-mini"),
                self.df,
                verbose=True,
                agent_type=AgentType.OPENAI_FUNCTIONS
            )
            LogFire.log("INIT", "Created pandas dataframe agent")
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "create_agent", None)
            raise
    
    async def _execute_query(self, query: str) -> str:
        """Execute query using the pandas agent"""
        try:
            result = self.agent.invoke(query)
            
            # Extract output from agent result
            if isinstance(result, dict) and 'output' in result:
                return str(result['output'])
            else:
                return str(result)
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "execute_query", None)
            return f"Query execution failed: {str(e)}"


async def create_supervisor_excel_analyzer() -> SupervisorSupervisor:
    """Create the Excel analyzer supervisor with data analysis tools"""
    class TaskCreator:
        excel_analyzer_task: SupervisorTask_SingleAgent = None
        
        async def create_tasks(self):
            # Create Excel analyzer tool
            excel_tool = ExcelAnalyzerTool()
            
            # Register the Excel analyzer task
            self.excel_analyzer_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(
                name="excel_analyzer_expert", 
                tools=[excel_tool], 
                prompt_id="Excel_Analyzer_Prompt"
            ))
            
            LogFire.log("INIT", "Created Excel analyzer task")

        async def create_supervisor(self) -> SupervisorSupervisor:
            supervisor = SupervisorManager.register_supervisor(SupervisorSupervisor(
                name="excel_analyzer_supervisor",
                prompt="""You are an expert supervisor for Excel and CSV data analysis. 
                You coordinate data analysis tasks and provide insights from spreadsheet data.
                Use the available tools to analyze data, answer questions, and generate reports.
                """
            ))
            
            supervisor.add_task(self.excel_analyzer_task, priority=1)
            return supervisor.compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()