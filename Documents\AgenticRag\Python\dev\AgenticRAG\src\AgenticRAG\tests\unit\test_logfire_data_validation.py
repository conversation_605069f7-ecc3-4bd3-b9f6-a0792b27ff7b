#!/usr/bin/env python3
# tests/unit/test_logfire_data_validation.py

from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from io import StringIO
import sys
from managers.manager_logfire import LogFire


class MockZairaUser:
    """Mock ZairaUser class for testing"""
    def __init__(self, guid="test-guid", session_guid="test-session"):
        self.GUID = guid
        self.sessionGUID = session_guid
        self.chat_history = {session_guid: []}


class TestLogFireDataValidation:
    """Test suite for LogFire.log data type validation"""
    
    def setup_method(self):
        """Set up test environment before each test"""
        # Reset LogFire singleton state
        LogFire._instance = None
        LogFire._initialized = False
        if hasattr(Log<PERSON><PERSON>, '_log_queue'):
            delattr(LogFire, '_log_queue')
        if hasattr(Log<PERSON>ire, '_log_worker_started'):
            delattr(Log<PERSON>ire, '_log_worker_started')
    
    def capture_print_output(self, func, *args, **kwargs):
        """Capture print output from function calls"""
        captured_output = StringIO()
        old_stdout = sys.stdout
        sys.stdout = captured_output
        try:
            func(*args, **kwargs)
        finally:
            sys.stdout = old_stdout
        return captured_output.getvalue()
    
    @patch('etc.helper_functions.is_claude_environment', return_value=False)
    def test_valid_parameters_all_types(self, mock_claude):
        """Test that valid parameters of correct types pass validation"""
        user = MockZairaUser()
        
        # This should not produce any validation error output
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "Test message", 
            "suffix",
            user,
            "test_source",
            "test_exception",
            "info"
        )
        
        # Should not contain any validation error messages
        assert "Invalid" not in output
        assert "LogFire.log:" not in output
    
    def test_invalid_event_code_type(self):
        """Test validation fails for invalid event_code type"""
        output = self.capture_print_output(
            LogFire.log,
            123,  # Invalid: should be str
            "Test message"
        )
        
        assert "LogFire.log: Invalid event_code type or value" in output
        assert "Expected str from" in output
        assert "got int" in output
    
    def test_invalid_event_code_value(self):
        """Test validation fails for invalid event_code value"""
        output = self.capture_print_output(
            LogFire.log,
            "INVALID_CODE",  # Invalid: not in allowed list
            "Test message"
        )
        
        assert "LogFire.log: Invalid event_code type or value" in output
        assert "Expected str from" in output
    
    def test_valid_event_codes(self):
        """Test all valid event codes pass validation"""
        valid_codes = ["INIT", "RETRIEVE", "TOP", "TASK", "OUTPUT", "USER", "ERROR"]
        
        for code in valid_codes:
            output = self.capture_print_output(
                LogFire.log,
                code,
                "Test message"
            )
            # Should not contain validation errors for event_code
            assert "Invalid event_code" not in output
    
    def test_invalid_data_logfire_type(self):
        """Test validation fails for invalid data_logfire type"""
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            123  # Invalid: should be str
        )
        
        assert "LogFire.log: Invalid data_logfire type" in output
        assert "Expected str, got int" in output
    
    def test_invalid_suffix_sql_type(self):
        """Test validation fails for invalid suffix_sql type"""
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "Test message",
            123  # Invalid: should be str
        )
        
        assert "LogFire.log: Invalid suffix_sql type" in output
        assert "Expected str, got int" in output
    
    def test_invalid_user_object_missing_attributes(self):
        """Test validation fails for user object missing required attributes"""
        class InvalidUser:
            pass
        
        invalid_user = InvalidUser()
        
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "Test message",
            "",
            invalid_user
        )
        
        assert "LogFire.log: Invalid user object" in output
        assert "Missing required ZairaUser attributes" in output
    
    def test_invalid_user_object_partial_attributes(self):
        """Test validation fails for user object with only some required attributes"""
        class PartialUser:
            def __init__(self):
                self.GUID = "test-guid"
                # Missing sessionGUID and chat_history
        
        partial_user = PartialUser()
        
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "Test message",
            "",
            partial_user
        )
        
        assert "LogFire.log: Invalid user object" in output
        assert "Missing required ZairaUser attributes" in output
    
    def test_valid_user_object(self):
        """Test validation passes for valid user object"""
        user = MockZairaUser()
        
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "Test message",
            "",
            user
        )
        
        # Should not contain user validation errors
        assert "Invalid user object" not in output
    
    def test_none_user_valid(self):
        """Test validation passes for None user"""
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "Test message",
            "",
            None  # Valid: None is allowed
        )
        
        # Should not contain user validation errors
        assert "Invalid user object" not in output
    
    def test_invalid_source_type(self):
        """Test validation fails for invalid source type"""
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "Test message",
            "",
            None,
            123  # Invalid: should be str
        )
        
        assert "LogFire.log: Invalid source type" in output
        assert "Expected str, got int" in output
    
    def test_invalid_exception_type(self):
        """Test validation fails for invalid exception type"""
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "Test message",
            "",
            None,
            "",
            123  # Invalid: should be str
        )
        
        assert "LogFire.log: Invalid exception type" in output
        assert "Expected str, got int" in output
    
    def test_invalid_severity_type(self):
        """Test validation fails for invalid severity type"""
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "Test message",
            "",
            None,
            "",
            "",
            123  # Invalid: should be str
        )
        
        assert "LogFire.log: Invalid severity type or value" in output
        assert "Expected str from" in output
        assert "got int" in output
    
    def test_invalid_severity_value(self):
        """Test validation fails for invalid severity value"""
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "Test message",
            "",
            None,
            "",
            "",
            "invalid_severity"  # Invalid: not in allowed list
        )
        
        assert "LogFire.log: Invalid severity type or value" in output
        assert "Expected str from" in output
    
    def test_valid_severities(self):
        """Test all valid severity levels pass validation"""
        valid_severities = ["debug", "info", "warning", "error"]
        
        for severity in valid_severities:
            output = self.capture_print_output(
                LogFire.log,
                "INIT",
                "Test message",
                "",
                None,
                "",
                "",
                severity
            )
            # Should not contain validation errors for severity
            assert "Invalid severity" not in output
    
    def test_validation_exception_handling(self):
        """Test that exceptions during validation are caught and handled"""
        # Create a scenario that might cause an exception during validation
        # by mocking hasattr to raise an exception
        with patch('builtins.hasattr', side_effect=Exception("Test exception")):
            output = self.capture_print_output(
                LogFire.log,
                "INIT",
                "Test message",
                "",
                MockZairaUser()
            )
            
            assert "LogFire.log: Data type validation failed with exception" in output
            assert "Test exception" in output
    
    def test_multiple_validation_failures(self):
        """Test that validation catches the first failure and returns early"""
        output = self.capture_print_output(
            LogFire.log,
            123,  # Invalid event_code (first failure)
            456   # Invalid data_logfire (would be second failure)
        )
        
        # Should only show the first validation error (event_code)
        assert "Invalid event_code type or value" in output
        # Should not reach data_logfire validation
        assert "Invalid data_logfire type" not in output
    
    @patch('etc.helper_functions.is_claude_environment', return_value=True)
    def test_claude_environment_validation_still_runs(self, mock_claude):
        """Test that data validation still runs even in Claude environment"""
        # Data validation happens before Claude environment check, so invalid data should still be caught
        output = self.capture_print_output(
            LogFire.log,
            123,  # Invalid: should be str
            456   # Invalid: should be str
        )
        
        # Should still contain validation error messages since validation runs first
        assert "LogFire.log: Invalid event_code type or value" in output
    
    def test_empty_string_parameters(self):
        """Test that empty strings are valid for string parameters"""
        output = self.capture_print_output(
            LogFire.log,
            "INIT",
            "",  # Empty string - should be valid
            "",  # Empty string - should be valid
            None,
            "",  # Empty string - should be valid
            "",  # Empty string - should be valid
            "info"
        )
        
        # Should not contain validation errors for empty strings
        assert "Invalid" not in output
        assert "LogFire.log:" not in output


if __name__ == "__main__":
    pytest.main([__file__, "-v"])