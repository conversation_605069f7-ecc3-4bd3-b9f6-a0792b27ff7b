"""Template loader for control panel HTML templates"""

from pathlib import Path
from typing import Dict, Any, Optional
import os

class ControlPanelTemplateLoader:
    """Loads and renders control panel templates"""
    
    def __init__(self):
        self.template_dir = Path(__file__).parent / "templates"
        self.static_dir = Path(__file__).parent / "static"
        self._template_cache = {}
    
    def load_template(self, template_name: str) -> str:
        """Load a template from the templates directory"""
        if template_name in self._template_cache:
            return self._template_cache[template_name]
        
        template_path = self.template_dir / f"{template_name}.html"
        if not template_path.exists():
            raise FileNotFoundError(f"Template {template_name} not found")
        
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self._template_cache[template_name] = content
        return content
    
    def load_css(self) -> str:
        """Load the CSS file content"""
        css_path = self.static_dir / "css" / "control_panel.css"
        if not css_path.exists():
            return ""
        
        with open(css_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def load_js(self) -> str:
        """Load the JavaScript file content"""
        js_path = self.static_dir / "js" / "control_panel.js"
        if not js_path.exists():
            return ""
        
        with open(js_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def render_dashboard(self, context: Dict[str, Any]) -> str:
        """Render the full dashboard with inline CSS and JS for now"""
        # For now, we'll use inline CSS and JS to avoid serving static files
        # In production, these should be served as separate files
        
        css_content = self.load_css()
        js_content = self.load_js()
        
        # Get status color based on overall status
        overall_status = context.get('overall_status', 'unknown')
        status_color = {
            'healthy': '#10b981',
            'degraded': '#f59e0b',
            'critical': '#dc3545',
            'unknown': '#6c757d'
        }.get(overall_status, '#6c757d')
        
        # Build the HTML with inline styles and scripts
        html = self._build_dashboard_html(context, css_content, js_content, status_color)
        return html
    
    def _build_dashboard_html(self, context: Dict[str, Any], css: str, js: str, status_color: str) -> str:
        """Build the complete dashboard HTML"""
        # Extract metrics from context
        system_health = context.get('system_health', {})
        factory_metrics = context.get('factory_metrics', {})
        quota_overview = context.get('quota_overview', {})
        performance_metrics = context.get('performance_metrics', {})
        
        overall_status = system_health.get('overall_status', 'unknown')
        active_managers = factory_metrics.get('active_managers', 0)
        active_tasks = factory_metrics.get('active_tasks', 0)
        paused_tasks = factory_metrics.get('paused_tasks', 0)
        completed_tasks = factory_metrics.get('completed_tasks', 0)
        total_users_with_quotas = quota_overview.get('total_users_with_quotas', 0)
        
        # For now, return the original inline HTML
        # This will be refactored to use proper templates later
        return self._get_inline_html(overall_status, status_color, active_managers, 
                                    active_tasks, paused_tasks, completed_tasks,
                                    total_users_with_quotas, css, js)
    
    def _get_inline_html(self, overall_status, status_color, active_managers, 
                         active_tasks, paused_tasks, completed_tasks,
                         total_users_with_quotas, css, js) -> str:
        """Get the inline HTML (temporary until full template implementation)"""
        # Return the dashboard HTML with inline CSS and JS
        # This is a temporary solution - in production, use proper template engine
        return f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZairaControl - Scheduled Request Dashboard</title>
    <style>{css}</style>
</head>
<body>
    <div class="header">
        <h1>ZairaControl Dashboard</h1>
        <span class="status-badge" data-status="{overall_status}">{overall_status}</span>
        <div style="display: flex; align-items: center; gap: 1rem;">
            <button onclick="window.location.href='/'" class="btn-primary">
                Dashboard
            </button>
            <div id="autoRefreshContainer">
                <span id="refreshTimer">Auto-refresh: Disabled</span>
                <button onclick="toggleAutoRefresh()" id="refreshToggle" class="btn-small">Enable</button>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- Metrics Grid -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-title">Active Managers</div>
                <div class="metric-value">{active_managers}</div>
                <div class="metric-subtitle">Scheduled request managers</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">Active Tasks</div>
                <div class="metric-value">{active_tasks}</div>
                <div class="metric-subtitle">Currently running</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">Paused Tasks</div>
                <div class="metric-value">{paused_tasks}</div>
                <div class="metric-subtitle">Temporarily stopped</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">Completed Tasks</div>
                <div class="metric-value">{completed_tasks}</div>
                <div class="metric-subtitle">Successfully finished</div>
            </div>
        </div>
        
        <!-- Tab Navigation -->
        <div class="section">
            <div class="tab-navigation">
                <button class="tab-button active" onclick="showTab('overview')">System Overview</button>
                <button class="tab-button" onclick="showTab('user-requests')">User Requests</button>
                <button class="tab-button" onclick="showTab('chat-history')">Chat History</button>
                <button class="tab-button" onclick="showTab('user-list')">User List</button>
            </div>
            
            <!-- Tab Contents -->
            <div id="overview" class="tab-content active">
                <h2>System Overview</h2>
                <div class="system-stats">
                    <div class="stat-item">
                        <div class="stat-label">Total Users</div>
                        <div class="stat-value">{total_users_with_quotas}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">System Status</div>
                        <div class="stat-value">{overall_status}</div>
                    </div>
                </div>
            </div>
            
            <div id="user-requests" class="tab-content">
                <h2>User Scheduled Requests</h2>
                <div class="user-search">
                    <input type="text" id="userGuidInput" placeholder="Enter User GUID">
                    <button onclick="loadUserRequests()">Search</button>
                </div>
                <div id="requestsContainer"></div>
            </div>
            
            <div id="chat-history" class="tab-content">
                <h2>Chat History</h2>
                <div class="user-search">
                    <input type="text" id="chatUserGuidInput" placeholder="Enter User GUID">
                    <button onclick="loadChatHistory()">Load History</button>
                </div>
                <div id="chatContainer"></div>
            </div>
            
            <div id="user-list" class="tab-content">
                <h2>User List</h2>
                <div class="user-search">
                    <input type="text" id="userSearchInput" placeholder="Search users..." onkeyup="filterUsers()">
                </div>
                <div id="userListContainer"></div>
            </div>
        </div>
    </div>
    
    <script>{js}</script>
</body>
</html>'''