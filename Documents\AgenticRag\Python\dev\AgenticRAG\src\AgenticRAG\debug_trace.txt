=== DEBUG TRACE STARTED at 2025-07-19 12:32:13.754739 ===
[2025-07-19 12:32:13.754] Debug trace capture activated - all console output will be logged to debug_trace.txt[2025-07-19 12:32:13.755] Debug trace capture activated - all console output will be logged to debug_trace.txt

[2025-07-19 12:32:35.592] WARNING:discord.client:PyNaCl is not installed, voice will NOT be supported
[2025-07-19 12:32:35.592] WARNING:discord.client:PyNaCl is not installed, voice will NOT be supported
[2025-07-19 12:32:37.830] === dev_run.py main() started ===[2025-07-19 12:32:37.830] === dev_run.py main() started ===

[2025-07-19 12:32:37.830] === About to check Claude environment ===[2025-07-19 12:32:37.830] === About to check Claude environment ===

[2025-07-19 12:32:37.830] === Starting OAuth external endpoint ===[2025-07-19 12:32:37.830] === Starting OAuth external endpoint ===

[2025-07-19 12:32:37.831] Starting OAuth External. Listening on http://localhost:41000:8084.[2025-07-19 12:32:37.831] Starting OAuth External. Listening on http://localhost:41000:8084.

[2025-07-19 12:32:37.832] === About to call mainFunc() ===[2025-07-19 12:32:37.832] === About to call mainFunc() ===

[2025-07-19 12:32:37.832] === mainFunc() started ===[2025-07-19 12:32:37.832] === mainFunc() started ===

[2025-07-19 12:32:37.833] No Claude environment detected[2025-07-19 12:32:37.833] No Claude environment detected

[2025-07-19 12:32:37.833] === About to set up data directories ===[2025-07-19 12:32:37.833] === About to set up data directories ===

[2025-07-19 12:32:37.834] Using data subfolder: AskZaira[2025-07-19 12:32:37.834] Using data subfolder: AskZaira

[2025-07-19 12:32:37.834] Data_dir: c:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\AskZaira[2025-07-19 12:32:37.834] Data_dir: c:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\AskZaira

[2025-07-19 12:32:37.835] Persist dir: c:\Users\<USER>\Documents\AgenticRag\Python\_DATA_EMBEDDED\AskZaira[2025-07-19 12:32:37.835] Persist dir: c:\Users\<USER>\Documents\AgenticRag\Python\_DATA_EMBEDDED\AskZaira

[2025-07-19 12:32:37.844] === About to call init() ===[2025-07-19 12:32:37.844] === About to call init() ===

[2025-07-19 12:32:37.903] LogEntries database table created/verified[2025-07-19 12:32:37.903] LogEntries database table created/verified

[2025-07-19 12:32:38.439] [1mLogfire[0m project URL: ]8;id=581927;https://logfire-eu.pydantic.dev/askzaira/agentic-rag\[4;36mhttps://logfire-eu.pydantic.dev/askzaira/agentic-rag[0m]8;;\
[2025-07-19 12:32:38.439] [1mLogfire[0m project URL: ]8;id=581927;https://logfire-eu.pydantic.dev/askzaira/agentic-rag\[4;36mhttps://logfire-eu.pydantic.dev/askzaira/agentic-rag[0m]8;;\
[2025-07-19 12:32:41.325] Database 'vectordb' already exists.[2025-07-19 12:32:41.325] Database 'vectordb' already exists.

[2025-07-19 12:32:42.523] INFO:httpx:HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
[2025-07-19 12:32:42.523] INFO:httpx:HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
[2025-07-19 12:32:42.524] HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
[2025-07-19 12:32:42.524] HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
[2025-07-19 12:32:43.593] INFO:httpx:HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
[2025-07-19 12:32:43.593] INFO:httpx:HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
[2025-07-19 12:32:43.594] HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
[2025-07-19 12:32:43.594] HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.454] Discord bot setup called[2025-07-19 12:32:44.454] Discord bot setup called

[2025-07-19 12:32:44.782] [32m10:32:44.780[0m [][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:41.464484+00:00
[2025-07-19 12:32:44.782] [32m10:32:44.780[0m [][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:41.464484+00:00
[2025-07-19 12:32:44.810] INFO:managers.manager_users:Creating a new singleton instance of ZairaUserManager.
[2025-07-19 12:32:44.810] INFO:managers.manager_users:Creating a new singleton instance of ZairaUserManager.
[2025-07-19 12:32:44.811] Creating a new singleton instance of ZairaUserManager.
[2025-07-19 12:32:44.811] Creating a new singleton instance of ZairaUserManager.
[2025-07-19 12:32:44.816] INFO:managers.manager_users:Pydantic models rebuilt successfully to resolve forward references
[2025-07-19 12:32:44.816] INFO:managers.manager_users:Pydantic models rebuilt successfully to resolve forward references
[2025-07-19 12:32:44.816] Pydantic models rebuilt successfully to resolve forward references
[2025-07-19 12:32:44.816] Pydantic models rebuilt successfully to resolve forward references
[2025-07-19 12:32:44.816] ZairaUser created[2025-07-19 12:32:44.816] ZairaUser created

[2025-07-19 12:32:44.817] INFO:managers.manager_users:User added: SYSTEM with GUID: 00000000-0000-0000-0000-000000000001
[2025-07-19 12:32:44.817] INFO:managers.manager_users:User added: SYSTEM with GUID: 00000000-0000-0000-0000-000000000001
[2025-07-19 12:32:44.817] User added: SYSTEM with GUID: 00000000-0000-0000-0000-000000000001
[2025-07-19 12:32:44.817] User added: SYSTEM with GUID: 00000000-0000-0000-0000-000000000001
[2025-07-19 12:32:44.818] Loading stored index[2025-07-19 12:32:44.818] Loading stored index

[2025-07-19 12:32:44.819] WARNING:llama_index.vector_stores.qdrant.base:Both client and aclient are provided. If using `:memory:` mode, the data between clients is not synced.
[2025-07-19 12:32:44.819] WARNING:llama_index.vector_stores.qdrant.base:Both client and aclient are provided. If using `:memory:` mode, the data between clients is not synced.
[2025-07-19 12:32:44.819] Both client and aclient are provided. If using `:memory:` mode, the data between clients is not synced.
[2025-07-19 12:32:44.819] Both client and aclient are provided. If using `:memory:` mode, the data between clients is not synced.
[2025-07-19 12:32:44.850] INFO:httpx:HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.850] INFO:httpx:HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.851] HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.851] HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.868] INFO:httpx:HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.868] INFO:httpx:HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.868] HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.869] HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.877] INFO:httpx:HTTP Request: GET http://localhost:6333/collections/mainCollection "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.877] INFO:httpx:HTTP Request: GET http://localhost:6333/collections/mainCollection "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.878] HTTP Request: GET http://localhost:6333/collections/mainCollection "HTTP/1.1 200 OK"
[2025-07-19 12:32:44.878] HTTP Request: GET http://localhost:6333/collections/mainCollection "HTTP/1.1 200 OK"
[2025-07-19 12:32:46.096] INFO:httpx:HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:46.096] INFO:httpx:HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:46.097] HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:46.097] HTTP Request: GET http://localhost:6333/collections/mainCollection/exists "HTTP/1.1 200 OK"
[2025-07-19 12:32:46.110] INFO:httpx:HTTP Request: GET http://localhost:6333/collections/mainCollection "HTTP/1.1 200 OK"
[2025-07-19 12:32:46.110] INFO:httpx:HTTP Request: GET http://localhost:6333/collections/mainCollection "HTTP/1.1 200 OK"
[2025-07-19 12:32:46.111] HTTP Request: GET http://localhost:6333/collections/mainCollection "HTTP/1.1 200 OK"
[2025-07-19 12:32:46.111] HTTP Request: GET http://localhost:6333/collections/mainCollection "HTTP/1.1 200 OK"
[2025-07-19 12:32:47.172] BertForMaskedLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
[2025-07-19 12:32:47.172]   - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
[2025-07-19 12:32:47.172]   - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
[2025-07-19 12:32:47.172]   - If you are not the owner of the model architecture class, please contact the model code owner to update it.
[2025-07-19 12:32:47.172] BertForMaskedLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
[2025-07-19 12:32:47.172]   - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
[2025-07-19 12:32:47.172]   - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
[2025-07-19 12:32:47.172]   - If you are not the owner of the model architecture class, please contact the model code owner to update it.
[2025-07-19 12:32:48.170] WARNING:root:'doc_id' is deprecated and 'id_' will be used instead
[2025-07-19 12:32:48.170] WARNING:root:'doc_id' is deprecated and 'id_' will be used instead
[2025-07-19 12:32:48.170] 'doc_id' is deprecated and 'id_' will be used instead
[2025-07-19 12:32:48.170] 'doc_id' is deprecated and 'id_' will be used instead
[2025-07-19 12:32:48.361] Index loaded[2025-07-19 12:32:48.361] Index loaded



[2025-07-19 12:32:48.920] INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
[2025-07-19 12:32:48.920] INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
[2025-07-19 12:32:48.920] file_cache is only supported with oauth2client<4.0.0
[2025-07-19 12:32:48.920] file_cache is only supported with oauth2client<4.0.0
[2025-07-19 12:32:49.094] Server started at http://0.0.0.0:41000[2025-07-19 12:32:49.094] Server started at http://0.0.0.0:41000

[2025-07-19 12:32:49.095] 41000 endpoint routes:[2025-07-19 12:32:49.095] 41000 endpoint routes:

[2025-07-19 12:32:49.095] http://localhost:41000/[2025-07-19 12:32:49.095] http://localhost:41000/

[2025-07-19 12:32:49.096] http://localhost:41000/[2025-07-19 12:32:49.096] http://localhost:41000/

[2025-07-19 12:32:49.096] http://localhost:41000/login[2025-07-19 12:32:49.096] http://localhost:41000/login

[2025-07-19 12:32:49.097] http://localhost:41000/login[2025-07-19 12:32:49.097] http://localhost:41000/login

[2025-07-19 12:32:49.097] http://localhost:41000/validate-login[2025-07-19 12:32:49.097] http://localhost:41000/validate-login

[2025-07-19 12:32:49.098] http://localhost:41000/dashboard[2025-07-19 12:32:49.098] http://localhost:41000/dashboard

[2025-07-19 12:32:49.098] http://localhost:41000/slack/events[2025-07-19 12:32:49.098] http://localhost:41000/slack/events

[2025-07-19 12:32:49.099] http://localhost:41000/ask[2025-07-19 12:32:49.099] http://localhost:41000/ask

[2025-07-19 12:32:49.100] http://localhost:41000/ask[2025-07-19 12:32:49.100] http://localhost:41000/ask

[2025-07-19 12:32:49.100] http://localhost:41000/ask_url[2025-07-19 12:32:49.100] http://localhost:41000/ask_url

[2025-07-19 12:32:49.101] http://localhost:41000/ask_url[2025-07-19 12:32:49.101] http://localhost:41000/ask_url

[2025-07-19 12:32:49.101] http://localhost:41000/delayed/ask[2025-07-19 12:32:49.101] http://localhost:41000/delayed/ask

[2025-07-19 12:32:49.102] http://localhost:41000/delayed/ask[2025-07-19 12:32:49.102] http://localhost:41000/delayed/ask

[2025-07-19 12:32:49.102] http://localhost:41000/askAgno[2025-07-19 12:32:49.102] http://localhost:41000/askAgno

[2025-07-19 12:32:49.103] http://localhost:41000/askAgno[2025-07-19 12:32:49.103] http://localhost:41000/askAgno

[2025-07-19 12:32:49.103] http://localhost:41000/managers/meltano/ConvertSQLToVectorStore[2025-07-19 12:32:49.103] http://localhost:41000/managers/meltano/ConvertSQLToVectorStore

[2025-07-19 12:32:49.104] http://localhost:41000/managers/meltano/ConvertSQLToVectorStore[2025-07-19 12:32:49.104] http://localhost:41000/managers/meltano/ConvertSQLToVectorStore

[2025-07-19 12:32:49.104] http://localhost:41000/v1/embeddings[2025-07-19 12:32:49.104] http://localhost:41000/v1/embeddings

[2025-07-19 12:32:49.104] http://localhost:41000/onnx/v1/embeddings[2025-07-19 12:32:49.105] http://localhost:41000/onnx/v1/embeddings

[2025-07-19 12:32:49.105] http://localhost:41000/file_upload[2025-07-19 12:32:49.105] http://localhost:41000/file_upload

[2025-07-19 12:32:49.106] http://localhost:41000/restart[2025-07-19 12:32:49.106] http://localhost:41000/restart

[2025-07-19 12:32:49.106] http://localhost:41000/update[2025-07-19 12:32:49.106] http://localhost:41000/update

[2025-07-19 12:32:49.107] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.107] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.107] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.107] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.108] http://localhost:41000/discord/oauth[2025-07-19 12:32:49.108] http://localhost:41000/discord/oauth

[2025-07-19 12:32:49.108] http://localhost:41000/discord/oauth[2025-07-19 12:32:49.108] http://localhost:41000/discord/oauth

[2025-07-19 12:32:49.109] http://localhost:41000/discord/oauth/twice[2025-07-19 12:32:49.109] http://localhost:41000/discord/oauth/twice

[2025-07-19 12:32:49.109] http://localhost:41000/discord/oauth/twice[2025-07-19 12:32:49.109] http://localhost:41000/discord/oauth/twice

[2025-07-19 12:32:49.110] http://localhost:41000/discord/oauth/status_wait[2025-07-19 12:32:49.110] http://localhost:41000/discord/oauth/status_wait

[2025-07-19 12:32:49.110] http://localhost:41000/discord/oauth/status_wait[2025-07-19 12:32:49.110] http://localhost:41000/discord/oauth/status_wait

[2025-07-19 12:32:49.111] http://localhost:41000/discord/oauth/status_get[2025-07-19 12:32:49.111] http://localhost:41000/discord/oauth/status_get

[2025-07-19 12:32:49.111] http://localhost:41000/discord/oauth/status_get[2025-07-19 12:32:49.111] http://localhost:41000/discord/oauth/status_get

[2025-07-19 12:32:49.111] http://localhost:41000/website/oauth_redirect[2025-07-19 12:32:49.112] http://localhost:41000/website/oauth_redirect

[2025-07-19 12:32:49.112] http://localhost:41000/website/oauth[2025-07-19 12:32:49.112] http://localhost:41000/website/oauth

[2025-07-19 12:32:49.112] http://localhost:41000/website/oauth[2025-07-19 12:32:49.112] http://localhost:41000/website/oauth

[2025-07-19 12:32:49.113] http://localhost:41000/website/oauth/twice[2025-07-19 12:32:49.113] http://localhost:41000/website/oauth/twice

[2025-07-19 12:32:49.113] http://localhost:41000/website/oauth/twice[2025-07-19 12:32:49.113] http://localhost:41000/website/oauth/twice

[2025-07-19 12:32:49.114] http://localhost:41000/website/oauth/status_wait[2025-07-19 12:32:49.114] http://localhost:41000/website/oauth/status_wait

[2025-07-19 12:32:49.114] http://localhost:41000/website/oauth/status_wait[2025-07-19 12:32:49.114] http://localhost:41000/website/oauth/status_wait

[2025-07-19 12:32:49.115] http://localhost:41000/website/oauth/status_get[2025-07-19 12:32:49.115] http://localhost:41000/website/oauth/status_get

[2025-07-19 12:32:49.115] http://localhost:41000/website/oauth/status_get[2025-07-19 12:32:49.115] http://localhost:41000/website/oauth/status_get

[2025-07-19 12:32:49.116] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.116] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.116] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.116] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.117] http://localhost:41000/slack/oauth[2025-07-19 12:32:49.117] http://localhost:41000/slack/oauth

[2025-07-19 12:32:49.117] http://localhost:41000/slack/oauth[2025-07-19 12:32:49.117] http://localhost:41000/slack/oauth

[2025-07-19 12:32:49.118] http://localhost:41000/slack/oauth/twice[2025-07-19 12:32:49.118] http://localhost:41000/slack/oauth/twice

[2025-07-19 12:32:49.118] http://localhost:41000/slack/oauth/twice[2025-07-19 12:32:49.119] http://localhost:41000/slack/oauth/twice

[2025-07-19 12:32:49.119] http://localhost:41000/slack/oauth/status_wait[2025-07-19 12:32:49.119] http://localhost:41000/slack/oauth/status_wait

[2025-07-19 12:32:49.119] http://localhost:41000/slack/oauth/status_wait[2025-07-19 12:32:49.119] http://localhost:41000/slack/oauth/status_wait

[2025-07-19 12:32:49.120] http://localhost:41000/slack/oauth/status_get[2025-07-19 12:32:49.120] http://localhost:41000/slack/oauth/status_get

[2025-07-19 12:32:49.120] http://localhost:41000/slack/oauth/status_get[2025-07-19 12:32:49.120] http://localhost:41000/slack/oauth/status_get

[2025-07-19 12:32:49.121] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.121] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.121] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.121] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.121] http://localhost:41000/gcalendar/oauth_redirect[2025-07-19 12:32:49.121] http://localhost:41000/gcalendar/oauth_redirect

[2025-07-19 12:32:49.122] http://localhost:41000/gcalendar/oauth[2025-07-19 12:32:49.122] http://localhost:41000/gcalendar/oauth

[2025-07-19 12:32:49.122] http://localhost:41000/gcalendar/oauth[2025-07-19 12:32:49.122] http://localhost:41000/gcalendar/oauth

[2025-07-19 12:32:49.123] http://localhost:41000/gcalendar/oauth/twice[2025-07-19 12:32:49.123] http://localhost:41000/gcalendar/oauth/twice

[2025-07-19 12:32:49.123] http://localhost:41000/gcalendar/oauth/twice[2025-07-19 12:32:49.123] http://localhost:41000/gcalendar/oauth/twice

[2025-07-19 12:32:49.124] http://localhost:41000/gcalendar/oauth/status_wait[2025-07-19 12:32:49.124] http://localhost:41000/gcalendar/oauth/status_wait

[2025-07-19 12:32:49.124] http://localhost:41000/gcalendar/oauth/status_wait[2025-07-19 12:32:49.124] http://localhost:41000/gcalendar/oauth/status_wait

[2025-07-19 12:32:49.125] http://localhost:41000/gcalendar/oauth/status_get[2025-07-19 12:32:49.125] http://localhost:41000/gcalendar/oauth/status_get

[2025-07-19 12:32:49.125] http://localhost:41000/gcalendar/oauth/status_get[2025-07-19 12:32:49.125] http://localhost:41000/gcalendar/oauth/status_get

[2025-07-19 12:32:49.125] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.125] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.126] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.126] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.126] http://localhost:41000/gmail/oauth_redirect[2025-07-19 12:32:49.126] http://localhost:41000/gmail/oauth_redirect

[2025-07-19 12:32:49.126] http://localhost:41000/gmail/oauth[2025-07-19 12:32:49.127] http://localhost:41000/gmail/oauth

[2025-07-19 12:32:49.127] http://localhost:41000/gmail/oauth[2025-07-19 12:32:49.127] http://localhost:41000/gmail/oauth

[2025-07-19 12:32:49.127] http://localhost:41000/gmail/oauth/twice[2025-07-19 12:32:49.127] http://localhost:41000/gmail/oauth/twice

[2025-07-19 12:32:49.128] http://localhost:41000/gmail/oauth/twice[2025-07-19 12:32:49.128] http://localhost:41000/gmail/oauth/twice

[2025-07-19 12:32:49.128] http://localhost:41000/gmail/oauth/status_wait[2025-07-19 12:32:49.128] http://localhost:41000/gmail/oauth/status_wait

[2025-07-19 12:32:49.128] http://localhost:41000/gmail/oauth/status_wait[2025-07-19 12:32:49.128] http://localhost:41000/gmail/oauth/status_wait

[2025-07-19 12:32:49.129] http://localhost:41000/gmail/oauth/status_get[2025-07-19 12:32:49.129] http://localhost:41000/gmail/oauth/status_get

[2025-07-19 12:32:49.129] http://localhost:41000/gmail/oauth/status_get[2025-07-19 12:32:49.129] http://localhost:41000/gmail/oauth/status_get

[2025-07-19 12:32:49.130] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.130] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.132] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.132] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.133] http://localhost:41000/gdrive/oauth_redirect[2025-07-19 12:32:49.133] http://localhost:41000/gdrive/oauth_redirect

[2025-07-19 12:32:49.133] http://localhost:41000/gdrive/oauth[2025-07-19 12:32:49.134] http://localhost:41000/gdrive/oauth

[2025-07-19 12:32:49.135] http://localhost:41000/gdrive/oauth[2025-07-19 12:32:49.135] http://localhost:41000/gdrive/oauth

[2025-07-19 12:32:49.135] http://localhost:41000/gdrive/oauth/twice[2025-07-19 12:32:49.135] http://localhost:41000/gdrive/oauth/twice

[2025-07-19 12:32:49.136] http://localhost:41000/gdrive/oauth/twice[2025-07-19 12:32:49.136] http://localhost:41000/gdrive/oauth/twice

[2025-07-19 12:32:49.136] http://localhost:41000/gdrive/oauth/status_wait[2025-07-19 12:32:49.137] http://localhost:41000/gdrive/oauth/status_wait

[2025-07-19 12:32:49.137] http://localhost:41000/gdrive/oauth/status_wait[2025-07-19 12:32:49.137] http://localhost:41000/gdrive/oauth/status_wait

[2025-07-19 12:32:49.137] http://localhost:41000/gdrive/oauth/status_get[2025-07-19 12:32:49.137] http://localhost:41000/gdrive/oauth/status_get

[2025-07-19 12:32:49.138] http://localhost:41000/gdrive/oauth/status_get[2025-07-19 12:32:49.138] http://localhost:41000/gdrive/oauth/status_get

[2025-07-19 12:32:49.138] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.138] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.139] http://localhost:41000/oauth_redirect[2025-07-19 12:32:49.139] http://localhost:41000/oauth_redirect

[2025-07-19 12:32:49.139] http://localhost:41000/googleads/oauth[2025-07-19 12:32:49.140] http://localhost:41000/googleads/oauth

[2025-07-19 12:32:49.140] http://localhost:41000/googleads/oauth[2025-07-19 12:32:49.140] http://localhost:41000/googleads/oauth

[2025-07-19 12:32:49.141] http://localhost:41000/googleads/oauth/twice[2025-07-19 12:32:49.141] http://localhost:41000/googleads/oauth/twice

[2025-07-19 12:32:49.141] http://localhost:41000/googleads/oauth/twice[2025-07-19 12:32:49.141] http://localhost:41000/googleads/oauth/twice

[2025-07-19 12:32:49.142] http://localhost:41000/googleads/oauth/status_wait[2025-07-19 12:32:49.142] http://localhost:41000/googleads/oauth/status_wait

[2025-07-19 12:32:49.142] http://localhost:41000/googleads/oauth/status_wait[2025-07-19 12:32:49.142] http://localhost:41000/googleads/oauth/status_wait

[2025-07-19 12:32:49.142] http://localhost:41000/googleads/oauth/status_get[2025-07-19 12:32:49.142] http://localhost:41000/googleads/oauth/status_get

[2025-07-19 12:32:49.143] http://localhost:41000/googleads/oauth/status_get[2025-07-19 12:32:49.143] http://localhost:41000/googleads/oauth/status_get

[2025-07-19 12:32:49.143] http://localhost:41000/airtable/oauth_redirect[2025-07-19 12:32:49.143] http://localhost:41000/airtable/oauth_redirect

[2025-07-19 12:32:49.144] http://localhost:41000/airtable/oauth[2025-07-19 12:32:49.144] http://localhost:41000/airtable/oauth

[2025-07-19 12:32:49.144] http://localhost:41000/airtable/oauth[2025-07-19 12:32:49.144] http://localhost:41000/airtable/oauth

[2025-07-19 12:32:49.145] http://localhost:41000/airtable/oauth/twice[2025-07-19 12:32:49.145] http://localhost:41000/airtable/oauth/twice

[2025-07-19 12:32:49.145] http://localhost:41000/airtable/oauth/twice[2025-07-19 12:32:49.145] http://localhost:41000/airtable/oauth/twice

[2025-07-19 12:32:49.146] http://localhost:41000/airtable/oauth/status_wait[2025-07-19 12:32:49.146] http://localhost:41000/airtable/oauth/status_wait

[2025-07-19 12:32:49.146] http://localhost:41000/airtable/oauth/status_wait[2025-07-19 12:32:49.146] http://localhost:41000/airtable/oauth/status_wait

[2025-07-19 12:32:49.147] http://localhost:41000/airtable/oauth/status_get[2025-07-19 12:32:49.147] http://localhost:41000/airtable/oauth/status_get

[2025-07-19 12:32:49.147] http://localhost:41000/airtable/oauth/status_get[2025-07-19 12:32:49.148] http://localhost:41000/airtable/oauth/status_get

[2025-07-19 12:32:49.148] http://localhost:41000/imap/oauth_redirect[2025-07-19 12:32:49.148] http://localhost:41000/imap/oauth_redirect

[2025-07-19 12:32:49.149] http://localhost:41000/imap/oauth[2025-07-19 12:32:49.149] http://localhost:41000/imap/oauth

[2025-07-19 12:32:49.149] http://localhost:41000/imap/oauth[2025-07-19 12:32:49.149] http://localhost:41000/imap/oauth

[2025-07-19 12:32:49.150] http://localhost:41000/imap/oauth/twice[2025-07-19 12:32:49.150] http://localhost:41000/imap/oauth/twice

[2025-07-19 12:32:49.150] http://localhost:41000/imap/oauth/twice[2025-07-19 12:32:49.151] http://localhost:41000/imap/oauth/twice

[2025-07-19 12:32:49.151] http://localhost:41000/imap/oauth/status_wait[2025-07-19 12:32:49.151] http://localhost:41000/imap/oauth/status_wait

[2025-07-19 12:32:49.151] http://localhost:41000/imap/oauth/status_wait[2025-07-19 12:32:49.151] http://localhost:41000/imap/oauth/status_wait

[2025-07-19 12:32:49.152] http://localhost:41000/imap/oauth/status_get[2025-07-19 12:32:49.152] http://localhost:41000/imap/oauth/status_get

[2025-07-19 12:32:49.152] http://localhost:41000/imap/oauth/status_get[2025-07-19 12:32:49.152] http://localhost:41000/imap/oauth/status_get

[2025-07-19 12:32:49.153] http://localhost:41000/smtp/oauth_redirect[2025-07-19 12:32:49.153] http://localhost:41000/smtp/oauth_redirect

[2025-07-19 12:32:49.153] http://localhost:41000/smtp/oauth[2025-07-19 12:32:49.153] http://localhost:41000/smtp/oauth

[2025-07-19 12:32:49.154] http://localhost:41000/smtp/oauth[2025-07-19 12:32:49.154] http://localhost:41000/smtp/oauth

[2025-07-19 12:32:49.155] http://localhost:41000/smtp/oauth/twice[2025-07-19 12:32:49.155] http://localhost:41000/smtp/oauth/twice

[2025-07-19 12:32:49.156] http://localhost:41000/smtp/oauth/twice[2025-07-19 12:32:49.156] http://localhost:41000/smtp/oauth/twice

[2025-07-19 12:32:49.157] http://localhost:41000/smtp/oauth/status_wait[2025-07-19 12:32:49.157] http://localhost:41000/smtp/oauth/status_wait

[2025-07-19 12:32:49.157] http://localhost:41000/smtp/oauth/status_wait[2025-07-19 12:32:49.157] http://localhost:41000/smtp/oauth/status_wait

[2025-07-19 12:32:49.158] http://localhost:41000/smtp/oauth/status_get[2025-07-19 12:32:49.158] http://localhost:41000/smtp/oauth/status_get

[2025-07-19 12:32:49.158] http://localhost:41000/smtp/oauth/status_get[2025-07-19 12:32:49.158] http://localhost:41000/smtp/oauth/status_get

[2025-07-19 12:32:49.159] http://localhost:41000/woocommerce/oauth_redirect[2025-07-19 12:32:49.159] http://localhost:41000/woocommerce/oauth_redirect

[2025-07-19 12:32:49.159] http://localhost:41000/woocommerce/oauth[2025-07-19 12:32:49.159] http://localhost:41000/woocommerce/oauth

[2025-07-19 12:32:49.160] http://localhost:41000/woocommerce/oauth[2025-07-19 12:32:49.160] http://localhost:41000/woocommerce/oauth

[2025-07-19 12:32:49.160] http://localhost:41000/woocommerce/oauth/twice[2025-07-19 12:32:49.160] http://localhost:41000/woocommerce/oauth/twice

[2025-07-19 12:32:49.161] http://localhost:41000/woocommerce/oauth/twice[2025-07-19 12:32:49.161] http://localhost:41000/woocommerce/oauth/twice

[2025-07-19 12:32:49.161] http://localhost:41000/woocommerce/oauth/status_wait[2025-07-19 12:32:49.161] http://localhost:41000/woocommerce/oauth/status_wait

[2025-07-19 12:32:49.162] http://localhost:41000/woocommerce/oauth/status_wait[2025-07-19 12:32:49.162] http://localhost:41000/woocommerce/oauth/status_wait

[2025-07-19 12:32:49.162] http://localhost:41000/woocommerce/oauth/status_get[2025-07-19 12:32:49.162] http://localhost:41000/woocommerce/oauth/status_get

[2025-07-19 12:32:49.163] http://localhost:41000/woocommerce/oauth/status_get[2025-07-19 12:32:49.163] http://localhost:41000/woocommerce/oauth/status_get

[2025-07-19 12:32:49.163] http://localhost:41000/whatsapp/oauth_redirect[2025-07-19 12:32:49.163] http://localhost:41000/whatsapp/oauth_redirect

[2025-07-19 12:32:49.164] http://localhost:41000/whatsapp/oauth[2025-07-19 12:32:49.164] http://localhost:41000/whatsapp/oauth

[2025-07-19 12:32:49.164] http://localhost:41000/whatsapp/oauth[2025-07-19 12:32:49.164] http://localhost:41000/whatsapp/oauth

[2025-07-19 12:32:49.165] http://localhost:41000/whatsapp/oauth/twice[2025-07-19 12:32:49.165] http://localhost:41000/whatsapp/oauth/twice

[2025-07-19 12:32:49.166] http://localhost:41000/whatsapp/oauth/twice[2025-07-19 12:32:49.166] http://localhost:41000/whatsapp/oauth/twice

[2025-07-19 12:32:49.166] http://localhost:41000/whatsapp/oauth/status_wait[2025-07-19 12:32:49.166] http://localhost:41000/whatsapp/oauth/status_wait

[2025-07-19 12:32:49.167] http://localhost:41000/whatsapp/oauth/status_wait[2025-07-19 12:32:49.167] http://localhost:41000/whatsapp/oauth/status_wait

[2025-07-19 12:32:49.167] http://localhost:41000/whatsapp/oauth/status_get[2025-07-19 12:32:49.167] http://localhost:41000/whatsapp/oauth/status_get

[2025-07-19 12:32:49.168] http://localhost:41000/whatsapp/oauth/status_get[2025-07-19 12:32:49.168] http://localhost:41000/whatsapp/oauth/status_get

[2025-07-19 12:32:49.168] http://localhost:41000/debug/oauth_redirect[2025-07-19 12:32:49.168] http://localhost:41000/debug/oauth_redirect

[2025-07-19 12:32:49.169] http://localhost:41000/debug/oauth[2025-07-19 12:32:49.169] http://localhost:41000/debug/oauth

[2025-07-19 12:32:49.169] http://localhost:41000/debug/oauth[2025-07-19 12:32:49.169] http://localhost:41000/debug/oauth

[2025-07-19 12:32:49.170] http://localhost:41000/debug/oauth/twice[2025-07-19 12:32:49.170] http://localhost:41000/debug/oauth/twice

[2025-07-19 12:32:49.170] http://localhost:41000/debug/oauth/twice[2025-07-19 12:32:49.170] http://localhost:41000/debug/oauth/twice

[2025-07-19 12:32:49.171] http://localhost:41000/debug/oauth/status_wait[2025-07-19 12:32:49.171] http://localhost:41000/debug/oauth/status_wait

[2025-07-19 12:32:49.171] http://localhost:41000/debug/oauth/status_wait[2025-07-19 12:32:49.172] http://localhost:41000/debug/oauth/status_wait

[2025-07-19 12:32:49.172] http://localhost:41000/debug/oauth/status_get[2025-07-19 12:32:49.173] http://localhost:41000/debug/oauth/status_get

[2025-07-19 12:32:49.173] http://localhost:41000/debug/oauth/status_get[2025-07-19 12:32:49.173] http://localhost:41000/debug/oauth/status_get

[2025-07-19 12:32:49.174] http://localhost:41000/ZairaPrompts1/oauth_redirect[2025-07-19 12:32:49.174] http://localhost:41000/ZairaPrompts1/oauth_redirect

[2025-07-19 12:32:49.174] http://localhost:41000/ZairaPrompts1/oauth[2025-07-19 12:32:49.174] http://localhost:41000/ZairaPrompts1/oauth

[2025-07-19 12:32:49.175] http://localhost:41000/ZairaPrompts1/oauth[2025-07-19 12:32:49.175] http://localhost:41000/ZairaPrompts1/oauth

[2025-07-19 12:32:49.175] http://localhost:41000/ZairaPrompts1/oauth/twice[2025-07-19 12:32:49.175] http://localhost:41000/ZairaPrompts1/oauth/twice

[2025-07-19 12:32:49.176] http://localhost:41000/ZairaPrompts1/oauth/twice[2025-07-19 12:32:49.176] http://localhost:41000/ZairaPrompts1/oauth/twice

[2025-07-19 12:32:49.176] http://localhost:41000/ZairaPrompts1/oauth/status_wait[2025-07-19 12:32:49.176] http://localhost:41000/ZairaPrompts1/oauth/status_wait

[2025-07-19 12:32:49.177] http://localhost:41000/ZairaPrompts1/oauth/status_wait[2025-07-19 12:32:49.177] http://localhost:41000/ZairaPrompts1/oauth/status_wait

[2025-07-19 12:32:49.177] http://localhost:41000/ZairaPrompts1/oauth/status_get[2025-07-19 12:32:49.177] http://localhost:41000/ZairaPrompts1/oauth/status_get

[2025-07-19 12:32:49.178] http://localhost:41000/ZairaPrompts1/oauth/status_get[2025-07-19 12:32:49.178] http://localhost:41000/ZairaPrompts1/oauth/status_get

[2025-07-19 12:32:49.178] http://localhost:41000/ZairaPrompts2/oauth_redirect[2025-07-19 12:32:49.178] http://localhost:41000/ZairaPrompts2/oauth_redirect

[2025-07-19 12:32:49.179] http://localhost:41000/ZairaPrompts2/oauth[2025-07-19 12:32:49.179] http://localhost:41000/ZairaPrompts2/oauth

[2025-07-19 12:32:49.179] http://localhost:41000/ZairaPrompts2/oauth[2025-07-19 12:32:49.179] http://localhost:41000/ZairaPrompts2/oauth

[2025-07-19 12:32:49.180] http://localhost:41000/ZairaPrompts2/oauth/twice[2025-07-19 12:32:49.180] http://localhost:41000/ZairaPrompts2/oauth/twice

[2025-07-19 12:32:49.180] http://localhost:41000/ZairaPrompts2/oauth/twice[2025-07-19 12:32:49.180] http://localhost:41000/ZairaPrompts2/oauth/twice

[2025-07-19 12:32:49.181] http://localhost:41000/ZairaPrompts2/oauth/status_wait[2025-07-19 12:32:49.181] http://localhost:41000/ZairaPrompts2/oauth/status_wait

[2025-07-19 12:32:49.181] http://localhost:41000/ZairaPrompts2/oauth/status_wait[2025-07-19 12:32:49.181] http://localhost:41000/ZairaPrompts2/oauth/status_wait

[2025-07-19 12:32:49.182] http://localhost:41000/ZairaPrompts2/oauth/status_get[2025-07-19 12:32:49.182] http://localhost:41000/ZairaPrompts2/oauth/status_get

[2025-07-19 12:32:49.182] http://localhost:41000/ZairaPrompts2/oauth/status_get[2025-07-19 12:32:49.183] http://localhost:41000/ZairaPrompts2/oauth/status_get

[2025-07-19 12:32:49.183] http://localhost:41000/ZairaPrompts3/oauth_redirect[2025-07-19 12:32:49.183] http://localhost:41000/ZairaPrompts3/oauth_redirect

[2025-07-19 12:32:49.184] http://localhost:41000/ZairaPrompts3/oauth[2025-07-19 12:32:49.184] http://localhost:41000/ZairaPrompts3/oauth

[2025-07-19 12:32:49.184] http://localhost:41000/ZairaPrompts3/oauth[2025-07-19 12:32:49.184] http://localhost:41000/ZairaPrompts3/oauth

[2025-07-19 12:32:49.185] http://localhost:41000/ZairaPrompts3/oauth/twice[2025-07-19 12:32:49.185] http://localhost:41000/ZairaPrompts3/oauth/twice

[2025-07-19 12:32:49.185] http://localhost:41000/ZairaPrompts3/oauth/twice[2025-07-19 12:32:49.185] http://localhost:41000/ZairaPrompts3/oauth/twice

[2025-07-19 12:32:49.186] http://localhost:41000/ZairaPrompts3/oauth/status_wait[2025-07-19 12:32:49.186] http://localhost:41000/ZairaPrompts3/oauth/status_wait

[2025-07-19 12:32:49.187] http://localhost:41000/ZairaPrompts3/oauth/status_wait[2025-07-19 12:32:49.187] http://localhost:41000/ZairaPrompts3/oauth/status_wait

[2025-07-19 12:32:49.187] http://localhost:41000/ZairaPrompts3/oauth/status_get[2025-07-19 12:32:49.187] http://localhost:41000/ZairaPrompts3/oauth/status_get

[2025-07-19 12:32:49.188] http://localhost:41000/ZairaPrompts3/oauth/status_get[2025-07-19 12:32:49.188] http://localhost:41000/ZairaPrompts3/oauth/status_get

[2025-07-19 12:32:49.189] http://localhost:41000/ZairaPrompts4/oauth_redirect[2025-07-19 12:32:49.189] http://localhost:41000/ZairaPrompts4/oauth_redirect

[2025-07-19 12:32:49.191] http://localhost:41000/ZairaPrompts4/oauth[2025-07-19 12:32:49.191] http://localhost:41000/ZairaPrompts4/oauth

[2025-07-19 12:32:49.193] http://localhost:41000/ZairaPrompts4/oauth[2025-07-19 12:32:49.193] http://localhost:41000/ZairaPrompts4/oauth

[2025-07-19 12:32:49.193] http://localhost:41000/ZairaPrompts4/oauth/twice[2025-07-19 12:32:49.193] http://localhost:41000/ZairaPrompts4/oauth/twice

[2025-07-19 12:32:49.194] http://localhost:41000/ZairaPrompts4/oauth/twice[2025-07-19 12:32:49.194] http://localhost:41000/ZairaPrompts4/oauth/twice

[2025-07-19 12:32:49.194] http://localhost:41000/ZairaPrompts4/oauth/status_wait[2025-07-19 12:32:49.195] http://localhost:41000/ZairaPrompts4/oauth/status_wait

[2025-07-19 12:32:49.195] http://localhost:41000/ZairaPrompts4/oauth/status_wait[2025-07-19 12:32:49.195] http://localhost:41000/ZairaPrompts4/oauth/status_wait

[2025-07-19 12:32:49.196] http://localhost:41000/ZairaPrompts4/oauth/status_get[2025-07-19 12:32:49.196] http://localhost:41000/ZairaPrompts4/oauth/status_get

[2025-07-19 12:32:49.197] http://localhost:41000/ZairaPrompts4/oauth/status_get[2025-07-19 12:32:49.197] http://localhost:41000/ZairaPrompts4/oauth/status_get

[2025-07-19 12:32:49.198] Starting Slack Bot Socket Mode client...[2025-07-19 12:32:49.198] Starting Slack Bot Socket Mode client...

[2025-07-19 12:32:49.201] [32m10:32:49.200[0m [][INIT], '_run_once -> _run': Database tables created/verified .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:48.906907+00:00
[2025-07-19 12:32:49.202] [32m10:32:49.200[0m [][INIT], '_run_once -> _run': Database tables created/verified .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:48.906907+00:00
[2025-07-19 12:32:49.264] [32m10:32:49.260[0m [][INIT], '_run_once -> _run': ScheduledTaskPersistenceManager initialized .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:49.202124+00:00
[2025-07-19 12:32:49.264] [32m10:32:49.260[0m [][INIT], '_run_once -> _run': ScheduledTaskPersistenceManager initialized .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:49.202124+00:00
[2025-07-19 12:32:49.280] [32m10:32:49.276[0m [][USER], '_run_once -> _run': User created with GUID 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:49.264471+00:00
[2025-07-19 12:32:49.280] [32m10:32:49.276[0m [][USER], '_run_once -> _run': User created with GUID 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:49.264471+00:00
[2025-07-19 12:32:49.293] [32m10:32:49.290[0m [][USER], '_run_once -> _run': SYSTEM user created successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:49.280436+00:00
[2025-07-19 12:32:49.293] [32m10:32:49.290[0m [][USER], '_run_once -> _run': SYSTEM user created successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:49.280436+00:00
[2025-07-19 12:32:49.302] [32m10:32:49.300[0m [][USER], '_run_once -> _run': SystemUserManager initialized with SYSTEM user .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:49.293403+00:00
[2025-07-19 12:32:49.302] [32m10:32:49.300[0m [][USER], '_run_once -> _run': SystemUserManager initialized with SYSTEM user .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:49.293403+00:00
[2025-07-19 12:32:50.552] INFO:slack_sdk.socket_mode.aiohttp:A new session (s_117743326081) has been established
[2025-07-19 12:32:50.553] INFO:slack_sdk.socket_mode.aiohttp:A new session (s_117743326081) has been established
[2025-07-19 12:32:50.554] A new session (s_117743326081) has been established
[2025-07-19 12:32:50.554] A new session (s_117743326081) has been established
[2025-07-19 12:32:50.564] INFO:managers.manager_users:SYSTEM user GUID requested: 00000000-0000-0000-0000-000000000001
[2025-07-19 12:32:50.564] INFO:managers.manager_users:SYSTEM user GUID requested: 00000000-0000-0000-0000-000000000001
[2025-07-19 12:32:50.565] SYSTEM user GUID requested: 00000000-0000-0000-0000-000000000001
[2025-07-19 12:32:50.565] SYSTEM user GUID requested: 00000000-0000-0000-0000-000000000001
[2025-07-19 12:32:50.565] INFO:managers.manager_users:SYSTEM user found in users dict
[2025-07-19 12:32:50.565] INFO:managers.manager_users:SYSTEM user found in users dict
[2025-07-19 12:32:50.566] SYSTEM user found in users dict
[2025-07-19 12:32:50.566] SYSTEM user found in users dict
[2025-07-19 12:32:50.641] === init() completed ===[2025-07-19 12:32:50.641] === init() completed ===

[2025-07-19 12:32:50.642] INFO:managers.manager_users:Generated new User GUID: f76f418d-a216-44b4-ad26-160035032e68
[2025-07-19 12:32:50.642] INFO:managers.manager_users:Generated new User GUID: f76f418d-a216-44b4-ad26-160035032e68
[2025-07-19 12:32:50.642] Generated new User GUID: f76f418d-a216-44b4-ad26-160035032e68
[2025-07-19 12:32:50.642] Generated new User GUID: f76f418d-a216-44b4-ad26-160035032e68
[2025-07-19 12:32:50.643] INFO:managers.manager_users:Generated new User GUID: 9bb3190b-cdde-4798-a61e-bdecbc7f57f1
[2025-07-19 12:32:50.643] INFO:managers.manager_users:Generated new User GUID: 9bb3190b-cdde-4798-a61e-bdecbc7f57f1
[2025-07-19 12:32:50.643] Generated new User GUID: 9bb3190b-cdde-4798-a61e-bdecbc7f57f1
[2025-07-19 12:32:50.644] Generated new User GUID: 9bb3190b-cdde-4798-a61e-bdecbc7f57f1
[2025-07-19 12:32:50.644] ZairaUser created[2025-07-19 12:32:50.644] ZairaUser created

[2025-07-19 12:32:50.645] INFO:managers.manager_users:User added: Python with GUID: f76f418d-a216-44b4-ad26-160035032e68
[2025-07-19 12:32:50.645] INFO:managers.manager_users:User added: Python with GUID: f76f418d-a216-44b4-ad26-160035032e68
[2025-07-19 12:32:50.646] User added: Python with GUID: f76f418d-a216-44b4-ad26-160035032e68
[2025-07-19 12:32:50.646] User added: Python with GUID: f76f418d-a216-44b4-ad26-160035032e68




















[2025-07-19 12:32:50.671] Please ask your company-specific question: [2025-07-19 12:32:50.671] Please ask your company-specific question: [2025-07-19 12:32:50.725] [32m10:32:50.724[0m [][INIT], '_run_once -> _run': Looking for user with GUID: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.635348+00:00
[2025-07-19 12:32:50.725] [32m10:32:50.724[0m [][INIT], '_run_once -> _run': Looking for user with GUID: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.635348+00:00
[2025-07-19 12:32:50.731] [32m10:32:50.730[0m [][INIT], '_run_once -> _run': User lookup result: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.726561+00:00
[2025-07-19 12:32:50.732] [32m10:32:50.730[0m [][INIT], '_run_once -> _run': User lookup result: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.726561+00:00
[2025-07-19 12:32:50.738] [32m10:32:50.736[0m [][TASK], '_run_once -> _run': Using provided schedule parameters .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.732590+00:00
[2025-07-19 12:32:50.738] [32m10:32:50.736[0m [][TASK], '_run_once -> _run': Using provided schedule parameters .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.732590+00:00
[2025-07-19 12:32:50.745] [32m10:32:50.744[0m [][INIT], '_run_once -> _run': New task started with question of length: 72. .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.738612+00:00
[2025-07-19 12:32:50.745] [32m10:32:50.744[0m [][INIT], '_run_once -> _run': New task started with question of length: 72. .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.738612+00:00
[2025-07-19 12:32:50.752] [32m10:32:50.750[0m [][TASK], '_run_once -> _run': Recreated task 26292d0b-7e78-4f0e-a6e5-f65cc886b9e7 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.745561+00:00
[2025-07-19 12:32:50.752] [32m10:32:50.750[0m [][TASK], '_run_once -> _run': Recreated task 26292d0b-7e78-4f0e-a6e5-f65cc886b9e7 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.745561+00:00
[2025-07-19 12:32:50.759] [32m10:32:50.757[0m [][TASK], '_run_once -> _run': Starting recovered task 26292d0b-7e78-4f0e-a6e5-f65cc886b9e7 in separate thread .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.753561+00:00
[2025-07-19 12:32:50.759] [32m10:32:50.757[0m [][TASK], '_run_once -> _run': Starting recovered task 26292d0b-7e78-4f0e-a6e5-f65cc886b9e7 in separate thread .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.753561+00:00
[2025-07-19 12:32:50.765] [32m10:32:50.764[0m [][TASK], '_run_once -> _run': Started thread for scheduled task 26292d0b-7e78-4f0e-a6e5-f65cc886b9e7 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.759594+00:00
[2025-07-19 12:32:50.765] [32m10:32:50.764[0m [][TASK], '_run_once -> _run': Started thread for scheduled task 26292d0b-7e78-4f0e-a6e5-f65cc886b9e7 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.759594+00:00
[2025-07-19 12:32:50.772] [32m10:32:50.770[0m [][TASK], '_run_once -> _run': Task 26292d0b-7e78-4f0e-a6e5-f65cc886b9e7 started successfully in thread .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.766566+00:00
[2025-07-19 12:32:50.772] [32m10:32:50.770[0m [][TASK], '_run_once -> _run': Task 26292d0b-7e78-4f0e-a6e5-f65cc886b9e7 started successfully in thread .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.766566+00:00
[2025-07-19 12:32:50.778] [32m10:32:50.776[0m [][TASK], '_run_once -> _run': Recovered 1 scheduled tasks, 0 tasks paused waiting for user login .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.772598+00:00
[2025-07-19 12:32:50.778] [32m10:32:50.776[0m [][TASK], '_run_once -> _run': Recovered 1 scheduled tasks, 0 tasks paused waiting for user login .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.772598+00:00
[2025-07-19 12:32:50.784] [32m10:32:50.783[0m [][INIT], '_run_once -> _run': Task recovery completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.778598+00:00
[2025-07-19 12:32:50.784] [32m10:32:50.783[0m [][INIT], '_run_once -> _run': Task recovery completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.778598+00:00
[2025-07-19 12:32:50.790] [32m10:32:50.789[0m [][USER], '_run_once -> _run': Environment detection: Claude=False, Docker=False, Debug=True .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.784591+00:00
[2025-07-19 12:32:50.790] [32m10:32:50.789[0m [][USER], '_run_once -> _run': Environment detection: Claude=False, Docker=False, Debug=True .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.784591+00:00
[2025-07-19 12:32:50.796] [32m10:32:50.795[0m [][WARNING], '_run_once -> _run': Database operation failed: column "run_on_startup" of relation "scheduled_tasks" does not exist .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.791616+00:00
[2025-07-19 12:32:50.796] [32m10:32:50.795[0m [][WARNING], '_run_once -> _run': Database operation failed: column "run_on_startup" of relation "scheduled_tasks" does not exist .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.791616+00:00
[2025-07-19 12:32:50.802] [32m10:32:50.801[0m [][ERROR], '_run_once -> _run': Failed to save scheduled task 26292d0b-7e78-4f0e-a6e5-f65cc886..."run_on_startup" of relation "scheduled_tasks" does not exist .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.796603+00:00
[2025-07-19 12:32:50.802] [32m10:32:50.801[0m [][ERROR], '_run_once -> _run': Failed to save scheduled task 26292d0b-7e78-4f0e-a6e5-f65cc886..."run_on_startup" of relation "scheduled_tasks" does not exist .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.796603+00:00
[2025-07-19 12:32:50.808] [32m10:32:50.807[0m [][USER], '_run_once -> _run': Monday 9am good luck message task already exists .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.802571+00:00
[2025-07-19 12:32:50.808] [32m10:32:50.807[0m [][USER], '_run_once -> _run': Monday 9am good luck message task already exists .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.802571+00:00
[2025-07-19 12:32:50.815] [32m10:32:50.814[0m [][USER], '_run_once -> _run': Additional environment-specific tasks not created for: Claude=False, Docker=False, Debug=True .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.809591+00:00
[2025-07-19 12:32:50.815] [32m10:32:50.814[0m [][USER], '_run_once -> _run': Additional environment-specific tasks not created for: Claude=False, Docker=False, Debug=True .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.809591+00:00
[2025-07-19 12:32:50.821] [32m10:32:50.820[0m [][USER], '_run_once -> _run': Environment-specific scheduled tasks created .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.815565+00:00
[2025-07-19 12:32:50.821] [32m10:32:50.820[0m [][USER], '_run_once -> _run': Environment-specific scheduled tasks created .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.815565+00:00
[2025-07-19 12:32:50.827] [32m10:32:50.826[0m [][INIT], '_run_once -> _run': Setup has completed. .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.821563+00:00
[2025-07-19 12:32:50.827] [32m10:32:50.826[0m [][INIT], '_run_once -> _run': Setup has completed. .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.821563+00:00
[2025-07-19 12:32:50.834] [32m10:32:50.832[0m [][USER], '_run_once -> _run': User created with GUID f76f418d-a216-44b4-ad26-160035032e68 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.827800+00:00
[2025-07-19 12:32:50.834] [32m10:32:50.832[0m [][USER], '_run_once -> _run': User created with GUID f76f418d-a216-44b4-ad26-160035032e68 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:50.827800+00:00
[2025-07-19 12:32:55.619] [32m10:32:55.617[0m [][TASK], '_run_once -> _run': Thread started for scheduled task 26292d0b-7e78-4f0e-a6e5-f65cc886b9e7 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:55.561647+00:00
[2025-07-19 12:32:55.619] [32m10:32:55.617[0m [][TASK], '_run_once -> _run': Thread started for scheduled task 26292d0b-7e78-4f0e-a6e5-f65cc886b9e7 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:55.561647+00:00
[2025-07-19 12:32:55.626] [32m10:32:55.625[0m [][TASK], '_run_once -> _run': Waiting 371284.74687 seconds until next execution at 2025-07-23 17:40:56.520208+00:00 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:55.619036+00:00
[2025-07-19 12:32:55.627] [32m10:32:55.625[0m [][TASK], '_run_once -> _run': Waiting 371284.74687 seconds until next execution at 2025-07-23 17:40:56.520208+00:00 .  Metadata: {"chat length#":-1}. User None on session None at 2025-07-19 10:32:55.619036+00:00
[2025-07-19 12:33:11.861] Endpoint dashboard called from IP: 127.0.0.1[2025-07-19 12:33:11.861] Endpoint dashboard called from IP: 127.0.0.1

[2025-07-19 12:33:11.869] Traceback (most recent call last):
[2025-07-19 12:33:11.869] Traceback (most recent call last):
[2025-07-19 12:33:11.871]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\botbuilder\core\integration\aiohttp_channel_service_exception_middleware.py", line 21, in aiohttp_error_middleware
[2025-07-19 12:33:11.871]     response = await handler(request)
[2025-07-19 12:33:11.871]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.871]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\botbuilder\core\integration\aiohttp_channel_service_exception_middleware.py", line 21, in aiohttp_error_middleware
[2025-07-19 12:33:11.871]     response = await handler(request)
[2025-07-19 12:33:11.871]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.872]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 28, in logfire_middleware
[2025-07-19 12:33:11.872]     response = await LogFire.logfire_middleware(request, handler)
[2025-07-19 12:33:11.872]                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.872]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 28, in logfire_middleware
[2025-07-19 12:33:11.872]     response = await LogFire.logfire_middleware(request, handler)
[2025-07-19 12:33:11.872]                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.873]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_logfire.py", line 100, in logfire_middleware
[2025-07-19 12:33:11.873]     response = await handler(request)
[2025-07-19 12:33:11.873]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.874]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_logfire.py", line 100, in logfire_middleware
[2025-07-19 12:33:11.874]     response = await handler(request)
[2025-07-19 12:33:11.874]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.875]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 37, in ip_check_middleware
[2025-07-19 12:33:11.875]     response = await handler(request)
[2025-07-19 12:33:11.875]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.875]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 37, in ip_check_middleware
[2025-07-19 12:33:11.875]     response = await handler(request)
[2025-07-19 12:33:11.875]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.876]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 465, in login
[2025-07-19 12:33:11.876]     site = etc.helper_functions.create_html_out("login", content)
[2025-07-19 12:33:11.876]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.876]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 465, in login
[2025-07-19 12:33:11.876]     site = etc.helper_functions.create_html_out("login", content)
[2025-07-19 12:33:11.876]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.877]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py", line 196, in create_html_out
[2025-07-19 12:33:11.877]     with open(os_path.join(ui_folder, page_name + "_head.txt"), "r", encoding="utf-8") as f:
[2025-07-19 12:33:11.877]          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.877]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py", line 196, in create_html_out
[2025-07-19 12:33:11.877]     with open(os_path.join(ui_folder, page_name + "_head.txt"), "r", encoding="utf-8") as f:
[2025-07-19 12:33:11.877]          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:11.878] FileNotFoundError: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Documents\\AgenticRag\\Python\\dev\\AgenticRAG\\src\\AgenticRAG/src/AgenticRAG/ui/login_head.txt'
[2025-07-19 12:33:11.878] FileNotFoundError: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Documents\\AgenticRag\\Python\\dev\\AgenticRAG\\src\\AgenticRAG/src/AgenticRAG/ui/login_head.txt'
[2025-07-19 12:33:11.880] INFO:aiohttp.access:127.0.0.1 [19/Jul/2025:11:33:11 +0100] "GET /login HTTP/1.1" 500 199 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-19 12:33:11.881] INFO:aiohttp.access:127.0.0.1 [19/Jul/2025:11:33:11 +0100] "GET /login HTTP/1.1" 500 199 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-19 12:33:11.881] 127.0.0.1 [19/Jul/2025:11:33:11 +0100] "GET /login HTTP/1.1" 500 199 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-19 12:33:11.881] 127.0.0.1 [19/Jul/2025:11:33:11 +0100] "GET /login HTTP/1.1" 500 199 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-19 12:33:12.132] Endpoint dashboard called from IP: 127.0.0.1[2025-07-19 12:33:12.132] Endpoint dashboard called from IP: 127.0.0.1

[2025-07-19 12:33:12.134] Traceback (most recent call last):
[2025-07-19 12:33:12.134] Traceback (most recent call last):
[2025-07-19 12:33:12.135]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\botbuilder\core\integration\aiohttp_channel_service_exception_middleware.py", line 21, in aiohttp_error_middleware
[2025-07-19 12:33:12.135]     response = await handler(request)
[2025-07-19 12:33:12.135]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.135]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\botbuilder\core\integration\aiohttp_channel_service_exception_middleware.py", line 21, in aiohttp_error_middleware
[2025-07-19 12:33:12.135]     response = await handler(request)
[2025-07-19 12:33:12.135]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.136]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 28, in logfire_middleware
[2025-07-19 12:33:12.136]     response = await LogFire.logfire_middleware(request, handler)
[2025-07-19 12:33:12.136]                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.136]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 28, in logfire_middleware
[2025-07-19 12:33:12.136]     response = await LogFire.logfire_middleware(request, handler)
[2025-07-19 12:33:12.136]                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.136]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_logfire.py", line 100, in logfire_middleware
[2025-07-19 12:33:12.136]     response = await handler(request)
[2025-07-19 12:33:12.136]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.136]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_logfire.py", line 100, in logfire_middleware
[2025-07-19 12:33:12.136]     response = await handler(request)
[2025-07-19 12:33:12.136]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.137]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 37, in ip_check_middleware
[2025-07-19 12:33:12.137]     response = await handler(request)
[2025-07-19 12:33:12.137]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.137]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 37, in ip_check_middleware
[2025-07-19 12:33:12.137]     response = await handler(request)
[2025-07-19 12:33:12.137]                ^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.137]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 465, in login
[2025-07-19 12:33:12.137]     site = etc.helper_functions.create_html_out("login", content)
[2025-07-19 12:33:12.137]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.138]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 465, in login
[2025-07-19 12:33:12.138]     site = etc.helper_functions.create_html_out("login", content)
[2025-07-19 12:33:12.138]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.138]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py", line 196, in create_html_out
[2025-07-19 12:33:12.138]     with open(os_path.join(ui_folder, page_name + "_head.txt"), "r", encoding="utf-8") as f:
[2025-07-19 12:33:12.138]          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.138]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py", line 196, in create_html_out
[2025-07-19 12:33:12.138]     with open(os_path.join(ui_folder, page_name + "_head.txt"), "r", encoding="utf-8") as f:
[2025-07-19 12:33:12.138]          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-19 12:33:12.139] FileNotFoundError: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Documents\\AgenticRag\\Python\\dev\\AgenticRAG\\src\\AgenticRAG/src/AgenticRAG/ui/login_head.txt'
[2025-07-19 12:33:12.139] FileNotFoundError: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Documents\\AgenticRag\\Python\\dev\\AgenticRAG\\src\\AgenticRAG/src/AgenticRAG/ui/login_head.txt'
[2025-07-19 12:33:12.140] INFO:aiohttp.access:127.0.0.1 [19/Jul/2025:11:33:12 +0100] "GET /login HTTP/1.1" 500 199 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-19 12:33:12.141] INFO:aiohttp.access:127.0.0.1 [19/Jul/2025:11:33:12 +0100] "GET /login HTTP/1.1" 500 199 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-19 12:33:12.141] 127.0.0.1 [19/Jul/2025:11:33:12 +0100] "GET /login HTTP/1.1" 500 199 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-19 12:33:12.141] 127.0.0.1 [19/Jul/2025:11:33:12 +0100] "GET /login HTTP/1.1" 500 199 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
[2025-07-19 12:34:00.649] INFO:slack_sdk.socket_mode.aiohttp:The session (s_117743326081) seems to be already closed. Reconnecting...
[2025-07-19 12:34:00.649] INFO:slack_sdk.socket_mode.aiohttp:The session (s_117743326081) seems to be already closed. Reconnecting...
[2025-07-19 12:34:00.650] The session (s_117743326081) seems to be already closed. Reconnecting...
[2025-07-19 12:34:00.651] The session (s_117743326081) seems to be already closed. Reconnecting...
[2025-07-19 12:34:01.334] INFO:slack_sdk.socket_mode.aiohttp:The old session (s_117743326081) has been abandoned
[2025-07-19 12:34:01.334] INFO:slack_sdk.socket_mode.aiohttp:The old session (s_117743326081) has been abandoned
[2025-07-19 12:34:01.334] The old session (s_117743326081) has been abandoned
[2025-07-19 12:34:01.335] The old session (s_117743326081) has been abandoned
[2025-07-19 12:34:02.048] INFO:slack_sdk.socket_mode.aiohttp:A new session (s_117600767529) has been established
[2025-07-19 12:34:02.048] INFO:slack_sdk.socket_mode.aiohttp:A new session (s_117600767529) has been established
[2025-07-19 12:34:02.049] A new session (s_117600767529) has been established
[2025-07-19 12:34:02.049] A new session (s_117600767529) has been established
