=== DEBUG TRACE STARTED at 2025-08-16 15:09:25.522257 ===
[2025-08-16 15:09:50.556] WARNING:discord.client:PyNaCl is not installed, voice will NOT be supported
[2025-08-16 15:09:53.073] LogEntries database table created/verified
[2025-08-16 15:09:53.142] Logfire project URL: ]8;id=921482;https://logfire-eu.pydantic.dev/askzaira/agentic-rag\https://logfire-eu.pydantic.dev/askzaira/agentic-rag]8;;\
[2025-08-16 15:09:53.918] === mainFunc() started ===
[2025-08-16 15:09:53.918] No Claude environment detected
[2025-08-16 15:09:53.919] === About to set up data directories ===
[2025-08-16 15:09:53.919] Using data subfolder: AskZaira
[2025-08-16 15:09:53.920] Data_dir: C:\Users\<USER>\Documents\AgenticRag\Python\dev\_DATA_RAW\AskZaira
[2025-08-16 15:09:53.920] Persist dir: C:\Users\<USER>\Documents\AgenticRag\Python\dev\_DATA_EMBEDDED\AskZaira
[2025-08-16 15:09:53.937] === About to call init() ===
[2025-08-16 15:09:57.331] Database 'vectordb' already exists.
[2025-08-16 15:09:57.504] 13:09:57.501 [][INIT], '_run_once -> _run': dev_run.py main() started .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:53.914265
[2025-08-16 15:09:57.542] 13:09:57.540 [Python][INIT], '_run_once -> _run': About to check Claude environment .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:57.505874
[2025-08-16 15:09:57.613] 13:09:57.611 [Python][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:57.542873
[2025-08-16 15:09:57.620] 13:09:57.618 [Python][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:57.614027
[2025-08-16 15:09:57.667] 13:09:57.666 [Python][INIT], '_run_once -> _run': About to call mainFunc() .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:57.620025
[2025-08-16 15:09:59.561] 13:09:59.559 [Python][INIT], '_run_once -> _run': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:57.668027
[2025-08-16 15:09:59.569] 13:09:59.567 [Python][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:59.561330
[2025-08-16 15:09:59.576] 13:09:59.574 [Python][INIT], '_run_once -> _run': Discord bot setup called .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:59.569329
[2025-08-16 15:09:59.589] 13:09:59.587 [Python][INIT], '_run_once -> _run': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:59.576330
[2025-08-16 15:09:59.659] 13:09:59.657 [Python][WARNING], '_run_once -> _run': Database operation failed: column "execution_count" does not exist .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:59.653555
[2025-08-16 15:09:59.665] 13:09:59.663 [Python][WARNING], '_run_once -> _run': Failed to create index idx_scheduled_requests_performance: column "execution_count" does not exist .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:09:59.659555
[2025-08-16 15:09:59.673] INFO:managers.manager_users:Creating a new singleton instance of ZairaUserManager.
[2025-08-16 15:09:59.673] Creating a new singleton instance of ZairaUserManager.
[2025-08-16 15:09:59.675] INFO:managers.manager_users:Pydantic models rebuilt successfully to resolve forward references
[2025-08-16 15:09:59.675] Pydantic models rebuilt successfully to resolve forward references
[2025-08-16 15:09:59.677] INFO:managers.manager_users:User added: SYSTEM with GUID: 00000000-0000-0000-0000-000000000001
[2025-08-16 15:09:59.677] User added: SYSTEM with GUID: 00000000-0000-0000-0000-000000000001
[2025-08-16 15:09:59.681] WARNING:llama_index.vector_stores.qdrant.base:Both client and aclient are provided. If using `:memory:` mode, the data between clients is not synced.
[2025-08-16 15:09:59.682] Both client and aclient are provided. If using `:memory:` mode, the data between clients is not synced.
[2025-08-16 15:10:00.915] BertForMaskedLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
[2025-08-16 15:10:00.915]   - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
[2025-08-16 15:10:00.915]   - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
[2025-08-16 15:10:00.915]   - If you are not the owner of the model architecture class, please contact the model code owner to update it.
[2025-08-16 15:10:02.714] WARNING:root:'doc_id' is deprecated and 'id_' will be used instead
[2025-08-16 15:10:02.714] 'doc_id' is deprecated and 'id_' will be used instead
[2025-08-16 15:10:04.301] INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
[2025-08-16 15:10:04.302] file_cache is only supported with oauth2client<4.0.0
[2025-08-16 15:10:04.419] 13:10:04.417 [Python][INIT], '_run_once -> _run': Database tables and indexes created/verified .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:10:04.289522
[2025-08-16 15:10:04.426] 13:10:04.423 [Python][INIT], '_run_once -> _run': Setting up new scheduled request architecture .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:10:04.419492
[2025-08-16 15:10:04.432] 13:10:04.431 [Python][INIT], '_run_once -> _run': ScheduledRequestIntegrationAdapter initialized successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:10:04.426491
[2025-08-16 15:10:04.440] 13:10:04.438 [Python][INIT], '_run_once -> _run': New scheduled request system ready .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:10:04.433809
[2025-08-16 15:10:04.475] 13:10:04.473 [Python][INIT], '_run_once -> _run': ZairaUser created: SYSTEM .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:10:04.468496
[2025-08-16 15:10:04.482] 13:10:04.480 [Python][USER], '_run_once -> _run': User created with GUID 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 04ac3f95-89a7-48d6-a74b-799f1065f694 at 2025-08-16 13:10:04.475492
[2025-08-16 15:10:04.517] 13:10:04.515 [Python][USER], '_run_once -> _run': SYSTEM user created successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:10:04.510515
[2025-08-16 15:10:04.530] 13:10:04.528 [Python][USER], '_run_once -> _run': SystemUserManager initialized with SYSTEM user .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:10:04.524548
[2025-08-16 15:10:04.535] 13:10:04.534 [Python][EVENT], '_run_once -> _run': Loading stored index .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:10:04.530150
[2025-08-16 15:10:04.542] 13:10:04.540 [Python][EVENT], '_run_once -> _run': Index loaded .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 13:10:04.536995
