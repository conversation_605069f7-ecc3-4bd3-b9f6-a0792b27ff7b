"""
Performance tests for WhatsApp bot functionality
"""
import pytest
import asyncio
import time
import psutil
import os
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.performance
@pytest.mark.asyncio
class TestWhatsAppPerformance:
    """Performance tests for WhatsApp bot"""
    
    async def test_message_processing_latency(self, mock_external_services):
        """Test message processing latency"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup fast mock user
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            # Warm up
            await MyWhatsappBot.on_message("warmup", "31611239487", "31611239487")
            
            # Measure latency for single message
            start_time = time.time()
            await MyWhatsappBot.on_message(
                "Performance test message",
                "31611239487",
                "31611239487"
            )
            end_time = time.time()
            
            latency = end_time - start_time
            
            # Should process message in under 100ms
            assert latency < 0.1, f"Message processing took {latency:.3f}s, expected < 0.1s"

    async def test_concurrent_message_throughput(self, mock_external_services):
        """Test concurrent message processing throughput"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            # Test concurrent processing
            num_messages = 50
            start_time = time.time()
            
            tasks = []
            for i in range(num_messages):
                task = asyncio.create_task(
                    MyWhatsappBot.on_message(
                        f"Concurrent message {i}",
                        f"3161123{i:04d}",
                        f"3161123{i:04d}"
                    )
                )
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            end_time = time.time()
            
            total_time = end_time - start_time
            throughput = num_messages / total_time
            
            # Should process at least 25 messages per second
            assert throughput >= 25, f"Throughput was {throughput:.1f} msg/s, expected >= 25 msg/s"
            
            # Verify all messages were processed
            assert mock_user.on_message.call_count == num_messages

    async def test_webhook_processing_performance(self, mock_external_services, sample_whatsapp_webhook_data):
        """Test webhook processing performance"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user, \
             patch.object(ZairaUserManager, 'add_user') as mock_add_user:
            
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = None
            mock_add_user.return_value = mock_user
            
            bot = MyWhatsappBot()
            
            # Test multiple webhook processing
            num_webhooks = 20
            start_time = time.time()
            
            tasks = []
            for i in range(num_webhooks):
                # Create unique webhook data
                webhook_data = sample_whatsapp_webhook_data.copy()
                webhook_data["entry"][0]["changes"][0]["value"]["messages"][0]["from"] = f"3161123{i:04d}"
                webhook_data["entry"][0]["changes"][0]["value"]["messages"][0]["id"] = f"wamid.test{i}"
                
                mock_request = AsyncMock()
                mock_request.content_type = 'application/json'
                mock_request.json.return_value = webhook_data
                
                task = asyncio.create_task(bot.whatsapp_webhook(mock_request))
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks)
            end_time = time.time()
            
            total_time = end_time - start_time
            throughput = num_webhooks / total_time
            
            # Should process at least 10 webhooks per second
            assert throughput >= 10, f"Webhook throughput was {throughput:.1f} req/s, expected >= 10 req/s"
            
            # All responses should be successful
            for response in responses:
                assert response.status == 200

    async def test_memory_usage_under_load(self, mock_external_services):
        """Test memory usage under sustained load"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            # Measure initial memory
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss
            
            # Process many messages in batches
            batch_size = 20
            num_batches = 5
            
            for batch in range(num_batches):
                tasks = []
                for i in range(batch_size):
                    task = asyncio.create_task(
                        MyWhatsappBot.on_message(
                            f"Memory test batch {batch} message {i}",
                            f"31611{batch:02d}{i:03d}",
                            f"31611{batch:02d}{i:03d}"
                        )
                    )
                    tasks.append(task)
                
                await asyncio.gather(*tasks)
                
                # Small delay between batches
                await asyncio.sleep(0.1)
            
            # Measure final memory
            final_memory = process.memory_info().rss
            memory_increase = final_memory - initial_memory
            memory_increase_mb = memory_increase / (1024 * 1024)
            
            # Memory increase should be reasonable (< 100MB)
            assert memory_increase_mb < 100, f"Memory increased by {memory_increase_mb:.1f}MB, expected < 100MB"

    async def test_api_call_performance(self, mock_whatsapp_api):
        """Test WhatsApp API call performance"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Test single API call latency
        start_time = time.time()
        result = await MyWhatsappBot.send_a_whatsapp_message(
            "31611239487",
            "Performance test message"
        )
        end_time = time.time()
        
        latency = end_time - start_time
        
        # API call should complete quickly
        assert latency < 0.5, f"API call took {latency:.3f}s, expected < 0.5s"
        assert result is True

    async def test_bulk_message_sending_performance(self, mock_whatsapp_api):
        """Test bulk message sending performance"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Test sending multiple messages
        num_messages = 10
        start_time = time.time()
        
        tasks = []
        for i in range(num_messages):
            task = asyncio.create_task(
                MyWhatsappBot.send_a_whatsapp_message(
                    f"3161123{i:04d}",
                    f"Bulk test message {i}"
                )
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_time = end_time - start_time
        throughput = num_messages / total_time
        
        # Should send at least 5 messages per second
        assert throughput >= 5, f"Send throughput was {throughput:.1f} msg/s, expected >= 5 msg/s"
        
        # All sends should succeed
        assert all(results), "All message sends should succeed"

@pytest.mark.performance
@pytest.mark.asyncio
class TestWhatsAppScalability:
    """Scalability tests for WhatsApp bot"""
    
    async def test_user_scaling(self, mock_external_services):
        """Test handling of many concurrent users"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Create mock users for different phone numbers
        user_cache = {}
        
        def get_mock_user(phone_number):
            if phone_number not in user_cache:
                mock_user = AsyncMock()
                mock_user.on_message = AsyncMock()
                user_cache[phone_number] = mock_user
            return user_cache[phone_number]
        
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_get_user.side_effect = lambda phone: get_mock_user(phone)
            
            # Simulate many users sending messages
            num_users = 100
            start_time = time.time()
            
            tasks = []
            for i in range(num_users):
                phone_number = f"31611{i:06d}"
                task = asyncio.create_task(
                    MyWhatsappBot.on_message(
                        f"Message from user {i}",
                        phone_number,
                        phone_number
                    )
                )
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            # Should handle 100 users in reasonable time
            assert total_time < 5.0, f"Processing {num_users} users took {total_time:.1f}s, expected < 5s"
            
            # Verify all users were processed
            assert len(user_cache) == num_users

    async def test_message_queue_performance(self, mock_external_services):
        """Test performance with message queuing"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user with slow processing
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            
            async def slow_message_processing(*args, **kwargs):
                await asyncio.sleep(0.1)  # Simulate slow processing
            
            mock_user.on_message = slow_message_processing
            mock_get_user.return_value = mock_user
            
            # Send messages rapidly
            num_messages = 20
            start_time = time.time()
            
            # Send all messages without waiting
            tasks = []
            for i in range(num_messages):
                task = asyncio.create_task(
                    MyWhatsappBot.on_message(
                        f"Queued message {i}",
                        "31611239487",
                        "31611239487"
                    )
                )
                tasks.append(task)
            
            # Wait for all to complete
            await asyncio.gather(*tasks)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            # Should handle queuing efficiently
            # With 0.1s per message and good concurrency, should be much less than 20 * 0.1 = 2s
            assert total_time < 1.5, f"Queued processing took {total_time:.1f}s, expected < 1.5s"

@pytest.mark.performance
@pytest.mark.asyncio
class TestWhatsAppResourceUsage:
    """Resource usage tests for WhatsApp bot"""
    
    async def test_cpu_usage_under_load(self, mock_external_services):
        """Test CPU usage under sustained load"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Setup mock user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_get_user.return_value = mock_user
            
            # Monitor CPU usage
            process = psutil.Process(os.getpid())
            
            # Baseline CPU measurement
            process.cpu_percent()  # First call to initialize
            await asyncio.sleep(0.1)
            baseline_cpu = process.cpu_percent()
            
            # Generate load
            tasks = []
            for i in range(50):
                task = asyncio.create_task(
                    MyWhatsappBot.on_message(
                        f"CPU test message {i}",
                        f"3161123{i:04d}",
                        f"3161123{i:04d}"
                    )
                )
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            
            # Measure CPU after load
            await asyncio.sleep(0.1)
            load_cpu = process.cpu_percent()
            
            # CPU usage should be reasonable (not maxed out)
            assert load_cpu < 80, f"CPU usage was {load_cpu}%, expected < 80%"

    async def test_connection_pooling_efficiency(self, mock_whatsapp_api):
        """Test HTTP connection pooling efficiency"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Test multiple API calls to verify connection reuse
        start_time = time.time()
        
        tasks = []
        for i in range(20):
            task = asyncio.create_task(
                MyWhatsappBot.send_a_whatsapp_message(
                    f"3161123{i:04d}",
                    f"Connection test {i}"
                )
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_time_per_call = total_time / 20
        
        # With connection pooling, average time per call should be low
        assert avg_time_per_call < 0.1, f"Average API call time was {avg_time_per_call:.3f}s, expected < 0.1s"
        assert all(results), "All API calls should succeed"
