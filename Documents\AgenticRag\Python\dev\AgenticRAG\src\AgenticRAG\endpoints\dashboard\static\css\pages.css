/* Dashboard Pages CSS - Profile, Account, System, Subscription */

/* Common Page Container Styles */
.profile-container,
.account-container,
.system-container,
.subscription-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

/* Page Headers */
.profile-header,
.account-header,
.system-header,
.subscription-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Avatar Styles */
.profile-avatar,
.subscription-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    font-weight: 600;
    box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.avatar-initials {
    text-transform: uppercase;
}

/* Page Titles */
.profile-name,
.account-name,
.system-title,
.subscription-title {
    background: linear-gradient(135deg, #60a5fa, #a78bfa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.profile-subtitle,
.account-subtitle,
.system-subtitle,
.subscription-subtitle {
    color: #94a3b8;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
    margin-top: 1rem;
}

.status-badge.active {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-badge.inactive {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.status-badge.system {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

/* Stats Grid */
.profile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    border: 1px solid rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 12px 24px rgba(59, 130, 246, 0.2);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #60a5fa;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #94a3b8;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Form Styles */
.profile-form,
.settings-card,
.billing-card,
.plan-card,
.invoices-card,
.account-management-card,
.config-card,
.components-card,
.data-management-card {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.card-title {
    color: #93c5fd;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: #cbd5e1;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    color: #f8fafc;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

textarea.form-input {
    resize: vertical;
    min-height: 100px;
}

.form-help {
    color: #64748b;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: block;
}

.form-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.form-checkbox input[type="checkbox"] {
    margin-right: 0.75rem;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.form-checkbox-label {
    color: #cbd5e1;
    font-size: 0.95rem;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(59, 130, 246, 0.1);
}

.form-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    color: white;
    box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: rgba(71, 85, 105, 0.8);
    color: #e2e8f0;
    border: 1px solid rgba(71, 85, 105, 0.5);
}

.btn-secondary:hover {
    background: rgba(100, 116, 139, 0.8);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    box-shadow: 0 4px 6px rgba(220, 38, 38, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(220, 38, 38, 0.4);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-link {
    background: none;
    color: #60a5fa;
    text-decoration: underline;
    padding: 0;
}

.btn-link:hover {
    color: #93c5fd;
}

/* Account Settings Grid */
.account-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

/* System Metrics Grid */
.system-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2rem;
}

/* Metric Stats */
.metric-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    text-align: center;
}

/* Progress Bar */
.progress-bar {
    background: rgba(15, 23, 42, 0.8);
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #6366f1);
    transition: width 0.5s ease;
}

/* Component Stats */
.component-stats {
    margin-bottom: 1.5rem;
}

.component-row {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 6px;
    margin-bottom: 0.5rem;
}

.component-row span:first-child {
    color: #cbd5e1;
}

.component-row span:last-child {
    color: #60a5fa;
    font-weight: 600;
}

/* Security Actions */
.security-actions,
.component-actions,
.data-actions,
.management-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-item {
    padding: 1rem;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.action-item h4 {
    color: #93c5fd;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.action-item p {
    color: #94a3b8;
    font-size: 0.85rem;
    margin-bottom: 1rem;
}

.action-item.danger {
    border-color: rgba(220, 38, 38, 0.2);
}

.action-item.danger h4 {
    color: #fca5a5;
}

/* Subscription Grid */
.subscription-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2rem;
}

/* Plan Details */
.plan-details {
    text-align: center;
}

.plan-name {
    font-size: 1.5rem;
    color: #60a5fa;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.plan-price {
    font-size: 2.5rem;
    color: #f8fafc;
    font-weight: 700;
    margin-bottom: 1rem;
}

.plan-period {
    font-size: 1rem;
    color: #94a3b8;
    font-weight: 400;
}

.plan-features {
    margin: 1.5rem 0;
}

.feature-item {
    padding: 0.5rem;
    color: #cbd5e1;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.plan-actions {
    margin-top: 1.5rem;
}

/* Billing Details */
.billing-details {
    margin-bottom: 1rem;
}

.billing-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.billing-label {
    color: #94a3b8;
    font-size: 0.9rem;
}

.billing-value {
    color: #f8fafc;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Invoices */
.invoices-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.invoice-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    align-items: center;
    padding: 0.75rem;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 6px;
    margin-bottom: 0.5rem;
}

.invoice-date {
    color: #cbd5e1;
}

.invoice-amount {
    color: #60a5fa;
    font-weight: 600;
}

.invoice-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.invoice-status.paid {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.invoice-status.unpaid {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.invoice-actions {
    text-align: right;
}

.invoices-actions {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(59, 130, 246, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .account-settings-grid,
    .system-metrics-grid,
    .subscription-grid {
        grid-template-columns: 1fr;
    }
    
    .metric-stats {
        grid-template-columns: 1fr;
    }
    
    .invoice-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
}