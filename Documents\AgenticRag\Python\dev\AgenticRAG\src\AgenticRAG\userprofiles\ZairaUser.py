from imports import *

from uuid import uuid4, UUID
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

from userprofiles.permission_levels import PERMISSION_LEVELS
from pydantic import BaseModel, Field, ConfigDict
from unstructured.partition.auto import partition
from langchain_core.messages import HumanMessage, AnyMessage
    
from endpoints.mybot_generic import MyBot_Generic
from userprofiles.ZairaMessage import ZairaMessage
from managers.manager_multimodal import MultimodalManager

class ZairaUser(BaseModel):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.LongRunningZairaTask import LongRunningZairaTask

    username: str = ""
    rank: PERMISSION_LEVELS = PERMISSION_LEVELS.NONE
    real_name: str = ""
    email: str = ""
    platform: str = ""  # Platform identifier (Discord, Teams, etc.)
    is_system_user: bool = False  # Flag to identify system users

    # Internal
    GUID: UUID = Field(None, exclude=True)
    DeviceGUID: UUID = Field(None, exclude=True)
    sessionGUID: UUID = Field(None, exclude=True)
    conversationGUID: UUID = Field(None, exclude=True)
    my_task: "LongRunningZairaTask" = None
    asyncio_Task: asyncio.Task = Field(None, exclude=True)
    chat_history: dict[UUID, list[ZairaMessage]] = {}
    
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, username: str, rank: PERMISSION_LEVELS, guid: UUID, device_guid: UUID):
        super().__init__()
        self.username = username
        self.rank = rank
        self.GUID = guid
        self.DeviceGUID = device_guid
        self.sessionGUID = uuid4()
        self.chat_history[self.sessionGUID] = []
        self.conversationGUID = uuid4()
        self.my_task = None
        self.platform = ""
        self.is_system_user = False
        print("ZairaUser created")
        LogFire.log("User", f"User created with GUID {self.GUID}", f". Username: {self.username}, rank: {self.rank}")
    
    @property
    def user_guid(self) -> str:
        """Get user GUID as string for compatibility"""
        return str(self.GUID)
    
    def getAvailableVectorStores(self):
        match self.rank:
            case PERMISSION_LEVELS.NONE:
                return None
            case PERMISSION_LEVELS.GUEST:
                return None
            case PERMISSION_LEVELS.USER:
                return ["user_vectors"]  # Example return value
            case PERMISSION_LEVELS.ADMIN:
                return ["user_vectors", "admin_vectors"]  # Example return value
            case _:
                return None  # Default case

    async def on_message(self, complete_message: str, calling_bot: MyBot_Generic, attachments: list = [], original_message = None):
        # Enhanced multimodal processing for attachments
        attachment_context = ""
        
        if len(attachments) > 0:
            attachment_context = await self._process_attachments_multimodal(attachments)
            
            # Add processed attachment context to the message
            if attachment_context:
                complete_message += f"\n\nAttachment Analysis:\n{attachment_context}"
        
        # Check if this is a human-in-the-loop response
        is_hitl_response = self.my_task is not None and self.my_task.human_in_the_loop_callback is not None
        
        # Only add to chat history if it's not a human-in-the-loop response and message is not empty
        if not is_hitl_response and complete_message and complete_message.strip():
            self.chat_history[self.sessionGUID].append(ZairaMessage.create_user_message(complete_message, self.conversationGUID, self.sessionGUID))
        
        if self.my_task == None:
            await self.start_task(complete_message=complete_message, calling_bot=calling_bot, original_message=original_message)
        else: 
            await self.my_task.on_message(complete_message=complete_message, calling_bot=calling_bot, original_message=original_message)
    
    async def _process_attachments_multimodal(self, attachments: List[str]) -> str:
        """
        Process attachments with multimodal AI capabilities.
        Detects images and processes them using vision models.
        """
        try:
            processed_content = []
            
            for attachment_path in attachments:
                if await self._is_image_file(attachment_path):
                    # Process image with multimodal AI
                    image_analysis = await self._process_image_attachment(attachment_path)
                    processed_content.append(image_analysis)
                else:
                    # Process non-image files with existing unstructured approach
                    try:
                        document_content = partition(filename=attachment_path)
                        if document_content:
                            processed_content.append(f"**Document: {Path(attachment_path).name}**\n{document_content}")
                    except Exception as e:
                        from etc.helper_functions import exception_triggered
                        exception_triggered(e, f"Processing non-image attachment: {attachment_path}", self.GUID)
                        processed_content.append(f"**File: {Path(attachment_path).name}** - Could not process content")
            
            return "\n\n".join(processed_content)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Processing attachments with multimodal AI", self.GUID)
            return "Error processing attachments"
    
    async def _is_image_file(self, file_path: str) -> bool:
        """
        Check if a file is an image based on its extension.
        """
        try:
            image_extensions = {".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".webp"}
            file_extension = Path(file_path).suffix.lower()
            return file_extension in image_extensions
        except Exception:
            return False
    
    async def _process_image_attachment(self, image_path: str) -> str:
        """
        Process an image attachment using the MultimodalManager.
        """
        try:
            # Generate a unique document ID for this image
            doc_guid = str(uuid4())
            
            # Use MultimodalManager to generate image summary
            image_summary = await MultimodalManager.generate_image_summary(
                image_path=image_path,
                context=f"Image sent by user {self.username} in Discord chat"
            )
            
            # Format the response
            filename = Path(image_path).name
            return f"**Image: {filename}**\n{image_summary}"
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, f"Processing image attachment: {image_path}", self.GUID)
            return f"**Image: {Path(image_path).name}** - Could not analyze image content"

    async def start_task(self, complete_message: str, calling_bot: MyBot_Generic, original_message = None):
        task_id: UUID = uuid4()
        self.my_task = LongRunningZairaTask(user=self, task_id=task_id, complete_message=complete_message, calling_bot=calling_bot, original_message=original_message)
        # Runs a new thread
        try:
            if Globals.is_debug() == True and calling_bot.parent_instance == None:
                await self.my_task.run_task()
            else:
                self.asyncio_Task = asyncio.create_task(self.my_task.run_task())
                self.asyncio_Task.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
            # Tell the system that we'd like to get the result back through the output supervisor
            await self.my_task.await_status_complete(wait_on_complete=False)
        except Exception as e:
            etc.helper_functions.exception_triggered(e)
            await calling_bot.send_reply("Zaira heeft haar best gedaan maar is tot de conclusie gekomen dat ze hulp nodig heeft. Indien nodig stuur dan een <NAME_EMAIL>", self, self.original_physical_message, False)
            self.my_task = None

    def get_chat_history(self, typing="LangChain") -> list:
        history = []
        if len(self.chat_history[self.sessionGUID]) > 0:
            if typing.lower() == "langchain":
                history = [message.to_langchain() for message in self.chat_history[self.sessionGUID]]
        return history
    
    async def get_supported_image_formats(self) -> List[str]:
        """
        Get list of supported image formats for multimodal processing.
        """
        return [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".webp"]
    
    async def is_multimodal_enabled(self) -> bool:
        """
        Check if multimodal processing is enabled for this user.
        """
        try:
            # Check user permissions and system configuration
            if self.rank == PERMISSION_LEVELS.NONE or self.rank == PERMISSION_LEVELS.GUEST:
                return False
            
            # Ensure MultimodalManager is set up
            await MultimodalManager.setup()
            return True
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Checking multimodal capabilities", self.GUID)
            return False

from userprofiles.LongRunningZairaTask import LongRunningZairaTask
ZairaUser.model_rebuild()
