from imports import *

from langchain_core.tools import BaseTool
from typing import Optional
from pydantic import BaseModel, Field

from managers.manager_supervisors import Supervisor<PERSON>anager, SupervisorTask_SingleAgent, SupervisorTaskState
from managers.manager_users import ZairaUserManager

class NewChatSessionTool(BaseTool):
    """Tool for creating new chat sessions"""
    name: str = "new_chat_session"
    description: str = "Creates a new chat session"
    
    def _run(self, state: SupervisorTaskState) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, state: SupervisorTaskState) -> str:
        """Creates a new chat session"""
        try:
            from uuid import uuid4
            user = await ZairaUserManager.get_instance().find_user(state.user_guid)
            if not user:
                return "User not found"
            
            new_session_guid = uuid4()
            user.sessionGUID = new_session_guid
            user.chat_history[new_session_guid] = []
            return f"New chat history session has been started with GUID {new_session_guid}"
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "new_chat_session", user if 'user' in locals() else None)
            return "Error creating new chat session"

class ChangeChatSessionTool(BaseTool):
    """Tool for changing chat sessions"""
    name: str = "change_chat_session"
    description: str = "Restores a previous chat session"
    
    def _run(self, sessionGUID: str, state: SupervisorTaskState) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, sessionGUID: str, state: SupervisorTaskState) -> str:
        """Restores a previous chat session"""
        try:
            user = await ZairaUserManager.get_instance().find_user(state.user_guid)
            if not user:
                return "User not found"
            
            from uuid import UUID
            try:
                session_uuid = UUID(sessionGUID)
            except ValueError:
                return f"Invalid session GUID format: {sessionGUID}"
            
            if session_uuid not in user.chat_history:
                return f"Session {sessionGUID} not found"
            
            old_session = user.sessionGUID
            user.sessionGUID = session_uuid
            return f"Chat session changed to {sessionGUID}. Previous session was {old_session}"
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "change_chat_session", user if 'user' in locals() else None)
            return "Error changing chat session"

class ListChatSessionsTool(BaseTool):
    """Tool for listing chat sessions"""
    name: str = "list_chat_sessions"
    description: str = "Print the GUIDs of all sessions of the user"
    
    def _run(self, state: SupervisorTaskState) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, state: SupervisorTaskState) -> str:
        """Print the GUIDs of all sessions of the user"""
        try:
            user = await ZairaUserManager.get_instance().find_user(state.user_guid)
            if not user:
                return "User not found"
            
            if not user.chat_history:
                return "No chat sessions found"
            
            session_list = [str(session_guid) for session_guid in user.chat_history.keys()]
            current_session = f"Current session: {user.sessionGUID}" if user.sessionGUID else "No current session"
            return f"{current_session}\nAll sessions: {', '.join(session_list)}"
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "list_chat_sessions", user if 'user' in locals() else None)
            return "Error listing chat sessions"

class SupervisorTask_ChangeSession(SupervisorTask_SingleAgent):
    folderID: dict[str,str] = {}

    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        
        return await super().llm_call(state)

# Create tool instances
new_chat_session_tool = NewChatSessionTool()
change_chat_session_tool = ChangeChatSessionTool()
list_chat_sessions_tool = ListChatSessionsTool()

async def create_task_manage_chat_sessions() -> SupervisorTask_SingleAgent:
    return SupervisorManager.get_instance().register_task(SupervisorTask_ChangeSession(name="manage_sessions_task", tools=[new_chat_session_tool, change_chat_session_tool, list_chat_sessions_tool], prompt_id="Task_Manage_Chat_Sessions"))
