"""
Comprehensive integration test for complete user workflow from creation to task execution.
This test covers the full end-to-end flow of user management, task creation, 
and supervisor coordination.
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_users import ZairaUserManager
from managers.manager_supervisors import SupervisorManager, SupervisorTaskState
from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
from userprofiles.ZairaUser import ZairaUser
from userprofiles.permission_levels import PERMISSION_LEVELS
from userprofiles.ScheduledZairaTask import ScheduledZairaTask
from userprofiles.LongRunningZairaTask import LongRunningZairaTask
from endpoints.mybot_generic import MyBot_Generic
from tasks.etc.task_chat_session import create_task_manage_chat_sessions
from tasks.inputs.task_scheduled_task_manager import SupervisorTask_ScheduledTaskManager
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
import asyncio

class TestComprehensiveUserWorkflow:
    """Integration test for complete user workflow"""
    
    def setup_method(self):
        """Set up comprehensive test environment"""
        # Reset all singleton instances
        ZairaUserManager._instance = None
        SupervisorManager._instance = None
        
        # Set up test data
        self.test_username = "integration_test_user"
        self.test_guid = uuid4()
        self.test_device_guid = uuid4()
        self.test_rank = PERMISSION_LEVELS.USER
        
        # Mock external dependencies
        self.mock_bot = MagicMock(spec=MyBot_Generic)
        self.mock_bot.name = "TestBot"
        self.mock_bot.parent_instance = None
        self.mock_bot.send_reply = AsyncMock()
        self.mock_bot.send_broadcast = AsyncMock()
    
    def teardown_method(self):
        """Clean up after tests"""
        ZairaUserManager._instance = None
        SupervisorManager._instance = None
    
    @pytest.mark.asyncio
    async def test_complete_user_lifecycle_workflow(self):
        """Test complete user lifecycle from creation to task execution"""
        with patch('managers.manager_scheduled_tasks.get_persistence_manager') as mock_get_persistence, \
             patch('userprofiles.ZairaUser.LogFire.log'), \
             patch('managers.manager_supervisors.LogFire.log'), \
             patch('userprofiles.LongRunningZairaTask.LogFire.log'), \
             patch('etc.helper_functions.exception_triggered') as mock_exception:
            
            # Mock persistence manager
            mock_persistence = AsyncMock()
            mock_persistence.resume_user_tasks = AsyncMock()
            mock_get_persistence.return_value = mock_persistence
            
            # Step 1: Create and add user
            user_manager = ZairaUserManager.get_instance()
            assert len(user_manager.users) == 0
            
            user = await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Verify user was created and added
            assert user is not None
            assert isinstance(user, ZairaUser)
            assert user.username == self.test_username
            assert user.GUID == self.test_guid
            assert str(self.test_guid) in user_manager.users
            assert len(user_manager.users) == 1
            
            # Step 2: Initialize supervisor system
            supervisor_manager = SupervisorManager.get_instance()
            assert supervisor_manager is not None
            
            # Step 3: Test user permissions and vector stores
            vector_stores = user.getAvailableVectorStores()
            assert vector_stores == ["user_vectors"]  # USER level permissions
            
            # Step 4: Test chat session management
            initial_session = user.sessionGUID
            assert initial_session is not None
            assert initial_session in user.chat_history
            assert len(user.chat_history[initial_session]) == 0
            
            # Step 5: Simulate user message interaction
            test_message = "Hello, I need help with my tasks"
            
            with patch('userprofiles.LongRunningZairaTask.LongRunningZairaTask') as mock_long_task, \
                 patch('userprofiles.ZairaUser.Globals.is_debug', return_value=True):
                
                # Mock the long running task
                mock_task_instance = MagicMock()
                mock_task_instance.run_task = AsyncMock()
                mock_task_instance.await_status_complete = AsyncMock()
                mock_task_instance.user = user
                mock_task_instance.human_in_the_loop_callback = None
                mock_long_task.return_value = mock_task_instance
                
                # Send message to user
                await user.on_message(
                    complete_message=test_message,
                    calling_bot=self.mock_bot,
                    attachments=[]
                )
                
                # Verify task was created and started
                mock_long_task.assert_called_once()
                mock_task_instance.run_task.assert_called_once()
                mock_task_instance.await_status_complete.assert_called_once()
                assert user.my_task == mock_task_instance
                
                # Verify message was added to chat history
                assert len(user.chat_history[user.sessionGUID]) == 1
                first_message = user.chat_history[user.sessionGUID][0]
                assert test_message in first_message.content
            
            # Step 6: Test user update functionality
            updated_user = await ZairaUserManager.update_user(
                str(self.test_guid),
                real_name="Test User",
                email="<EMAIL>",
                platform="TestPlatform"
            )
            
            assert updated_user == user
            assert user.real_name == "Test User"
            assert user.email == "<EMAIL>"
            assert user.platform == "TestPlatform"
            
            # Step 7: Test user lookup functionality
            found_user = await ZairaUserManager.find_user(str(self.test_guid))
            assert found_user == user
            
            found_by_username = await ZairaUserManager.get_user(self.test_username)
            assert found_by_username == user
            
            # Step 8: Test user removal
            removal_result = await ZairaUserManager.remove_user(str(self.test_guid))
            assert removal_result is True
            assert len(user_manager.users) == 0
            
            # Verify user can't be found after removal
            not_found = await ZairaUserManager.find_user(str(self.test_guid))
            assert not_found is None
    
    @pytest.mark.asyncio
    async def test_concurrent_user_operations(self):
        """Test concurrent user operations and thread safety"""
        with patch('managers.manager_scheduled_tasks.get_persistence_manager') as mock_get_persistence, \
             patch('userprofiles.ZairaUser.LogFire.log'):
            
            mock_persistence = AsyncMock()
            mock_persistence.resume_user_tasks = AsyncMock()
            mock_get_persistence.return_value = mock_persistence
            
            # Create multiple users concurrently
            async def create_user_task(index):
                username = f"concurrent_user_{index}"
                guid = uuid4()
                device_guid = uuid4()
                
                return await ZairaUserManager.add_user(
                    username=username,
                    rank=PERMISSION_LEVELS.USER,
                    guid=guid,
                    device_guid=device_guid
                )
            
            # Create 10 users concurrently
            tasks = [create_user_task(i) for i in range(10)]
            users = await asyncio.gather(*tasks)
            
            # Verify all users were created
            assert len(users) == 10
            user_manager = ZairaUserManager.get_instance()
            assert len(user_manager.users) == 10
            
            # Verify each user is unique and properly initialized
            user_guids = set()
            usernames = set()
            
            for user in users:
                assert isinstance(user, ZairaUser)
                assert user.GUID not in user_guids
                assert user.username not in usernames
                user_guids.add(user.GUID)
                usernames.add(user.username)
                
                # Verify user has proper session and chat history
                assert user.sessionGUID is not None
                assert user.sessionGUID in user.chat_history
                assert len(user.chat_history[user.sessionGUID]) == 0
            
            # Test concurrent user operations
            async def operate_on_user(user, operation_id):
                # Update user
                await ZairaUserManager.update_user(
                    str(user.GUID),
                    real_name=f"Real Name {operation_id}",
                    email=f"user{operation_id}@example.com"
                )
                
                # Find user
                found = await ZairaUserManager.find_user(str(user.GUID))
                assert found == user
                
                return user.GUID
            
            # Perform concurrent operations
            operation_tasks = [operate_on_user(user, i) for i, user in enumerate(users)]
            operation_results = await asyncio.gather(*operation_tasks)
            
            assert len(operation_results) == 10
            
            # Verify all operations completed successfully
            for i, user in enumerate(users):
                assert user.real_name == f"Real Name {i}"
                assert user.email == f"user{i}@example.com"
    
    @pytest.mark.asyncio
    async def test_user_permission_escalation_workflow(self):
        """Test user permission escalation and access control"""
        with patch('managers.manager_scheduled_tasks.get_persistence_manager') as mock_get_persistence, \
             patch('userprofiles.ZairaUser.LogFire.log'):
            
            mock_persistence = AsyncMock()
            mock_persistence.resume_user_tasks = AsyncMock()
            mock_get_persistence.return_value = mock_persistence
            
            # Create user with GUEST permissions
            guest_user = await ZairaUserManager.add_user(
                username="guest_user",
                rank=PERMISSION_LEVELS.GUEST,
                guid=uuid4(),
                device_guid=uuid4()
            )
            
            # Verify guest permissions
            assert guest_user.rank == PERMISSION_LEVELS.GUEST
            assert guest_user.getAvailableVectorStores() is None
            
            # Escalate to USER permissions
            updated_user = await ZairaUserManager.update_user(
                str(guest_user.GUID),
                rank=PERMISSION_LEVELS.USER
            )
            
            assert updated_user == guest_user
            assert guest_user.rank == PERMISSION_LEVELS.USER
            assert guest_user.getAvailableVectorStores() == ["user_vectors"]
            
            # Escalate to ADMIN permissions
            await ZairaUserManager.update_user(
                str(guest_user.GUID),
                rank=PERMISSION_LEVELS.ADMIN
            )
            
            assert guest_user.rank == PERMISSION_LEVELS.ADMIN
            assert guest_user.getAvailableVectorStores() == ["user_vectors", "admin_vectors"]
            
            # Test system user flag
            await ZairaUserManager.update_user(
                str(guest_user.GUID),
                is_system_user=True
            )
            
            assert guest_user.is_system_user is True
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self):
        """Test error handling and system recovery capabilities"""
        with patch('managers.manager_scheduled_tasks.get_persistence_manager') as mock_get_persistence, \
             patch('userprofiles.ZairaUser.LogFire.log'), \
             patch('etc.helper_functions.exception_triggered') as mock_exception:
            
            mock_persistence = AsyncMock()
            mock_persistence.resume_user_tasks = AsyncMock(side_effect=Exception("Persistence error"))
            mock_get_persistence.return_value = mock_persistence
            
            # Test user creation with persistence failure
            user = await ZairaUserManager.add_user(
                username="error_test_user",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            
            # User should still be created despite persistence error
            assert user is not None
            user_manager = ZairaUserManager.get_instance()
            assert str(user.GUID) in user_manager.users
            
            # Test duplicate user error handling
            with pytest.raises(ValueError, match="already exists"):
                await ZairaUserManager.add_user(
                    username="duplicate_user",
                    rank=PERMISSION_LEVELS.USER,
                    guid=user.GUID,  # Same GUID
                    device_guid=uuid4()
                )
            
            # Test operations on non-existent users
            fake_guid = str(uuid4())
            
            not_found = await ZairaUserManager.find_user(fake_guid)
            assert not_found is None
            
            not_updated = await ZairaUserManager.update_user(fake_guid, real_name="Test")
            assert not_updated is None
            
            not_removed = await ZairaUserManager.remove_user(fake_guid)
            assert not_removed is False
            
            # Test task creation error handling
            user.original_physical_message = MagicMock()  # Add required attribute
            
            with patch('userprofiles.LongRunningZairaTask.LongRunningZairaTask') as mock_long_task:
                mock_task_instance = MagicMock()
                mock_task_instance.await_status_complete = AsyncMock(side_effect=Exception("Task error"))
                mock_long_task.return_value = mock_task_instance
                
                await user.start_task(
                    complete_message="Test message",
                    calling_bot=self.mock_bot,
                    original_message=None
                )
                
                # Verify error was handled
                mock_exception.assert_called()
                self.mock_bot.send_reply.assert_called()
                assert user.my_task is None