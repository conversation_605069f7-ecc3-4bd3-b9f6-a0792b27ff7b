from imports import *

from langchain_core.tools import BaseTool, tool
import logging
from typing import Optional, Any, Dict
import json
import asyncio
from datetime import datetime

from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState, SupervisorTask_Base
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from tasks.data import AgendaProcessingData

from pydantic import BaseModel, Field

try:
    from langchain_google_community import CalendarToolkit
    from langchain_google_community.calendar.create_event import CalendarCreateEvent
    from langchain_google_community.calendar.utils import (
        build_resource_service,
        get_google_credentials,
    )
    from googleapiclient.discovery import build
    from google.oauth2.credentials import Credentials
    CALENDAR_IMPORTS_AVAILABLE = True
except ImportError as e:
    logging.error(f"Google Calendar dependencies not available: {e}")
    CALENDAR_IMPORTS_AVAILABLE = False


def format_event_preview(event_details: dict) -> str:
    """Format event details for user preview"""
    try:
        # Parse datetime strings for better display
        start_time = event_details.get('start', {}).get('dateTime', 'Niet opgegeven')
        end_time = event_details.get('end', {}).get('dateTime', 'Niet opgegeven')
        
        # Format attendees
        attendees = event_details.get('attendees', [])
        attendee_emails = []
        if attendees:
            for attendee in attendees:
                if isinstance(attendee, dict):
                    attendee_emails.append(attendee.get('email', ''))
                else:
                    attendee_emails.append(str(attendee))
        
        attendee_list = ', '.join(filter(None, attendee_emails)) if attendee_emails else 'Geen'
        
        return f"""
Afspraak details:
Titel: {event_details.get('summary', 'Geen titel')}
Start: {start_time}
Eind: {end_time}
Locatie: {event_details.get('location', 'Geen')}
Deelnemers: {attendee_list}
        """.strip()
    except Exception as e:
        logging.error(f"Error formatting event preview: {e}")
        return f"Fout bij het formatteren van afspraak details: {str(e)}"


class CalendarCreateEventInput(BaseModel):
    """Input schema for calendar create event with confirmation"""
    event_details: dict = Field(description="Event details in Google Calendar API format")
    state: Optional[Any] = Field(default=None, description="Supervisor state for context")


class AgendaSenderTool(BaseTool):
    """Tool for creating calendar events using Google Calendar API"""
    name: str = "agenda_sender_tool"
    description: str = "Creates calendar events using Google Calendar API with OAuth2 authentication"
    
    def _run(self, event_details: dict, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, event_details: dict, state: SupervisorTaskState = None) -> str:
        """Creates a calendar event using the provided event data"""
        try:
            user = await ZairaUserManager.get_instance().find_user(state.user_guid)
            
            # Get agenda data from processing_data
            agenda_data = None
            if hasattr(user, 'my_requests') and state.scheduled_guid in user.my_requests:
                processing_data = user.my_requests[state.scheduled_guid].processing_data
                if processing_data and isinstance(processing_data, AgendaProcessingData):
                    agenda_data = processing_data
            
            # If no structured data available, try to get from event_details parameter
            if not agenda_data:
                if not event_details or not isinstance(event_details, dict):
                    return "Missing agenda data - no structured processing data found and event_details incomplete"
                
                # Create agenda data from event_details for backward compatibility
                from datetime import datetime
                try:
                    agenda_data = AgendaProcessingData(
                        summary=event_details.get('summary', 'Untitled Event'),
                        description=event_details.get('description'),
                        location=event_details.get('location'),
                        start_datetime=datetime.fromisoformat(event_details['start']['dateTime']) if 'start' in event_details else datetime.now(),
                        end_datetime=datetime.fromisoformat(event_details['end']['dateTime']) if 'end' in event_details else datetime.now()
                    )
                    # Mark as approved since it came from direct parameters
                    agenda_data.mark_approved()
                except Exception as e:
                    return f"Error creating agenda data from event_details: {e}"
            
            # Check if agenda was approved (agenda data should be pre-approved from processing task)
            if not agenda_data.user_approved:
                return "Calendar event creation cancelled - not approved by user."
            
            # Create CalendarClient for API operations using structured data
            calendar_event_data = agenda_data.to_calendar_event_format()
            result = await self._create_calendar_event(calendar_event_data, state)
            
            if result["success"]:
                # Mark agenda data as sent and store event ID
                agenda_data.mark_sent()
                agenda_data.event_id = result.get('event_id', '')
                
                # Update the processing data in the request
                if hasattr(user, 'my_requests') and state.scheduled_guid in user.my_requests:
                    user.my_requests[state.scheduled_guid].processing_data = agenda_data
                
                return f"Calendar event created successfully!\n\nEvent: {agenda_data.summary}\nEvent ID: {result.get('event_id', 'unknown')}"
            else:
                return f"Failed to create calendar event: {result['error']}"
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "agenda_sender_tool", state.user_guid if state else None)
            return "Failed to create calendar event due to an error"
    
    async def _create_calendar_event(self, event_data: dict, state: SupervisorTaskState = None) -> dict:
        """
        Direct CalendarCreateEvent integration function with error handling
        
        Args:
            event_data: Dict with keys: summary, start, end, description?, location?
            state: Supervisor state for context
        
        Returns:
            Dict with success status and event details or error message
        """
        try:
            if not CALENDAR_IMPORTS_AVAILABLE:
                return {"success": False, "error": "Google Calendar dependencies not available"}
            
            # Get OAuth tokens using existing system
            try:
                bearer_token = await OAuth2Verifier.get_token("gcalendar")
                refresh_token = await OAuth2Verifier.get_token("gcalendar", "refresh_token")
            except Exception as e:
                logging.error(f"Error getting OAuth tokens: {str(e)}")
                return {"success": False, "error": f"Authentication failed: {str(e)}"}

            if not bearer_token:
                return {"success": False, "error": "No valid authentication token available"}

            # Get OAuth app configuration
            gcalendar_app = OAuth2Verifier.get_instance().apps["gcalendar"]
            
            # Set up Google Calendar credentials
            token_info = {
                "client_id": gcalendar_app.client_id,
                "client_secret": gcalendar_app.client_secret,
                "refresh_token": refresh_token,
                "token_uri": gcalendar_app.token_url,
                "access_token": bearer_token,
                "expires_in": await OAuth2Verifier.get_token("gcalendar", "expires_in"),
                "scopes": gcalendar_app.oauth_scopes
            }
            
            # Create credentials and build the Google API service
            credentials = Credentials.from_authorized_user_info(token_info)
            api_resource = build_resource_service(credentials=credentials)
            
            # Create the CalendarCreateEvent tool
            create_event_tool = CalendarCreateEvent.from_api_resource(api_resource)
            
            # Validate required fields
            required_fields = ["summary", "start", "end"]
            for field in required_fields:
                if field not in event_data:
                    return {"success": False, "error": f"Missing required field: {field}"}
            
            # Execute event creation
            logging.info(f"Creating calendar event: {event_data.get('summary', 'Untitled')}")
            result = await create_event_tool.ainvoke(event_data)
            
            return {
                "success": True, 
                "event_id": result.get("id", "unknown"),
                "event_details": result,
                "message": f"Successfully created event: {event_data.get('summary', 'Untitled')}"
            }
            
        except Exception as e:
            logging.error(f"Error creating calendar event: {str(e)}")
            return {
                "success": False, 
                "error": f"Failed to create calendar event: {str(e)}"
            }


class CalendarCreateEventWithConfirmation(BaseTool):
    """Custom calendar create event tool with human-in-the-loop confirmation"""
    name: str = "CalendarCreateEvent"
    description: str = "Create a new calendar event with user confirmation preview"
    args_schema: type = CalendarCreateEventInput
    original_tool: Any = Field(default=None, description="Original calendar create tool")
    
    def __init__(self, original_tool, **kwargs):
        super().__init__(**kwargs)
        self.original_tool = original_tool
    
    def _run(self, event_details: dict, state: Optional[Any] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, event_details: dict, state: Optional[Any] = None) -> str:
        """Create calendar event with user confirmation"""
        try:
            # Get current user and task from injected state
            if not state:
                return "Fout: Geen state beschikbaar voor bevestiging"
            
            if not hasattr(state, 'user_guid') or not state.user_guid:
                return "Fout: Geen gebruiker ID gevonden in state"
            
            from managers.manager_users import ZairaUserManager
            user = await ZairaUserManager.find_user(state.user_guid)
            if not user or not user.has_active_requests():
                return "Fout: Geen gebruikerstaak gevonden voor bevestiging"
            
            # Format event preview
            event_preview = format_event_preview(event_details)
            
            # Request human confirmation
            confirmation_message = f"Wil je deze afspraak aanmaken? (j/n)\n\n{event_preview}"
            
            # Define callback to capture user response
            self.user_response = None
            async def confirmation_callback(task, response):
                self.user_response = response
                return response
            
            # Request confirmation from user
            # Get user's active task to request human input
            await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                confirmation_message,
                confirmation_callback,
                True  # Wait for response
            )
            
            # Wait for user response
            while self.user_response is None:
                await asyncio.sleep(0.1)
            
            confirmed = self.user_response
            
            # Check if user confirmed
            if not confirmed or str(confirmed).lower() not in ['j', 'ja', 'yes', 'y']:
                return "Afspraak aanmaken geannuleerd door gebruiker"
            
            # User confirmed, create the event using original tool
            if hasattr(self.original_tool, '_arun'):
                result = await self.original_tool._arun(event_details)
            else:
                result = self.original_tool._run(event_details)
            
            return f"Afspraak succesvol aangemaakt na bevestiging!\n\nDetails:\n{result}"
            
        except Exception as e:
            logging.error(f"Error in calendar create with confirmation: {e}")
            return f"Fout bij aanmaken afspraak: {str(e)}"


class AgendaSenderDirectTool(BaseTool):
    """Direct agenda creation tool without approval workflow"""
    name: str = "agenda_sender_direct_tool"
    description: str = "Creates calendar events directly without user approval - use for automated/system events"
    
    def _run(self, event_details: dict, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, event_details: dict, state: SupervisorTaskState = None) -> str:
        """Creates a calendar event directly without approval"""
        try:
            # Use AgendaSenderTool but skip approval check
            agenda_tool = AgendaSenderTool()
            result = await agenda_tool._create_calendar_event(event_details, state)
            
            if result["success"]:
                # Direct tool doesn't update processing data since it bypasses the normal workflow
                return f"Calendar event created directly!\n\nEvent: {event_details.get('summary', 'Untitled')}\nEvent ID: {result.get('event_id', 'unknown')}"
            else:
                return f"Failed to create calendar event: {result['error']}"
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "agenda_sender_direct_tool", state.user_guid if state else None)
            return "Failed to create calendar event due to an error"


@tool
async def direct_calendar_create_tool(summary: str, start_datetime: str, end_datetime: str, description: str = "", location: str = "", state: Optional[Any] = None) -> str:
    """
    Create a calendar event directly using CalendarCreateEvent tool
    
    Args:
        summary: Event title/summary
        start_datetime: Start time in ISO format (e.g., "2024-01-15T14:00:00")
        end_datetime: End time in ISO format (e.g., "2024-01-15T15:30:00") 
        description: Optional event description
        location: Optional event location
        state: Supervisor state for context
    
    Returns:
        Success message with event details or error message
    """
    try:
        from datetime import datetime
        
        # Parse datetime strings
        start_dt = datetime.fromisoformat(start_datetime.replace("Z", "+00:00"))
        end_dt = datetime.fromisoformat(end_datetime.replace("Z", "+00:00"))
        
        event_data = {
            "summary": summary,
            "start": start_dt,
            "end": end_dt
        }
        
        if description:
            event_data["description"] = description
        if location:
            event_data["location"] = location
            
        # Use AgendaSenderDirectTool for direct creation
        direct_tool = AgendaSenderDirectTool()
        result = await direct_tool._arun(event_data, state)
        
        return result
            
    except Exception as e:
        logging.error(f"Error in direct_calendar_create_tool: {e}")
        return f"❌ Error creating calendar event: {str(e)}"


async def get_calendar_tools():
    """Get Google Calendar tools for the agenda planner"""
    try:
        if not CALENDAR_IMPORTS_AVAILABLE:
            return []
            
        # Get OAuth tokens using existing system
        try:
            bearer_token = await OAuth2Verifier.get_token("gcalendar")
            refresh_token = await OAuth2Verifier.get_token("gcalendar", "refresh_token")
        except Exception as e:
            logging.error(f"Error getting OAuth tokens: {str(e)}")
            return []

        if not bearer_token:
            return []

        # Get OAuth app configuration
        gcalendar_app = OAuth2Verifier.get_instance().apps["gcalendar"]
        
        # Set up Google Calendar credentials for the toolkit
        token_info = {
            "client_id": gcalendar_app.client_id,
            "client_secret": gcalendar_app.client_secret,
            "refresh_token": refresh_token,
            "token_uri": gcalendar_app.token_url,
            "access_token": bearer_token,
            "expires_in": await OAuth2Verifier.get_token("gcalendar", "expires_in"),
            "scopes": gcalendar_app.oauth_scopes
        }
        
        # Create credentials and build the Google API service
        credentials = Credentials.from_authorized_user_info(token_info)
        api_resource = build_resource_service(credentials=credentials)
        
        # Initialize the Google Calendar Toolkit
        toolkit = CalendarToolkit(api_resource=api_resource)
        
        # Get all tools from the toolkit
        calendar_tools = toolkit.get_tools()
        
        # Replace CalendarCreateEvent with our custom confirmation version
        modified_tools = []
        original_create_tool = None
        
        for tool in calendar_tools:
            if tool.name == "CalendarCreateEvent":
                original_create_tool = tool
                # Create our custom tool with confirmation
                custom_create_tool = CalendarCreateEventWithConfirmation(original_tool=tool)
                modified_tools.append(custom_create_tool)
            else:
                modified_tools.append(tool)
        
        return modified_tools
        
    except Exception as e:
        logging.error(f"Error setting up calendar tools: {str(e)}")
        return []


class SupervisorTask_Agenda(SupervisorTask_SingleAgent):
    """Supervisor task for agenda/calendar operations"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # You can add specific agenda task initialization here
    
    async def llm_call(self, state: SupervisorTaskState):
        # Use the parent's LLM call implementation
        return await super().llm_call(state)