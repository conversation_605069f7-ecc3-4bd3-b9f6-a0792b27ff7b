<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZairaControl - Scheduled Request Dashboard</title>
    <link rel="stylesheet" href="/dashboard/static/css/dashboard.css">
    <link rel="stylesheet" href="/dashboard/static/css/menu.css">
</head>
<body>
    <div class="header">
        <h1>ZairaControl Dashboard</h1>
        <span class="status-badge">{{ overall_status }}</span>
        <div style="display: flex; align-items: center; gap: 1rem;">
            <button onclick="window.location.href='/'" style="background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 0.8rem; font-weight: 500; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(59, 130, 246, 0.3)'">
                Dashboard
            </button>
            <div class="auto-refresh" id="autoRefresh">
                <span id="refreshTimer">Auto-refresh: Disabled</span>
                <button onclick="toggleAutoRefresh()" id="refreshToggle" style="margin-left: 10px; padding: 2px 8px; font-size: 0.7rem;">Enable</button>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-title">Active Managers</div>
                <div class="metric-value">{{ active_managers }}</div>
                <div class="metric-subtitle">User-specific managers</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Total Requests</div>
                <div class="metric-value">{{ total_requests }}</div>
                <div class="metric-subtitle">All-time requests</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Active Tasks</div>
                <div class="metric-value">{{ total_active_tasks }}</div>
                <div class="metric-subtitle">Currently running</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">System Health</div>
                <div class="metric-value" style="color: {{ status_color }}; font-size: 1.8rem;">{{ overall_status.upper() }}</div>
                <div class="metric-subtitle">Overall system status</div>
            </div>
        </div>
        
        <div class="section">
            <h2>User Management</h2>
            <div class="tab-container">
                <div class="tabs">
                    <button class="tab-button active" onclick="showTab('user-list')">User List</button>
                    <button class="tab-button" onclick="showTab('user-requests')">Scheduled Requests</button>
                    <button class="tab-button" onclick="showTab('chat-history')">Chat History</button>
                </div>
                
                <!-- User List Tab -->
                <div id="user-list" class="tab-content active">
                    <div class="user-search-controls">
                        <input type="text" id="userSearchInput" placeholder="Search users by username..." onkeyup="filterUsers()" />
                        <button onclick="loadUserList()">Refresh User List</button>
                    </div>
                    <div id="userListContainer">
                        <div class="loading">Click "Refresh User List" to load all users</div>
                    </div>
                </div>
                
                <!-- User Requests Tab -->
                <div id="user-requests" class="tab-content">
                    <div class="user-search-controls">
                        <input type="text" id="userGuidInput" placeholder="Enter user GUID..." />
                        <button onclick="loadUserRequests()">Load Requests</button>
                        <button onclick="loadSystemOverview()">System Overview</button>
                    </div>
                    <div id="requestsContainer">
                        <div class="loading">Select a user from User List or enter GUID to view scheduled requests</div>
                    </div>
                </div>
                
                <!-- Chat History Tab -->
                <div id="chat-history" class="tab-content">
                    <div class="user-search-controls">
                        <input type="text" id="chatUserGuidInput" placeholder="Enter user GUID..." />
                        <button onclick="loadChatHistory()">Load Chat History</button>
                    </div>
                    <div id="chatHistoryContainer">
                        <div class="loading">Select a user from User List or enter GUID to view chat history</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>System Information</h2>
            <div id="systemInfo">
                <p><strong>Last Updated:</strong> {{ timestamp | default('Unknown') }}</p>
                <p><strong>System Status:</strong> <span style="color: {{ status_color }}">{{ overall_status.title() }}</span></p>
                <p><strong>Active User Managers:</strong> {{ active_managers }}</p>
                <p><strong>Architecture:</strong> Enterprise-grade user-isolated scheduled request management</p>
            </div>
        </div>
    </div>
    
    <!-- Menu will be injected here -->
    
    <!-- dashboard.js is loaded in dashboard_footer.txt -->
    <script src="/dashboard/static/js/menu.js"></script>
</body>
</html>