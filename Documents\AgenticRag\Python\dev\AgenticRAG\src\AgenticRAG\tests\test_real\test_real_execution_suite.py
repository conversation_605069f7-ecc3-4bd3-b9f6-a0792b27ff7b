#!/usr/bin/env python3
"""
CONSOLIDATED Real Execution Test Suite
This file consolidates all real execution integration tests into a single comprehensive suite.

Original files consolidated:
- test_real_email_agenda_coordination.py
- test_real_agenda_execution.py  
- test_real_email_sending_consolidated.py
- test_real_email_sending_execution.py
- test_real_tell_me_about_your_data_execution.py

Coverage:
- Real email-agenda coordination workflows
- Real agenda planning and calendar operations
- Real email sending and routing verification
- Real tell me about your data execution
- Performance analysis for real execution
- Quality and response validation
- Real system component verification
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../src'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
import asyncio
import time
from uuid import uuid4
from typing import List, Dict, Any, Optional
from pathlib import Path

from managers.manager_supervisors import SupervisorManager
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import <PERSON><PERSON><PERSON><PERSON><PERSON>, PERMISSION_LEVELS
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from etc.setup import init
from tasks.task_top_level_supervisor import create_top_level_supervisor
from endpoints.testing_endpoint import MyBot_Testing

# Global cache for real execution results to avoid repeated expensive operations
_real_execution_cache = {}

def clear_real_execution_cache():
    """Clear the global real execution cache"""
    global _real_execution_cache
    _real_execution_cache = {}


@pytest.mark.integration
@pytest.mark.asyncio
class TestRealExecutionSuite:
    """Consolidated test suite for all real execution integration testing"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_session_guid = str(uuid4())
        self.test_scheduled_guid = str(uuid4())
        
        # Clear cache before each test
        clear_real_execution_cache()
        
        # Test queries for different real execution scenarios
        self.real_execution_queries = {
            # Email-agenda coordination
            "email_agenda_coordination": "Check my calendar for tomorrow and send a meeting reminder <NAME_EMAIL>",
            "meeting_invitation": "Create a meeting invitation email for next week's team <NAME_EMAIL>",
            "agenda_summary": "Review my agenda and email a <NAME_EMAIL>",
            "calendar_conflict": "Check for schedule conflicts <NAME_EMAIL> if needed",
            "event_reminder": "Send email reminders for upcoming calendar <NAME_EMAIL>",
            
            # Agenda-specific operations
            "basic_agenda": "Plan a team meeting for next week",
            "detailed_planning": "Create a comprehensive agenda for our quarterly review meeting",
            "calendar_scheduling": "Schedule a weekly team sync and create the agenda",
            "event_planning": "Plan and schedule a project kickoff meeting with agenda items",
            "agenda_analysis": "Analyze my calendar and suggest meeting agenda improvements",
            
            # Email-specific operations
            "email_sending": "send a random test <NAME_EMAIL>",
            "email_routing": "generate and send an <NAME_EMAIL>",
            
            # Tell me about your data
            "tell_me_about_data": "tell me about your data",
            "data_analysis": "analyze the data you have access to",
            "data_summary": "provide a summary of your available data sources"
        }
        
        # Initialize Globals for testing
        try:
            Globals.Debug = False
            if not hasattr(ZairaSettings, 'IsDebugMode'):
                ZairaSettings.IsDebugMode = False
        except:
            pass
    
    def teardown_method(self):
        """Clean up after each test"""
        try:
            loop = asyncio.get_running_loop()
            tasks = [task for task in asyncio.all_requests(loop) if not task.done()]
            for task in tasks:
                if task != asyncio.current_task():
                    task.cancel()
        except RuntimeError:
            pass
    
    # =============================================================================
    # REAL SYSTEM SETUP
    # =============================================================================
    
    async def _setup_real_system(self, system_type: str = "general"):
        """Set up the REAL system components for testing"""
        try:
            LogFire.log("DEBUG", f"[TEST] Setting up REAL system for {system_type} testing")
            
            import etc.parsers as parsers
            
            # Set up directories based on system type
            if system_type == "email_agenda":
                DATA_DIR = Path("/tmp/test_email_agenda_real_data")
                PERSIST_DIR = Path("/tmp/test_email_agenda_real_persist")
                test_docs = [
                    ("calendar_policy.txt", "Company calendar policy and meeting guidelines"),
                    ("email_templates.txt", "Standard email templates for business communication"),
                    ("team_directory.txt", "Team member contact information and roles")
                ]
            elif system_type == "agenda":
                DATA_DIR = Path("/tmp/test_agenda_real_data")
                PERSIST_DIR = Path("/tmp/test_agenda_real_persist")
                test_docs = [
                    ("meeting_guidelines.txt", "Company meeting guidelines and best practices for agenda creation"),
                    ("team_structure.txt", "Team organization and meeting participants information"),
                    ("project_info.txt", "Current project status and milestones for meeting planning"),
                    ("calendar_policies.txt", "Calendar scheduling policies and meeting room availability")
                ]
            elif system_type == "email":
                DATA_DIR = Path("/tmp/test_email_real_data")
                PERSIST_DIR = Path("/tmp/test_email_real_persist")
                test_docs = [
                    ("email_templates.txt", "Standard email templates for business communication"),
                    ("contact_list.txt", "Company contact directory and email addresses"),
                    ("communication_policies.txt", "Email communication guidelines and policies")
                ]
            elif system_type == "data_analysis":
                DATA_DIR = Path("/tmp/test_data_analysis_real_data")
                PERSIST_DIR = Path("/tmp/test_data_analysis_real_persist")
                test_docs = [
                    ("data_sources.txt", "Available data sources and their descriptions"),
                    ("data_catalog.txt", "Data catalog with source metadata and access information"),
                    ("analysis_templates.txt", "Templates for data analysis and reporting")
                ]
            else:
                DATA_DIR = Path("/tmp/test_real_data")
                PERSIST_DIR = Path("/tmp/test_real_persist")
                test_docs = [
                    ("general_info.txt", "General information for real system testing"),
                    ("system_docs.txt", "System documentation and user guides"),
                    ("test_data.txt", "Test data for validation and verification")
                ]
            
            # Create directories
            DATA_DIR.mkdir(parents=True, exist_ok=True)
            PERSIST_DIR.mkdir(parents=True, exist_ok=True)
            
            # Create test documents
            for filename, content in test_docs:
                doc_path = DATA_DIR / filename
                if not doc_path.exists():
                    doc_path.write_text(content)
            
            # Initialize full system
            await init(newProject=True, DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
            
            # Verify managers are initialized
            await SupervisorManager.setup()
            
            # Check if supervisors are available
            supervisor_manager = SupervisorManager.get_instance()
            top_supervisor = supervisor_manager.get_supervisor("top_level_supervisor")
            
            if top_supervisor:
                LogFire.log("DEBUG", "[TEST] Real system setup complete - supervisors available")
                return True
            
            # Create supervisors if needed
            LogFire.log("DEBUG", "[TEST] Creating supervisors for real system testing")
            
            from managers.manager_prompts import PromptManager
            await PromptManager.setDefaultPrompts()
            
            top_supervisor = await create_top_level_supervisor()
            
            # Initialize specialized supervisors based on system type
            try:
                if system_type in ["email_agenda", "email"]:
                    from tasks.processing.task_email_generator import create_task_email_generator
                    from tasks.outputs.output_requests.task_out_email import create_supervisor_email_sender
                    await create_task_email_generator()
                    await create_supervisor_email_sender()
                    LogFire.log("DEBUG", "[TEST] Email supervisors created")
                
                if system_type in ["email_agenda", "agenda"]:
                    from tasks.processing.task_agenda_planner import create_supervisor_agenda_planner
                    await create_supervisor_agenda_planner()
                    LogFire.log("DEBUG", "[TEST] Agenda supervisors created")
                
                if system_type == "data_analysis":
                    # Data analysis specific supervisors would be created here
                    LogFire.log("DEBUG", "[TEST] Data analysis supervisors created")
                
            except Exception as e:
                LogFire.log("DEBUG", f"[TEST] Warning: Could not create all specialized supervisors: {e}")
            
            # Verify LLM and other components
            if not hasattr(ZairaSettings, 'llm') or ZairaSettings.llm is None:
                LogFire.log("DEBUG", "[TEST] Warning: No LLM configured - this may affect real execution")
            
            return True
            
        except Exception as e:
            LogFire.log("DEBUG", f"[TEST] Real system setup failed: {e}")
            return False
    
    async def _get_real_execution_result(self, query_type: str, system_type: str = "general"):
        """Get cached real execution result or run the test"""
        global _real_execution_cache
        cache_key = f"{query_type}_{system_type}_result"
        
        if cache_key in _real_execution_cache:
            return _real_execution_cache[cache_key]
        
        # Set up real system
        setup_success = await self._setup_real_system(system_type)
        if not setup_success:
            pytest.skip(f"Could not initialize real system for {system_type} testing")
        
        query = self.real_execution_queries.get(query_type, "tell me about your capabilities")
        
        async def run_real_test():
            try:
                # Create test user
                user_manager = ZairaUserManager.get_instance()
                test_user = await user_manager.add_user(
                    "test_platform", 
                    PERMISSION_LEVELS.ADMIN, 
                    uuid4(), 
                    uuid4()
                )
                
                # Set user email for email functionality
                test_user.email = "<EMAIL>"
                
                # Create testing bot instead of using main_loop's bot
                bot_generic = MyBot_Testing(None, "Testing")
                
                LogFire.log("DEBUG", f"[TEST] Executing REAL {system_type} query: '{query}'")
                LogFire.log("DEBUG", "[TEST] This will use actual LLM calls, real components, system processing, and automated bot responses")
                
                # Create task with timeout - use on_message instead of call_supervisor
                # This ensures the bot handles human-in-the-loop requests
                user_task = asyncio.create_task(
                    test_user.on_message(
                        complete_message=query,
                        calling_bot=bot_generic,
                        attachments=[],
                        original_message=None
                    )
                )
                
                # Wait for completion with timeout
                await asyncio.wait_for(user_task, timeout=90.0)
                
                # Extract results from the user's active tasks
                if test_user.has_active_requests():
                    # Wait a bit more for completion
                    await asyncio.sleep(2)
                
                # Get the most recent task result
                task_results = []
                for task_guid, task in test_user.my_requests.items():
                    if hasattr(task, 'call_trace') and hasattr(task, 'result_message'):
                        task_results.append({
                            "call_trace": task.call_trace,
                            "result": task.result_message,
                            "task_guid": task_guid
                        })
                
                # Use the most recent task or create a basic result
                if task_results:
                    result = task_results[-1]  # Get the most recent
                else:
                    # Fallback - try to get result from user's conversation
                    recent_messages = test_user.get_recent_messages(limit=5)
                    result = {
                        "call_trace": ["test_execution_via_bot"],
                        "result": recent_messages[-1].content if recent_messages else "Test completed"
                    }
                
                # Extract results
                actual_call_trace = result["call_trace"]
                actual_response = result["result"]
                
                LogFire.log("DEBUG", f"[TEST] REAL {system_type} call_trace: {actual_call_trace}")
                LogFire.log("DEBUG", f"[TEST] Response length: {len(str(actual_response))}")
                
                # Analyze real execution components
                real_components = self._analyze_real_execution(actual_call_trace, system_type)
                
                execution_result = {
                    "call_trace": actual_call_trace,
                    "response": actual_response,
                    "real_components": real_components,
                    "execution_type": "real",
                    "query_type": query_type,
                    "system_type": system_type,
                    "query": query
                }
                
                return execution_result
                
            except asyncio.TimeoutError:
                LogFire.log("DEBUG", f"[TEST] Real {system_type} execution timed out")
                if 'supervisor_task' in locals() and not supervisor_task.done():
                    supervisor_task.cancel()
                    try:
                        await asyncio.wait_for(supervisor_task, timeout=2.0)
                    except (asyncio.CancelledError, asyncio.TimeoutError):
                        pass
                
                return {
                    "call_trace": ["real_execution_timeout"],
                    "response": "Real execution timed out",
                    "real_components": {},
                    "execution_type": "timeout",
                    "query_type": query_type,
                    "system_type": system_type,
                    "query": query
                }
                
            except Exception as e:
                LogFire.log("DEBUG", f"[TEST] Exception during real {system_type} execution: {str(e)}")
                return {
                    "call_trace": ["real_execution_exception"],
                    "response": f"Real execution exception: {str(e)}",
                    "real_components": {},
                    "execution_type": "exception",
                    "query_type": query_type,
                    "system_type": system_type,
                    "query": query
                }
        
        try:
            result = await run_real_test()
            
            # Cache the result
            _real_execution_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            pytest.fail(f"Real {system_type} execution test failed: {str(e)}")
    
    def _analyze_real_execution(self, call_trace: List[str], system_type: str) -> Dict[str, List[str]]:
        """Analyze the real execution components based on system type"""
        if system_type == "email_agenda":
            return self._analyze_email_agenda_execution(call_trace)
        elif system_type == "agenda":
            return self._analyze_agenda_execution(call_trace)
        elif system_type == "email":
            return self._analyze_email_execution(call_trace)
        elif system_type == "data_analysis":
            return self._analyze_data_analysis_execution(call_trace)
        else:
            return self._analyze_general_execution(call_trace)
    
    def _analyze_email_agenda_execution(self, call_trace: List[str]) -> Dict[str, List[str]]:
        """Analyze email-agenda coordination execution"""
        components = {
            "top_level_supervisor": [],
            "email_generation_components": [],
            "email_sending_components": [],
            "agenda_planning_components": [],
            "calendar_access_components": [],
            "coordination_components": [],
            "output_processing_components": [],
            "rag_components": [],
            "llm_components": [],
            "other_components": []
        }
        
        for entry in call_trace:
            entry_lower = str(entry).lower()
            
            if "top_level_supervisor" in entry_lower:
                components["top_level_supervisor"].append(entry)
            elif "email_generator" in entry_lower or "generate_email" in entry_lower:
                components["email_generation_components"].append(entry)
            elif "email_sender" in entry_lower or "send_email" in entry_lower or "mail_out" in entry_lower:
                components["email_sending_components"].append(entry)
            elif "agenda_planner" in entry_lower or "agenda_expert" in entry_lower:
                components["agenda_planning_components"].append(entry)
            elif "calendar" in entry_lower and ("access" in entry_lower or "tool" in entry_lower):
                components["calendar_access_components"].append(entry)
            elif (("email" in entry_lower and "agenda" in entry_lower) or ("coordination" in entry_lower)):
                components["coordination_components"].append(entry)
            elif "output" in entry_lower and ("processing" in entry_lower or "sender" in entry_lower):
                components["output_processing_components"].append(entry)
            elif "rag" in entry_lower or "retrieval" in entry_lower:
                components["rag_components"].append(entry)
            elif "llm" in entry_lower or "language_model" in entry_lower:
                components["llm_components"].append(entry)
            else:
                components["other_components"].append(entry)
        
        return components
    
    def _analyze_agenda_execution(self, call_trace: List[str]) -> Dict[str, List[str]]:
        """Analyze agenda-specific execution"""
        components = {
            "top_level_supervisor": [],
            "agenda_planning_components": [],
            "agenda_generation_components": [],
            "calendar_tools_components": [],
            "calendar_creation_components": [],
            "event_scheduling_components": [],
            "output_processing_components": [],
            "rag_components": [],
            "llm_components": [],
            "other_components": []
        }
        
        for entry in call_trace:
            entry_lower = str(entry).lower()
            
            if "top_level_supervisor" in entry_lower:
                components["top_level_supervisor"].append(entry)
            elif "agenda_planner" in entry_lower or "agenda_planning" in entry_lower:
                components["agenda_planning_components"].append(entry)
            elif "agenda_generator" in entry_lower or "agenda_generation" in entry_lower:
                components["agenda_generation_components"].append(entry)
            elif "calendar" in entry_lower and ("tool" in entry_lower or "toolkit" in entry_lower):
                components["calendar_tools_components"].append(entry)
            elif "calendar" in entry_lower and ("create" in entry_lower or "event" in entry_lower):
                components["calendar_creation_components"].append(entry)
            elif "schedule" in entry_lower or "meeting" in entry_lower or "event" in entry_lower:
                components["event_scheduling_components"].append(entry)
            elif "output" in entry_lower and ("processing" in entry_lower or "sender" in entry_lower):
                components["output_processing_components"].append(entry)
            elif "rag" in entry_lower or "retrieval" in entry_lower:
                components["rag_components"].append(entry)
            elif "llm" in entry_lower or "language_model" in entry_lower:
                components["llm_components"].append(entry)
            else:
                components["other_components"].append(entry)
        
        return components
    
    def _analyze_email_execution(self, call_trace: List[str]) -> Dict[str, List[str]]:
        """Analyze email-specific execution"""
        components = {
            "top_level_supervisor": [],
            "quick_requests": [],
            "email_generator_entries": [],
            "email_sender_entries": [],
            "email_out_entries": [],
            "output_supervisor_entries": [],
            "smtp_components": [],
            "routing_components": [],
            "other_components": []
        }
        
        for entry in call_trace:
            entry_lower = str(entry).lower()
            
            if "top_level_supervisor" in entry_lower:
                components["top_level_supervisor"].append(entry)
            elif any(quick in entry_lower for quick in ["quick_rag", "quick_llm", "quick_complexity"]):
                components["quick_requests"].append(entry)
            elif "email_generator" in entry_lower or "generate_email" in entry_lower:
                components["email_generator_entries"].append(entry)
            elif "email_sender" in entry_lower or "send_email" in entry_lower:
                components["email_sender_entries"].append(entry)
            elif "email_out" in entry_lower:
                components["email_out_entries"].append(entry)
            elif "output_supervisor" in entry_lower or "top_output" in entry_lower:
                components["output_supervisor_entries"].append(entry)
            elif "smtp" in entry_lower or "mail" in entry_lower:
                components["smtp_components"].append(entry)
            elif "routing" in entry_lower or "route" in entry_lower:
                components["routing_components"].append(entry)
            else:
                components["other_components"].append(entry)
        
        return components
    
    def _analyze_data_analysis_execution(self, call_trace: List[str]) -> Dict[str, List[str]]:
        """Analyze data analysis execution"""
        components = {
            "top_level_supervisor": [],
            "data_analysis_components": [],
            "data_retrieval_components": [],
            "data_processing_components": [],
            "rag_components": [],
            "llm_components": [],
            "output_components": [],
            "other_components": []
        }
        
        for entry in call_trace:
            entry_lower = str(entry).lower()
            
            if "top_level_supervisor" in entry_lower:
                components["top_level_supervisor"].append(entry)
            elif "data" in entry_lower and ("analysis" in entry_lower or "analyze" in entry_lower):
                components["data_analysis_components"].append(entry)
            elif "data" in entry_lower and ("retrieval" in entry_lower or "retrieve" in entry_lower):
                components["data_retrieval_components"].append(entry)
            elif "data" in entry_lower and ("process" in entry_lower or "processing" in entry_lower):
                components["data_processing_components"].append(entry)
            elif "rag" in entry_lower or "retrieval" in entry_lower:
                components["rag_components"].append(entry)
            elif "llm" in entry_lower or "language_model" in entry_lower:
                components["llm_components"].append(entry)
            elif "output" in entry_lower:
                components["output_components"].append(entry)
            else:
                components["other_components"].append(entry)
        
        return components
    
    def _analyze_general_execution(self, call_trace: List[str]) -> Dict[str, List[str]]:
        """Analyze general execution"""
        components = {
            "top_level_supervisor": [],
            "processing_components": [],
            "rag_components": [],
            "llm_components": [],
            "output_components": [],
            "other_components": []
        }
        
        for entry in call_trace:
            entry_lower = str(entry).lower()
            
            if "top_level_supervisor" in entry_lower:
                components["top_level_supervisor"].append(entry)
            elif "processing" in entry_lower or "process" in entry_lower:
                components["processing_components"].append(entry)
            elif "rag" in entry_lower or "retrieval" in entry_lower:
                components["rag_components"].append(entry)
            elif "llm" in entry_lower or "language_model" in entry_lower:
                components["llm_components"].append(entry)
            elif "output" in entry_lower:
                components["output_components"].append(entry)
            else:
                components["other_components"].append(entry)
        
        return components
    
    # =============================================================================
    # EMAIL-AGENDA COORDINATION REAL TESTS
    # =============================================================================
    
    async def test_real_email_agenda_coordination_execution(self):
        """Test real email-agenda coordination execution"""
        result = await self._get_real_execution_result("email_agenda_coordination", "email_agenda")
        
        # Basic validation
        assert result is not None
        assert "call_trace" in result
        assert "response" in result
        assert "execution_type" in result
        
        LogFire.log("DEBUG", f"[TEST] Email-agenda coordination execution type: {result['execution_type']}")
        if result["execution_type"] == "real":
            LogFire.log("DEBUG", f"[TEST] Call trace length: {len(result['call_trace'])}")
            LogFire.log("DEBUG", f"[TEST] Response preview: {result['response'][:200]}...")
        
        return result
    
    async def test_real_email_agenda_components_validation(self):
        """Test that real execution includes expected email-agenda components"""
        result = await self._get_real_execution_result("email_agenda_coordination", "email_agenda")
        
        if result["execution_type"] in ["timeout", "exception"]:
            pytest.skip(f"Real execution {result['execution_type']} - cannot validate components")
        
        call_trace = result["call_trace"]
        real_components = result["real_components"]
        
        LogFire.log("DEBUG", "[TEST] Real email-agenda execution components analysis:")
        for component_type, entries in real_components.items():
            if entries:
                LogFire.log("DEBUG", f"  {component_type}: {len(entries)} entries")
                for entry in entries[:3]:
                    LogFire.log("DEBUG", f"    - {entry}")
        
        # Validate execution flow
        assert len(call_trace) > 0, "Real execution should have call trace entries"
        
        # Check for expected coordination components
        coordination_indicators = [
            len(real_components["email_generation_components"]) > 0,
            len(real_components["agenda_planning_components"]) > 0,
            len(real_components["coordination_components"]) > 0,
            len(real_components["top_level_supervisor"]) > 0
        ]
        
        coordination_found = sum(coordination_indicators)
        LogFire.log("DEBUG", f"[TEST] Email-agenda coordination indicators found: {coordination_found}/4")
        
        assert coordination_found >= 1, (
            f"FAILED: No email-agenda coordination found in trace! "
            f"Expected coordination components, but actual trace shows: {call_trace[:10]}"
        )
        
        return real_components
    
    async def test_real_meeting_invitation_workflow(self):
        """Test real meeting invitation workflow"""
        result = await self._get_real_execution_result("meeting_invitation", "email_agenda")
        
        LogFire.log("DEBUG", f"[TEST] Meeting invitation workflow execution type: {result['execution_type']}")
        
        if result["execution_type"] == "real":
            call_trace = result["call_trace"]
            real_components = result["real_components"]
            
            # Check for meeting-specific components
            meeting_indicators = [
                any("meeting" in str(entry).lower() for entry in call_trace),
                any("invitation" in str(entry).lower() for entry in call_trace),
                len(real_components["email_generation_components"]) > 0
            ]
            
            meeting_workflow_detected = sum(meeting_indicators)
            LogFire.log("DEBUG", f"[TEST] Meeting invitation indicators: {meeting_workflow_detected}/3")
            
            assert meeting_workflow_detected >= 1, "Expected meeting invitation workflow components"
        
        return result
    
    # =============================================================================
    # AGENDA-SPECIFIC REAL TESTS
    # =============================================================================
    
    async def test_real_agenda_execution_call_trace(self):
        """Test real agenda execution call trace"""
        result = await self._get_real_execution_result("basic_agenda", "agenda")
        
        assert result is not None
        assert "call_trace" in result
        assert "response" in result
        assert "execution_type" in result
        
        LogFire.log("DEBUG", f"[TEST] Agenda execution type: {result['execution_type']}")
        if result["execution_type"] == "real":
            LogFire.log("DEBUG", f"[TEST] Call trace length: {len(result['call_trace'])}")
            LogFire.log("DEBUG", f"[TEST] Response preview: {result['response'][:200]}...")
        
        return result
    
    async def test_real_agenda_components_validation(self):
        """Test that real execution includes expected agenda components"""
        result = await self._get_real_execution_result("basic_agenda", "agenda")
        
        if result["execution_type"] in ["timeout", "exception"]:
            pytest.skip(f"Real execution {result['execution_type']} - cannot validate components")
        
        call_trace = result["call_trace"]
        real_components = result["real_components"]
        
        LogFire.log("DEBUG", "[TEST] Real agenda execution components analysis:")
        for component_type, entries in real_components.items():
            if entries:
                LogFire.log("DEBUG", f"  {component_type}: {len(entries)} entries")
                for entry in entries[:3]:
                    LogFire.log("DEBUG", f"    - {entry}")
        
        # Validate execution flow
        assert len(call_trace) > 0, "Real execution should have call trace entries"
        
        # Check for expected agenda components
        agenda_indicators = [
            len(real_components["agenda_planning_components"]) > 0,
            len(real_components["agenda_generation_components"]) > 0,
            len(real_components["calendar_tools_components"]) > 0,
            len(real_components["event_scheduling_components"]) > 0,
            len(real_components["top_level_supervisor"]) > 0
        ]
        
        agenda_components_found = sum(agenda_indicators)
        LogFire.log("DEBUG", f"[TEST] Agenda-specific components found: {agenda_components_found}/5")
        
        assert agenda_components_found >= 1, (
            f"FAILED: No agenda-specific components found in trace! "
            f"Expected agenda components, but actual trace shows: {call_trace[:10]}"
        )
        
        return real_components
    
    async def test_real_detailed_planning_workflow(self):
        """Test real detailed planning workflow"""
        result = await self._get_real_execution_result("detailed_planning", "agenda")
        
        LogFire.log("DEBUG", f"[TEST] Detailed planning workflow execution type: {result['execution_type']}")
        
        if result["execution_type"] == "real":
            call_trace = result["call_trace"]
            real_components = result["real_components"]
            
            # Check for detailed planning components
            planning_indicators = [
                any("detailed" in str(entry).lower() for entry in call_trace),
                any("comprehensive" in str(entry).lower() for entry in call_trace),
                any("agenda" in str(entry).lower() for entry in call_trace),
                len(real_components["agenda_planning_components"]) > 0
            ]
            
            detailed_planning_detected = sum(planning_indicators)
            LogFire.log("DEBUG", f"[TEST] Detailed planning indicators: {detailed_planning_detected}/4")
            
            assert detailed_planning_detected >= 1, "Expected detailed planning workflow components"
        
        return result
    
    # =============================================================================
    # EMAIL-SPECIFIC REAL TESTS
    # =============================================================================
    
    async def test_real_email_sending_execution(self):
        """Test real email sending execution"""
        result = await self._get_real_execution_result("email_sending", "email")
        
        assert result is not None
        assert "call_trace" in result
        assert "response" in result
        assert "execution_type" in result
        
        LogFire.log("DEBUG", f"[TEST] Email sending execution type: {result['execution_type']}")
        if result["execution_type"] == "real":
            LogFire.log("DEBUG", f"[TEST] Call trace length: {len(result['call_trace'])}")
            LogFire.log("DEBUG", f"[TEST] Response preview: {result['response'][:200]}...")
        
        return result
    
    async def test_real_email_routing_validation(self):
        """Test real email routing validation"""
        result = await self._get_real_execution_result("email_routing", "email")
        
        if result["execution_type"] in ["timeout", "exception"]:
            pytest.skip(f"Real execution {result['execution_type']} - cannot validate routing")
        
        call_trace = result["call_trace"]
        real_components = result["real_components"]
        
        LogFire.log("DEBUG", "[TEST] Real email routing components analysis:")
        for component_type, entries in real_components.items():
            if entries:
                LogFire.log("DEBUG", f"  {component_type}: {len(entries)} entries")
                for entry in entries[:3]:
                    LogFire.log("DEBUG", f"    - {entry}")
        
        # Check for email routing components
        routing_indicators = [
            len(real_components["email_generator_entries"]) > 0,
            len(real_components["email_out_entries"]) > 0,
            len(real_components["output_supervisor_entries"]) > 0,
            len(real_components["top_level_supervisor"]) > 0
        ]
        
        routing_found = sum(routing_indicators)
        LogFire.log("DEBUG", f"[TEST] Email routing indicators found: {routing_found}/4")
        
        # At least some routing should be present
        assert routing_found >= 1, (
            f"FAILED: No email routing found in trace! "
            f"Expected routing components, but actual trace shows: {call_trace[:10]}"
        )
        
        return real_components
    
    # =============================================================================
    # DATA ANALYSIS REAL TESTS
    # =============================================================================
    
    async def test_real_tell_me_about_data_execution(self):
        """Test real tell me about data execution"""
        result = await self._get_real_execution_result("tell_me_about_data", "data_analysis")
        
        assert result is not None
        assert "call_trace" in result
        assert "response" in result
        assert "execution_type" in result
        
        LogFire.log("DEBUG", f"[TEST] Tell me about data execution type: {result['execution_type']}")
        if result["execution_type"] == "real":
            LogFire.log("DEBUG", f"[TEST] Call trace length: {len(result['call_trace'])}")
            LogFire.log("DEBUG", f"[TEST] Response preview: {result['response'][:200]}...")
        
        return result
    
    async def test_real_data_analysis_components_validation(self):
        """Test that real execution includes expected data analysis components"""
        result = await self._get_real_execution_result("data_analysis", "data_analysis")
        
        if result["execution_type"] in ["timeout", "exception"]:
            pytest.skip(f"Real execution {result['execution_type']} - cannot validate components")
        
        call_trace = result["call_trace"]
        real_components = result["real_components"]
        
        LogFire.log("DEBUG", "[TEST] Real data analysis execution components analysis:")
        for component_type, entries in real_components.items():
            if entries:
                LogFire.log("DEBUG", f"  {component_type}: {len(entries)} entries")
                for entry in entries[:3]:
                    LogFire.log("DEBUG", f"    - {entry}")
        
        # Validate execution flow
        assert len(call_trace) > 0, "Real execution should have call trace entries"
        
        # Check for expected data analysis components
        data_indicators = [
            len(real_components["data_analysis_components"]) > 0,
            len(real_components["data_retrieval_components"]) > 0,
            len(real_components["rag_components"]) > 0,
            len(real_components["top_level_supervisor"]) > 0
        ]
        
        data_components_found = sum(data_indicators)
        LogFire.log("DEBUG", f"[TEST] Data analysis components found: {data_components_found}/4")
        
        assert data_components_found >= 1, (
            f"FAILED: No data analysis components found in trace! "
            f"Expected data components, but actual trace shows: {call_trace[:10]}"
        )
        
        return real_components
    
    # =============================================================================
    # PERFORMANCE ANALYSIS TESTS
    # =============================================================================
    
    async def test_real_execution_performance_analysis(self):
        """Test real execution performance across all system types"""
        import time
        
        start_time = time.time()
        
        # Test different system types and workflows
        test_scenarios = [
            ("email_agenda_coordination", "email_agenda"),
            ("basic_agenda", "agenda"),
            ("email_sending", "email"),
            ("tell_me_about_data", "data_analysis")
        ]
        
        workflow_results = {}
        
        for query_type, system_type in test_scenarios:
            scenario_start = time.time()
            result = await self._get_real_execution_result(query_type, system_type)
            scenario_time = time.time() - scenario_start
            
            workflow_results[f"{query_type}_{system_type}"] = {
                "execution_time": scenario_time,
                "execution_type": result["execution_type"],
                "trace_length": len(result.get("call_trace", [])),
                "system_type": system_type
            }
        
        total_time = time.time() - start_time
        
        LogFire.log("DEBUG", f"[TEST] Total real execution testing time: {total_time:.2f} seconds")
        
        for scenario_name, metrics in workflow_results.items():
            LogFire.log("DEBUG", f"[TEST] {scenario_name}: {metrics['execution_time']:.2f}s, "
                  f"type: {metrics['execution_type']}, "
                  f"trace: {metrics['trace_length']} entries")
        
        # Performance assertions
        assert total_time < 360, f"Total testing time too long: {total_time:.2f} seconds"
        
        for scenario_name, metrics in workflow_results.items():
            if metrics["execution_type"] == "real":
                assert metrics["execution_time"] < 90, (
                    f"{scenario_name} real execution too slow: {metrics['execution_time']:.2f} seconds"
                )
        
        return workflow_results
    
    async def test_real_response_quality_analysis(self):
        """Test real response quality across different system types"""
        test_scenarios = [
            ("email_agenda_coordination", "email_agenda"),
            ("basic_agenda", "agenda"),
            ("email_sending", "email"),
            ("tell_me_about_data", "data_analysis")
        ]
        
        quality_results = {}
        
        for query_type, system_type in test_scenarios:
            result = await self._get_real_execution_result(query_type, system_type)
            actual_response = result["response"]
            
            # Handle special cases
            if result["execution_type"] in ["timeout", "exception"]:
                quality_results[f"{query_type}_{system_type}"] = {
                    "response_length": len(actual_response),
                    "has_relevant_content": False,
                    "response_preview": actual_response[:200],
                    "execution_type": result["execution_type"]
                }
                continue
            
            # Quality checks for real responses
            assert isinstance(actual_response, str), "Response should be a string"
            assert len(actual_response.strip()) > 10, "Response should have meaningful content"
            
            # Check for relevant content based on system type
            response_lower = actual_response.lower()
            
            if system_type == "email_agenda":
                relevant_terms = ["email", "agenda", "calendar", "meeting", "schedule"]
            elif system_type == "agenda":
                relevant_terms = ["agenda", "meeting", "plan", "schedule", "event"]
            elif system_type == "email":
                relevant_terms = ["email", "mail", "send", "message", "recipient"]
            elif system_type == "data_analysis":
                relevant_terms = ["data", "analysis", "information", "source", "available"]
            else:
                relevant_terms = ["information", "help", "assist", "provide"]
            
            has_relevant_content = any(term in response_lower for term in relevant_terms)
            
            quality_results[f"{query_type}_{system_type}"] = {
                "response_length": len(actual_response),
                "has_relevant_content": has_relevant_content,
                "response_preview": actual_response[:200],
                "execution_type": result["execution_type"]
            }
            
            LogFire.log("DEBUG", f"[TEST] {query_type}_{system_type} response preview: {actual_response[:200]}...")
            LogFire.log("DEBUG", f"[TEST] Contains relevant content: {has_relevant_content}")
        
        return quality_results
    
    # =============================================================================
    # COMPREHENSIVE REAL EXECUTION TEST
    # =============================================================================
    
    async def test_comprehensive_real_execution_suite(self):
        """Comprehensive test covering all real execution scenarios"""
        LogFire.log("DEBUG", "\n" + "="*80)
        LogFire.log("DEBUG", "COMPREHENSIVE REAL EXECUTION SUITE")
        LogFire.log("DEBUG", "="*80)
        
        # Phase 1: System setup validation
        LogFire.log("DEBUG", "\n[PHASE 1] Testing system setup for all types...")
        system_types = ["email_agenda", "agenda", "email", "data_analysis"]
        setup_results = {}
        
        for system_type in system_types:
            setup_success = await self._setup_real_system(system_type)
            setup_results[system_type] = setup_success
            LogFire.log("DEBUG", f"[PHASE 1] {system_type} setup: {'✓' if setup_success else '✗'}")
        
        # Phase 2: Execute representative queries
        LogFire.log("DEBUG", "\n[PHASE 2] Executing representative real queries...")
        execution_results = {}
        
        representative_queries = [
            ("email_agenda_coordination", "email_agenda"),
            ("basic_agenda", "agenda"),
            ("email_sending", "email"),
            ("tell_me_about_data", "data_analysis")
        ]
        
        for query_type, system_type in representative_queries:
            if setup_results.get(system_type):
                result = await self._get_real_execution_result(query_type, system_type)
                execution_results[f"{query_type}_{system_type}"] = result
                LogFire.log("DEBUG", f"[PHASE 2] {query_type}: {result['execution_type']}")
            else:
                LogFire.log("DEBUG", f"[PHASE 2] {query_type}: SKIPPED (setup failed)")
        
        # Phase 3: Performance analysis
        LogFire.log("DEBUG", "\n[PHASE 3] Performance analysis...")
        performance_results = await self.test_real_execution_performance_analysis()
        
        # Phase 4: Quality analysis
        LogFire.log("DEBUG", "\n[PHASE 4] Quality analysis...")
        quality_results = await self.test_real_response_quality_analysis()
        
        # Phase 5: Component validation
        LogFire.log("DEBUG", "\n[PHASE 5] Component validation...")
        component_validation_results = {}
        
        for query_type, system_type in representative_queries:
            result_key = f"{query_type}_{system_type}"
            if result_key in execution_results:
                result = execution_results[result_key]
                if result["execution_type"] == "real":
                    real_components = result["real_components"]
                    component_count = sum(len(entries) for entries in real_components.values())
                    component_validation_results[result_key] = {
                        "total_components": component_count,
                        "component_types": len([k for k, v in real_components.items() if v]),
                        "has_components": component_count > 0
                    }
                    LogFire.log("DEBUG", f"[PHASE 5] {result_key}: {component_count} components, {len([k for k, v in real_components.items() if v])} types")
        
        # Final summary
        LogFire.log("DEBUG", "\n[FINAL SUMMARY] Real execution suite results:")
        LogFire.log("DEBUG", f"  System setups: {sum(setup_results.values())}/{len(setup_results)} successful")
        LogFire.log("DEBUG", f"  Executions: {len([r for r in execution_results.values() if r['execution_type'] == 'real'])}/{len(execution_results)} real")
        LogFire.log("DEBUG", f"  Performance: {len([r for r in performance_results.values() if r['execution_type'] == 'real'])}/{len(performance_results)} acceptable")
        LogFire.log("DEBUG", f"  Quality: {len([r for r in quality_results.values() if r['has_relevant_content']])}/{len(quality_results)} relevant")
        LogFire.log("DEBUG", f"  Components: {len([r for r in component_validation_results.values() if r['has_components']])}/{len(component_validation_results)} validated")
        
        LogFire.log("DEBUG", "\n" + "="*80)
        LogFire.log("DEBUG", "COMPREHENSIVE REAL EXECUTION SUITE COMPLETED")
        LogFire.log("DEBUG", "="*80)
        
        return {
            "setup_results": setup_results,
            "execution_results": execution_results,
            "performance_results": performance_results,
            "quality_results": quality_results,
            "component_validation_results": component_validation_results
        }


if __name__ == "__main__":
    # Run real execution tests manually if needed
    import asyncio
    
    async def run_manual_real_execution_test():
        test_suite = TestRealExecutionSuite()
        test_suite.setup_method()
        
        try:
            LogFire.log("DEBUG", "Running comprehensive real execution test...")
            result = await test_suite.test_comprehensive_real_execution_suite()
            LogFire.log("DEBUG", f"Manual real execution test completed: {result}", severity="debug")
        except Exception as e:
            LogFire.log("DEBUG", f"Manual real execution test failed: {str(e)}", severity="debug")
        finally:
            test_suite.teardown_method()
    
    asyncio.run(run_manual_real_execution_test())