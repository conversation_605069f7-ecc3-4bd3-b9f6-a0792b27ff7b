from imports import *

from typing import Optional
from pydantic import Field, validator
from .processing_output_data import ProcessingOutputDataBase

class EmailProcessingData(ProcessingOutputDataBase):
    """Data structure for email generation → email sending workflow"""
    
    # Email-specific fields
    subject: str = Field(..., min_length=1, description="Email subject line")
    content: str = Field(..., min_length=1, description="Email body content")  
    sender: str = Field(..., description="Sender email address")
    recipient: str = Field(..., description="Recipient email address")
    
    # Email workflow state
    content_generated: bool = Field(default=False)
    subject_generated: bool = Field(default=False)
    sender_validated: bool = Field(default=False)
    recipient_validated: bool = Field(default=False)
    
    # Optional fields
    cc_recipients: list[str] = Field(default_factory=list)
    bcc_recipients: list[str] = Field(default_factory=list) 
    attachments: list[str] = Field(default_factory=list)
    email_thread_id: Optional[str] = Field(default=None)
    
    @validator('sender', 'recipient')
    def validate_email_addresses(cls, v):
        if '@' not in v or '.' not in v.split('@')[1]:
            raise ValueError('Invalid email address format')
        return v
        
    def get_output_type(self) -> str:
        return "email"
        
    def get_preview_text(self) -> str:
        return f"""
From: {self.sender}
To: {self.recipient}
Subject: {self.subject}

{self.content}

-- 
Mail opgesteld met AskZaira
        """.strip()
        
    def to_smtp_format(self) -> dict:
        """Convert to format expected by email sending tools"""
        return {
            "subject": self.subject,
            "content": self.content, 
            "sender": self.sender,
            "recipient": self.recipient,
            "cc": self.cc_recipients,
            "bcc": self.bcc_recipients
        }