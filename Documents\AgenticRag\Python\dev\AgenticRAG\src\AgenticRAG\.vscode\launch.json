{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python Debugger: Debug dev.py",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/dev_run.py",
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}/../..",
      "justMyCode": false,
      "python": "${workspaceFolder}/../../.venv/Scripts/python.exe",
      "preLaunchTask": "startDockerDesktop"
    },
    {
      "name": "Python Debugger: Run Current Test File",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}",
      "justMyCode": false,
      "python": "${workspaceFolder}/../../.venv/Scripts/python.exe",
    }
  ]
}
