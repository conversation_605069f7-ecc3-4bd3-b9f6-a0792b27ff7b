from imports import *

from asyncio import sleep, Task, create_task, gather as asyncio_gather, CancelledError as asyncio_CancelledError
import time
from uuid import UUID, uuid4
from typing import Dict, Any, Optional, Awaitable
from random import choice, randint
from time import time as time_time
from threading import Lock
from pydantic import BaseModel, Field, ConfigDict

# SupervisorManager import moved to TYPE_CHECKING to avoid circular import
# MyBot_Generic import moved to TYPE_CHECKING to avoid circular import

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from userprofiles.ZairaUser import ZairaUser
    from endpoints.mybot_generic import MyBot_Generic
    from managers.manager_supervisors import SupervisorManager
    from tasks.data.processing_output_data import ProcessingOutputDataBase

class LongRunningZairaRequest(BaseModel):

    """Handles a single query requested by a user"""
    human_in_the_loop_callback: Optional[Awaitable] = None
    human_in_the_loop_request_message: Optional[str] = None  # Store the HITL request message
    _lock: Lock = None
    scheduled_guid: UUID = Field(default_factory=uuid4, description="Unique identifier for this scheduled request")
    chat_session_guid: UUID = Field(default_factory=uuid4, description="GUID of the ZairaChat session associated with this request")
    output_demands: list[str] = []
    complete_message: str = ""
    user: "ZairaUser" = None
    calling_bot: "MyBot_Generic" = None
    original_physical_message: Optional[Any] = None # Type-agnostic. Gets filled based on the source. DO NOT CALL OR CAST DIRECTLY!
    asyncio_Task_await_response: Task = Field(None, exclude=True)
    asyncio_Task_long_running_query: Task = Field(None, exclude=True)
    call_trace: list[str] = []
    
    # Structured data from processing tasks
    processing_data: Optional["ProcessingOutputDataBase"] = Field(None, exclude=True, description="Structured data from processing tasks")
    
    # Fields that were previously in request_status
    status: str = "pending"
    message: str = ""
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    call_trace_updated_at: Optional[float] = None
    final_call_trace_updated_at: Optional[float] = None
    
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, user: "ZairaUser", complete_message: str, calling_bot: "MyBot_Generic", original_message, scheduled_guid: Optional[UUID] = None, parent_chat_session_guid: Optional[UUID] = None):
        super().__init__()
        self._lock = Lock()
        # Use provided scheduled_guid or generate a new one
        self.scheduled_guid = scheduled_guid if scheduled_guid else uuid4()
        self.complete_message = complete_message
        self.user = user
        self.calling_bot = calling_bot
        self.output_demands.append(self.calling_bot.name)
        self.original_physical_message = original_message
        
        # Set chat session GUID based on request type
        if parent_chat_session_guid:
            # Use provided parent chat session - for execution requests from ScheduledZairaRequest
            self.chat_session_guid = parent_chat_session_guid
            # Ensure the chat session has user reference for logging
            if self.chat_session_guid in user.chat_history:
                user.chat_history[self.chat_session_guid].set_user(user)
        elif scheduled_guid:
            # This is a scheduled request - create isolated session
            self.chat_session_guid = self._create_isolated_session_for_scheduled_request()
        else:
            # This is a user-initiated request - use user's active session
            self.chat_session_guid = user.session_guid
            # Ensure the active chat session has user reference for logging
            if self.chat_session_guid in user.chat_history:
                user.chat_history[self.chat_session_guid].set_user(user)
        # Initialize status fields
        self.status = 'running'
        self.message = f'Request {str(self.scheduled_guid)} started!'
        self.started_at = time_time()
        # Get the associated chat session for logging
        chat_session = self.user.chat_history[self.chat_session_guid]
        LogFire.log("INIT", f"New request started with question of length: {len(self.complete_message)}.", f"Question: {self.complete_message}", chat=chat_session)

    async def run_request(self):
        lowered = self.complete_message.lower()
        parts = lowered.split(' ', 1)  # Split on first space
        first_word: str = parts[0]
        user_message = self.complete_message
        if len(first_word) > 0:
            if first_word[0] == "!" or first_word[0] == "?":
                if len(parts) > 1:
                    # Redefine user_message as everything after the command
                    user_message = self.complete_message[1:]

        async def long_running_query(command:str, user_message: str):
            try:
                original_status = {'status': self.status}  # Keep current status for comparison
                # if command == '':
                #     response = f'Well, you\'re awfully silent...'
                # elif 'hello' == command:
                #     response = f'Hello there!'
                # elif 'howareyou' == command:
                #     response = f'Good, thanks!'
                # elif 'rolldice' in command:
                #     response = f'You rolled: {randint(1, 6)}'
                # else 'zaira' in command:
                # Initialize variables before try block
                call_trace = []
                response = "Request failed"
                
                try:
                    await self.calling_bot.send_reply(f"Starting a process with a maximum time of {(TIMEOUT_LIMIT / 60).__ceil__()} minutes.", self, self.original_physical_message, False)
                    # Format retrieved data for the agents
                    #retrieval_summary = ""
                    # ragdata, llmdata, complexity_score = await asyncio_gather(
                    #         rag_data(Globals.get_query_engine_default(), user_message),
                    #         llm_data(user_message),
                    #         complexity_data(user_message)
                    #     )

                    #test met begin input uit
                    
                    # Run the agent architecture with our enhanced prompt
                    from managers.manager_supervisors import SupervisorManager
                    top_level_supervisor = SupervisorManager.get_supervisor("top_level_supervisor")
                    
                    # Create a background async task to periodically capture call traces during execution
                    import asyncio
                    from etc.helper_functions import handle_asyncio_task_result_errors
                    
                    call_trace_monitor_active = True
                    
                    async def monitor_supervisor_call_traces():
                        """Monitor and capture call traces during supervisor execution"""
                        try:
                            while call_trace_monitor_active:
                                await asyncio.sleep(1)  # Check every second
                                
                                # Try to get partial call traces from supervisor if available
                                try:
                                    if hasattr(top_level_supervisor, '_last_call_trace'):
                                        partial_call_trace = top_level_supervisor._last_call_trace
                                        if partial_call_trace:
                                            # Update self.call_trace with partial call trace
                                            self.call_trace = partial_call_trace
                                            self.call_trace_updated_at = time_time()
                                except Exception:
                                    pass  # Ignore errors accessing partial traces
                                    
                        except asyncio.CancelledError:
                            pass  # Clean cancellation
                        except Exception as e:
                            from etc.helper_functions import exception_triggered
                            # Get user's chat session for exception logging
                            chat_session = self.user.chat_history.get(self.chat_session_guid) if self.user else None
                            exception_triggered(e, "monitor_supervisor_call_traces", chat_session)
                    
                    # Start call trace monitoring
                    monitor_task = asyncio.create_task(monitor_supervisor_call_traces())
                    # Register error handler as callback when async task completes
                    monitor_task.add_done_callback(handle_asyncio_task_result_errors)
                    
                    try:
                        result = await top_level_supervisor.call_supervisor(user_message, self.user, self.scheduled_guid, chat_session_guid=self.chat_session_guid, original_source=self.calling_bot.name) #  self.user,llmdata , ComplexityScore=complexity_score
                        response = result["result"]
                        call_trace = result["call_trace"]
                    finally:
                        # Stop call trace monitoring
                        call_trace_monitor_active = False
                        if not monitor_task.done():
                            monitor_task.cancel()
                            try:
                                await monitor_task
                            except asyncio.CancelledError:
                                pass

                    # Print the result
                    async def wait_for_no_active_request_python_log(self: "LongRunningZairaRequest", call_trace):
                        try:
                            while True:
                                # Wait with outputting to the Python log until there's nothing being logged anymore
                                await sleep(1)
                                if not self.user.has_active_requests():
                                    break
                            # print("Call trace:")
                            # for call in call_trace:
                            #     print(call)
                        except asyncio_CancelledError:
                            # Clean cancellation
                            raise
                        except Exception as e:
                            from etc.helper_functions import exception_triggered
                            # Get user's chat session for exception logging
                            chat_session = self.user.chat_history.get(self.chat_session_guid) if self.user else None
                            exception_triggered(e, "wait_for_no_active_request_python_log", chat_session)
                            raise
                    
                    # Update self.call_trace with final call_trace and debug_messages after execution
                    self.call_trace = call_trace
                    self.final_call_trace_updated_at = time_time()
                    
                    
                    # No longer needed - fields are set directly
                    
                    self.asyncio_Task_long_running_query = create_task(wait_for_no_active_request_python_log(self, result["call_trace"]))
                    self.asyncio_Task_long_running_query.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
                except Exception as e:
                    etc.helper_functions.exception_triggered(e)
                    response = f"{e}"
                    
                    # Try to capture partial call trace from supervisor manager if available
                    try:
                        from managers.manager_supervisors import SupervisorManager
                        top_level_supervisor = SupervisorManager.get_supervisor("top_level_supervisor")
                        if hasattr(top_level_supervisor, '_last_call_trace'):
                            call_trace = top_level_supervisor._last_call_trace
                    except:
                        pass  # If we can't get partial call trace, keep empty list
                # Check if status hasn't changed since we started
                if self.status == original_status.get('status', 'running'):
                    self.call_trace = call_trace
                    self.status = "completed"
                    self.message = response
                    self.completed_at = time.time()
            except Exception as e:
                self.status = "failed"
                self.message = f'Request {self.scheduled_guid} failed: {str(e)}'
                self.completed_at = time.time()
            return

        # Define handler for zaira query
        try:
            await long_running_query(command=first_word, user_message=user_message) # Separate function to ensure we handle end-of-life calls
        except asyncio_CancelledError:
            # Clean cancellation
            self.status = "cancelled"
            self.message = f'Request {self.scheduled_guid} was cancelled'
            self.completed_at = time.time()
            raise
        except Exception as e:
            from etc.helper_functions import exception_triggered
            # Get user's chat session for exception logging
            chat_session = self.user.chat_history.get(self.chat_session_guid) if self.user else None
            exception_triggered(e, "run_request", chat_session)
            self.status = "failed"
            self.message = f'Request {self.scheduled_guid} failed: {str(e)}'
            self.completed_at = time.time()
            raise


    async def on_message(self, complete_message: str):
        self.on_message(complete_message=complete_message, calling_bot=self.calling_bot, attachments=[], original_message=self.original_physical_message) 
    async def on_message(self, complete_message: str, calling_bot: "MyBot_Generic", original_message = None, reply_context = None): # Second version exists to keep consistency with the ZairaUser function equivalent. That one needs the parameters for start_request
        if self.human_in_the_loop_callback == None:
            # Request is still running, do not allow a new one
            await self.calling_bot.send_reply("Zaira is currently working, please hold", self, self.original_physical_message, False)
        else:
            # We're expecting human-in-the-loop, feed it into the callback
            hitl_callback = self.human_in_the_loop_callback
            self.human_in_the_loop_callback = None
            response = await hitl_callback(self, complete_message)

    async def request_human_in_the_loop(self, request: str, callback: Awaitable, halt_until_response = False):
        chat_session = self.user.chat_history[self.chat_session_guid]
        LogFire.log("DEBUG", f"[LongRunningZairaRequest] HITL requested: {request[:100]}", chat=chat_session)
        LogFire.log("DEBUG", f"[LongRunningZairaRequest] Request GUID: {self.scheduled_guid}", chat=chat_session)
        LogFire.log("DEBUG", f"[LongRunningZairaRequest] Bot: {self.calling_bot.name if self.calling_bot else 'No bot'}", chat=chat_session)
        
        if self.human_in_the_loop_callback != None:
            raise RuntimeError(f"Request '{request}' for human-in-the-loop while we're still waiting on user input")
        self.human_in_the_loop_callback = callback
        self.human_in_the_loop_request_message = request  # Store the request for debugging
        
        if not self.calling_bot:
            chat_session = self.user.chat_history[self.chat_session_guid]
            LogFire.log("ERROR", "[LongRunningZairaRequest] No calling_bot available for HITL", chat=chat_session)
            raise RuntimeError("No bot available to handle HITL request")
            
        chat_session = self.user.chat_history[self.chat_session_guid]
        LogFire.log("DEBUG", "[LongRunningZairaRequest] Calling bot's request_human_in_the_loop_internal", chat=chat_session)
        await self.calling_bot.request_human_in_the_loop_internal(request=request, request_obj=self, message=self.original_physical_message, halt_until_response=halt_until_response)
        
    async def cleanup_tasks(self):
        """Cancel and cleanup all running async tasks"""
        tasks_to_cancel = []
        
        if self.asyncio_Task_await_response and not self.asyncio_Task_await_response.done():
            tasks_to_cancel.append(self.asyncio_Task_await_response)
            
        if self.asyncio_Task_long_running_query and not self.asyncio_Task_long_running_query.done():
            tasks_to_cancel.append(self.asyncio_Task_long_running_query)
        
        for task in tasks_to_cancel:
            task.cancel()
        
        # Wait for cancellation to complete
        if tasks_to_cancel:
            await asyncio_gather(*tasks_to_cancel, return_exceptions=True)
    
    async def await_status_complete(self, wait_on_complete = False):
        async def long_running_query(self: "LongRunningZairaRequest"):
            try:
                while True:
                    await sleep(1)
                    if self.status != "running":
                        # Request completed - use the main completion flow instead of _handle_response
                        if self.status == "completed":
                            await self.send_response(self.message, True, self.call_trace)
                        await _handle_response(self, self.message, "\n".join(self.call_trace if self.call_trace else []))
                        break
            except asyncio_CancelledError:
                # Clean cancellation
                raise
            except Exception as e:
                from etc.helper_functions import exception_triggered
                # Get user's chat session for exception logging
                chat_session = self.user.chat_history.get(self.chat_session_guid) if self.user else None
                exception_triggered(e, "long_running_query", chat_session)
                raise

        async def _handle_response(self: "LongRunningZairaRequest", message: str, call_trace: str):
            #output_supervisor = SupervisorManager.get_supervisor("top_output_supervisor")
            # Add call trace
            #await output_supervisor.call_supervisor(query=message, user=self.user, OriginalSource = self.calling_bot.name, OriginalQuery=self.complete_message, CallTrace = call_trace)
            # In case the output supervisor is requesting HITML, don't end the request yet!
            if self.human_in_the_loop_callback == None:
                # Remove the reference to this request so that a new one can be started
                self.user.remove_request(self.scheduled_guid)
            else:
                self.status = "running"
                self.asyncio_Task_await_response = create_task(long_running_query(self))
                self.asyncio_Task_await_response.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
            
        if self.status == "running":
            if wait_on_complete == True:
                await long_running_query(self)
            else:
                # Always use non-blocking approach for scheduled requests to prevent system freeze
                # Previous debug mode blocking (Globals.is_debug() check) was causing scheduled requests
                # to freeze the entire system when wait_on_complete=False was requested
                # Create new thread that waits for the message to be completed and then sends it
                self.asyncio_Task_await_response = create_task(long_running_query(self))
                self.asyncio_Task_await_response.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
        elif self.status == "completed":
            # Task already completed - just clean up
            await _handle_response(self, self.message, "\n".join(self.call_trace if self.call_trace else []))
        else:
            await self.send_response("Last request has failed.", False)

    async def send_response(self, answer: str, add_to_chat_history = True, call_trace = None):
        # If call_trace is provided, store it in self.call_trace
        if call_trace and add_to_chat_history:
            self.call_trace = call_trace
        await self.calling_bot.send_reply(answer, self, self.original_physical_message, add_to_chat_history=add_to_chat_history)
    
    def _create_isolated_session_for_scheduled_request(self) -> UUID:
        """
        Create an isolated ZairaChat session for this scheduled request.
        This prevents scheduled requests from interfering with the user's active chat session.
        """
        from userprofiles.ZairaChat import ZairaChat, ChatSessionType
        
        new_session_guid = uuid4()
        
        # Create a descriptive title from the request content
        content_preview = self.complete_message[:50] if self.complete_message else "Scheduled Request"
        if len(self.complete_message) > 50:
            content_preview += "..."
        
        scheduled_chat = ZairaChat(
            session_guid=new_session_guid,
            user_guid=str(self.user.user_guid),
            conversation_guid=new_session_guid,
            session_type=ChatSessionType.SCHEDULED,
            title=content_preview
        )
        
        # Add metadata to link this session to the scheduled request
        scheduled_chat.metadata['scheduled_guid'] = str(self.scheduled_guid)
        scheduled_chat.metadata['created_for'] = 'scheduled_request_isolation'
        
        # Store the isolated session in user's chat history
        self.user.chat_history[new_session_guid] = scheduled_chat
        
        # Set the user reference in the chat for logging
        scheduled_chat.set_user(self.user)
        
        LogFire.log("USER", f"Created isolated chat session {new_session_guid} for scheduled request {self.scheduled_guid}", chat=scheduled_chat)
        
        return new_session_guid

    def add_processing_output(self, data: "ProcessingOutputDataBase") -> None:
        """Add structured processing data and update output demands"""
        self.processing_data = data
        output_type = data.get_output_type()
        if output_type not in self.output_demands:
            self.output_demands.append(output_type)
            chat_session = self.user.chat_history.get(self.chat_session_guid) if self.user else None
            LogFire.log("DEBUG", f"[LongRunningZairaRequest] Added '{output_type}' to output_demands via processing_data", chat=chat_session)

    async def complete_request(self) -> None:
        """Mark this request as complete and remove it from user's request list"""
        if hasattr(self.user, 'remove_request'):
            self.user.remove_request(self.scheduled_guid)
            LogFire.log("EVENT", f"Request {self.scheduled_guid} completed and removed from user request list")

# Import after class definition to avoid circular import during class definition
from userprofiles.ZairaUser import ZairaUser
from endpoints.mybot_generic import MyBot_Generic

# Import after class definition to avoid circular import during class definition  
try:
    from tasks.data.processing_output_data import ProcessingOutputDataBase
except ImportError:
    # Handle case where import may not be available during initialization
    ProcessingOutputDataBase = None

# Model rebuild handled in userprofiles/__init__.py after all classes are defined
