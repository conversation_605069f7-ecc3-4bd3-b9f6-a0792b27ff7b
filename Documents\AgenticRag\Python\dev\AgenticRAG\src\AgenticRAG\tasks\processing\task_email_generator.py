from imports import *

from langchain_core.tools import BaseTool
from typing import Optional
import json

from managers.manager_supervisors import Supervisor<PERSON>ana<PERSON>, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaTask import LongRunningZairaTask


class EmailGeneratorTool(BaseTool):
    """Tool for generating email content with proper formatting and validation"""
    name: str = "email_generator_tool"
    description: str = "Generates professional email content with subject, body, sender and recipient validation"
    
    def _run(self, content_request: str, subject_hint: Optional[str] = None, sender: Optional[str] = None, recipient: Optional[str] = None, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, content_request: str, subject_hint: Optional[str] = None, sender: Optional[str] = None, recipient: Optional[str] = None, state: SupervisorTaskState = None) -> str:
        """Generates professional email content based on the request"""
        try:
            user = await ZairaUserManager.get_instance().find_user(state.user_guid)
            
            # Determine sender email
            final_sender = sender
            if not final_sender or '@' not in final_sender:
                if user.email:
                    final_sender = user.email
                else:
                    # Request sender email from user
                    async def set_user_email(task: LongRunningZairaTask, response: str):
                        user.email = response
                        nonlocal final_sender
                        final_sender = response
                    
                    await user.my_task.request_human_in_the_loop(
                        "Je e-mail adres is nog niet bekend bij ons. Van welk e-mailadres moet de mail verzonden worden?", 
                        set_user_email, 
                        True
                    )
            
            # Determine recipient email
            final_recipient = recipient
            if not final_recipient or '@' not in final_recipient:
                async def set_recipient(task: LongRunningZairaTask, response: str):
                    if response and '@' in response:
                        nonlocal final_recipient
                        final_recipient = response
                    else:
                        # Invalid email, ask again
                        await user.my_task.request_human_in_the_loop(
                            f"'{response}' is geen geldig e-mailadres. Geef een geldig e-mailadres op (bijvoorbeeld: <EMAIL>):", 
                            set_recipient, 
                            True
                        )
                
                await user.my_task.request_human_in_the_loop(
                    "Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?", 
                    set_recipient, 
                    True
                )
            
            # Generate email subject if not provided
            final_subject = subject_hint
            if not final_subject:
                # Use LLM to generate appropriate subject based on content request
                from langchain_openai import ChatOpenAI
                from langchain.prompts import ChatPromptTemplate
                from langchain_core.output_parsers import StrOutputParser
                
                subject_prompt = ChatPromptTemplate.from_template(
                    "Generate a professional and concise email subject line for the following email request:\n\n"
                    "Email request: {content_request}\n\n"
                    "Subject line (maximum 50 characters, professional tone):"
                )
                
                llm = ChatOpenAI(temperature=0.3)
                subject_chain = subject_prompt | llm | StrOutputParser()
                
                final_subject = await subject_chain.ainvoke({"content_request": content_request})
                final_subject = final_subject.strip().strip('"').strip("'")
            
            # Generate email content using LLM
            from langchain_openai import ChatOpenAI
            from langchain.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            
            content_prompt = ChatPromptTemplate.from_template(
                "Generate a professional email body based on the following request. "
                "The email should be polite, clear, and appropriate for business communication.\n\n"
                "Email request: {content_request}\n"
                "Sender: {sender}\n"
                "Recipient: {recipient}\n\n"
                "Generate only the email body content (no subject line, no headers):"
            )
            
            llm = ChatOpenAI(temperature=0.7)
            content_chain = content_prompt | llm | StrOutputParser()
            
            generated_content = await content_chain.ainvoke({
                "content_request": content_request,
                "sender": final_sender,
                "recipient": final_recipient
            })
            
            # Create structured email data
            email_data = {
                "subject": final_subject,
                "content": generated_content.strip(),
                "sender": final_sender,
                "recipient": final_recipient,
                "generated_at": datetime.now().isoformat(),
                "user_guid": state.user_guid
            }
            
            # Store email data in state for the sending task
            if not hasattr(state, 'sections'):
                state.sections = {}
            state.sections['generated_email'] = email_data
            
            # Create formatted email preview
            email_preview = f"""
From: {final_sender}
To: {final_recipient}
Subject: {final_subject}

{generated_content}

-- 
Mail opgesteld met AskZaira
            """.strip()
            
            # Request user approval
            async def handle_approval(task: LongRunningZairaTask, response: str):
                if response and response.lower().startswith('j'):
                    # User approved - email ready for sending
                    state.sections['email_approved'] = True
                else:
                    # User wants to edit
                    async def handle_edited_content(task: LongRunningZairaTask, edited_response: str):
                        if edited_response and edited_response.lower().strip() != 'nee':
                            # Update email content with user's edits
                            email_data['content'] = edited_response
                            state.sections['generated_email'] = email_data
                            state.sections['email_approved'] = True
                        else:
                            state.sections['email_approved'] = False
                    
                    await user.my_task.request_human_in_the_loop(
                        f"Kopieer de mail en pas hem aan zodat ik hem kan genereren. Reageer met 'nee' als je het opstellen wilt afbreken.\n\n{generated_content}",
                        handle_edited_content,
                        True
                    )
            
            await user.my_task.request_human_in_the_loop(
                f"Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)\n\n{email_preview}",
                handle_approval,
                True
            )
            
            return json.dumps(email_data, indent=2)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "email_generator_tool", state.user_guid if state else None)
            return f"Error generating email: {e}"


# Create tool instance
email_generator_tool = EmailGeneratorTool()


async def create_supervisor_email_generator() -> SupervisorSupervisor:
    """Create supervisor for email generation tasks"""
    class TaskCreator:
        email_generation_task: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.email_generation_task = SupervisorManager.get_instance().register_task(
                SupervisorTask_SingleAgent(
                    name="email_generator_task", 
                    tools=[email_generator_tool], 
                    prompt_id="Task_EmailGenerator_Agent"
                )
            )
        
        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.get_instance().register_supervisor(
                SupervisorSupervisor(
                    name="email_generation_supervisor", 
                    prompt_id="Task_EmailGenerator_Supervisor"
                )
            ).add_task(self.email_generation_task).compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()