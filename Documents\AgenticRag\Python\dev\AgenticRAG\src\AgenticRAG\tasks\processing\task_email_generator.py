from imports import *

from langchain_core.tools import BaseTool
from typing import Optional
import json
from datetime import datetime, timezone

from managers.manager_supervisors import Supervisor<PERSON>anager, SupervisorTask_SingleAgent, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from tasks.data import EmailProcessingData


class EmailGeneratorTool(BaseTool):
    """Tool for generating email content with proper formatting and validation"""
    name: str = "email_generator_tool"
    description: str = "Generates professional email content with subject, body, sender and recipient validation. " \
                      "ALWAYS use this tool for ANY email request. Pass the user's request as content_request parameter. " \
                      "Extract recipient from request if available (e.g., 'send <NAME_EMAIL>' -> recipient='<EMAIL>'). " \
                      "Tool handles missing information by requesting human input."
    
    def _run(self, content_request: str, subject_hint: Optional[str] = None, sender: Optional[str] = None, recipient: Optional[str] = None, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, content_request: str, subject_hint: Optional[str] = None, sender: Optional[str] = None, recipient: Optional[str] = None, state: SupervisorTaskState = None) -> str:
        """Generates professional email content based on the request"""
        try:
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Starting email generation with content_request: {content_request}", chat=None)
            LogFire.log("DEBUG", f"[EmailGeneratorTool] subject_hint: {subject_hint}, sender: {sender}, recipient: {recipient}", chat=None)
            LogFire.log("DEBUG", f"[EmailGeneratorTool] state.user_guid: {state.user_guid if state else 'No state'}", chat=None)
            
            user = await ZairaUserManager.get_instance().find_user(state.user_guid)
            # Get user's current chat session for logging
            chat_session = user.get_current_chat_session() if user else None
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Found user: {user.username if user else 'No user'}", chat=chat_session)
            
            # Determine sender email
            final_sender = sender
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Initial final_sender: {final_sender}", chat=chat_session)
            if not final_sender or '@' not in final_sender:
                LogFire.log("DEBUG", f"[EmailGeneratorTool] No valid sender, checking user.email: {user.email}", chat=chat_session)
                if user.email:
                    final_sender = user.email
                    LogFire.log("DEBUG", f"[EmailGeneratorTool] Using user email as sender: {final_sender}", chat=chat_session)
                else:
                    # Request sender email from user
                    LogFire.log("DEBUG", "[EmailGeneratorTool] Requesting sender email from user via HITL", chat=chat_session)
                    async def set_user_email(task: LongRunningZairaRequest, response: str):
                        user.email = response
                        nonlocal final_sender
                        final_sender = response
                        LogFire.log("DEBUG", f"[EmailGeneratorTool] HITL response for sender: {response}", chat=chat_session)
                    
                    await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                        "Je e-mail adres is nog niet bekend bij ons. Van welk e-mailadres moet de mail verzonden worden?", 
                        set_user_email, 
                        True
                    )
                    LogFire.log("DEBUG", "[EmailGeneratorTool] HITL request for sender completed", chat=chat_session)
            
            # Determine recipient email
            final_recipient = recipient
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Initial final_recipient: {final_recipient}", chat=chat_session)
            if not final_recipient or '@' not in final_recipient:
                async def set_recipient(task: LongRunningZairaRequest, response: str):
                    if response and '@' in response:
                        nonlocal final_recipient
                        final_recipient = response
                    else:
                        # Invalid email, ask again
                        # Get user's active task to request human input
                        await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                            f"'{response}' is geen geldig e-mailadres. Geef een geldig e-mailadres op (bijvoorbeeld: <EMAIL>):", 
                            set_recipient, 
                            True
                        )

                LogFire.log("DEBUG", "[EmailGeneratorTool] Requesting recipient email from user via HITL", chat=chat_session)
                await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                    "Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?", 
                    set_recipient, 
                    True
                )
                LogFire.log("DEBUG", "[EmailGeneratorTool] HITL request for recipient completed", chat=chat_session)
            
            # Generate email subject if not provided
            final_subject = subject_hint
            if not final_subject:
                # Use LLM to generate appropriate subject based on content request
                from langchain_openai import ChatOpenAI
                from langchain.prompts import ChatPromptTemplate
                from langchain_core.output_parsers import StrOutputParser
                
                subject_prompt = ChatPromptTemplate.from_template(
                    "Generate a professional and concise email subject line for the following email request:\n\n"
                    "Email request: {content_request}\n\n"
                    "Subject line (maximum 50 characters, professional tone):"
                )
                
                llm = ChatOpenAI(temperature=0.3)
                subject_chain = subject_prompt | llm | StrOutputParser()
                
                final_subject = await subject_chain.ainvoke({"content_request": content_request})
                final_subject = final_subject.strip().strip('"').strip("'")
            
            # Generate email content using LLM
            LogFire.log("DEBUG", "[EmailGeneratorTool] Starting email content generation with LLM", chat=chat_session)
            from langchain_openai import ChatOpenAI
            from langchain.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            
            content_prompt = ChatPromptTemplate.from_template(
                "Generate a professional email body based on the following request. "
                "The email should be polite, clear, and appropriate for business communication.\n\n"
                "Email request: {content_request}\n"
                "Sender: {sender}\n"
                "Recipient: {recipient}\n\n"
                "Generate only the email body content (no subject line, no headers):"
            )
            
            llm = ChatOpenAI(temperature=0.7)
            content_chain = content_prompt | llm | StrOutputParser()
            
            generated_content = await content_chain.ainvoke({
                "content_request": content_request,
                "sender": final_sender,
                "recipient": final_recipient
            })
            
            # Create structured email data using EmailProcessingData
            email_data = EmailProcessingData(
                subject=final_subject,
                content=generated_content.strip(),
                sender=final_sender,
                recipient=final_recipient,
                content_generated=True,
                subject_generated=True,
                sender_validated=True,
                recipient_validated=True,
                processing_metadata={
                    "generated_at": datetime.now(timezone.utc).isoformat(),
                    "user_guid": state.user_guid
                }
            )
            
            # Use structured data's preview method
            email_preview = email_data.get_preview_text()
            
            # Request user approval
            LogFire.log("DEBUG", f"[EmailGeneratorTool] About to request user approval for email preview", chat=chat_session)
            async def add_to_output():
                # Mark email as approved and add to processing data
                email_data.mark_approved()
                user.my_requests[state.scheduled_guid].add_processing_output(email_data)
                LogFire.log("DEBUG", f"[EmailGeneratorTool] Added EmailProcessingData to processing_data. Current demands: {user.my_requests[state.scheduled_guid].output_demands}", chat=chat_session)
            async def handle_approval(task: LongRunningZairaRequest, response: str):
                if response and response.lower().startswith('j'):
                    # User approved - email ready for sending
                    await add_to_output()
                else:
                    # User wants to edit
                    async def handle_edited_content(task: LongRunningZairaRequest, edited_response: str):
                        if edited_response and edited_response.lower().strip() != 'nee':
                            # Update email content with user's edits - create new EmailProcessingData with updated content
                            updated_email_data = EmailProcessingData(
                                subject=email_data.subject,
                                content=edited_response,
                                sender=email_data.sender,
                                recipient=email_data.recipient,
                                content_generated=True,
                                subject_generated=email_data.subject_generated,
                                sender_validated=email_data.sender_validated,
                                recipient_validated=email_data.recipient_validated,
                                processing_metadata=email_data.processing_metadata
                            )
                            # Update the email_data reference
                            email_data.content = edited_response
                            await add_to_output()
                        # If user cancels, we don't add to output
                    
                    # Get user's active task to request human input
                    await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                        f"Kopieer de mail en pas hem aan zodat ik hem kan genereren. Reageer met 'nee' als je het opstellen wilt afbreken.\n\n{generated_content}",
                        handle_edited_content,
                        True
                    )
            
            # Get user's active task to request human input
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Requesting HITL approval for email", chat=chat_session)
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Email preview: {email_preview[:100]}...", chat=chat_session)
            await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                f"Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)\n\n{email_preview}",
                handle_approval,
                True
            )
            LogFire.log("DEBUG", "[EmailGeneratorTool] HITL approval request completed", chat=chat_session)
            
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Returning EmailProcessingData: {email_data.dict()}", chat=chat_session)
            return email_data.model_dump_json(indent=2)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "email_generator_tool", state.user_guid if state else None)
            return f"Error generating email: {e}"


# Create tool instance
email_generator_tool = EmailGeneratorTool()


async def create_task_email_generator() -> SupervisorTask_SingleAgent:
    """Create supervisor for email generation tasks"""

    return SupervisorManager.get_instance().register_task(
                SupervisorTask_SingleAgent(
                    name="email_generator_task", 
                    tools=[email_generator_tool], 
                    prompt="You are an email generation specialist. Your task is to create professional, well-structured emails based on user requests. "
                           "When users request email creation or sending, use the email_generator_tool to generate proper email content. "
                           "The tool handles subject generation, formatting, sender/recipient validation, and user approval. "
                           "If the request is unclear or you need more information to generate a meaningful email, you may ask for clarification first. "
                           "For clear email requests (like 'send <NAME_EMAIL>'), use the tool directly to generate and process the email. "
                           "Use professional tone and ensure all emails are properly formatted and validated before sending."
                )
            )