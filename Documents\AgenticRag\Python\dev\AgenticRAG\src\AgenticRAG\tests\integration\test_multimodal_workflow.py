# tests/integration/test_multimodal_workflow.py
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
from managers.manager_multimodal import MultimodalManager
from managers.manager_qdrant import QDrantManager
from managers.manager_retrieval import RetrievalManager
from managers.manager_meltano import MeltanoManager
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
import tempfile
import shutil
from pathlib import Path

class TestMultimodalWorkflow:
    """Integration tests for complete multimodal processing workflow"""
    
    @pytest_asyncio.fixture
    async def setup_managers(self):
        """Setup all required managers for integration testing"""
        # Setup managers
        multimodal_manager = MultimodalManager.get_instance()
        await multimodal_manager.setup()

        qdrant_manager = QDrantManager.get_instance()
        retrieval_manager = RetrievalManager.get_instance()
        meltano_manager = MeltanoManager.get_instance()

        return {
            "multimodal": multimodal_manager,
            "qdrant": qdrant_manager,
            "retrieval": retrieval_manager,
            "meltano": meltano_manager
        }
    
    @pytest.fixture
    def sample_document_content(self):
        """Sample document content for testing"""
        return {
            "text_elements": [
                {"id": "text_1", "type": "Title", "text": "Sample Document Title"},
                {"id": "text_2", "type": "NarrativeText", "text": "This document contains various types of content including images and tables."}
            ],
            "images": [
                {
                    "id": "img_1",
                    "type": "Image",
                    "text": "Chart showing quarterly sales",
                    "summary": "A bar chart displaying quarterly sales data for 2024, showing increasing trends in Q3 and Q4",
                    "asset_path": "/assets/documents/test_doc/img_1_hash123.png",
                    "has_asset": True
                }
            ],
            "tables": [
                {
                    "id": "tbl_1",
                    "type": "Table",
                    "text": "Sales data table",
                    "summary": "Quarterly sales figures broken down by region, showing North region leading with $150K in Q4",
                    "markdown": "| Quarter | North | South | East | West |\n| --- | --- | --- | --- | --- |\n| Q1 | 100K | 80K | 90K | 70K |\n| Q2 | 120K | 90K | 95K | 75K |\n| Q3 | 140K | 100K | 105K | 85K |\n| Q4 | 150K | 110K | 115K | 95K |",
                    "has_structure": True,
                    "key_info": {
                        "headers": ["Quarter", "North", "South", "East", "West"],
                        "num_columns": 5,
                        "num_rows": 4,
                        "column_types": {
                            "Quarter": "text",
                            "North": "numeric",
                            "South": "numeric", 
                            "East": "numeric",
                            "West": "numeric"
                        }
                    }
                }
            ],
            "figures": [],
            "captions": [],
            "all_elements": []
        }
    
    @pytest.fixture
    def temp_test_directory(self):
        """Create temporary directory for test files"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        # Cleanup
        shutil.rmtree(temp_dir)
    
    @pytest.mark.asyncio
    async def test_multimodal_chunking_workflow(self, setup_managers, sample_document_content):
        """Test complete multimodal chunking preserves context"""
        managers = setup_managers
        retrieval_manager = managers["retrieval"]
        
        # Test multimodal chunking
        chunks = await retrieval_manager.chunk_multimodal_elements(
            sample_document_content,
            chunk_size=1000,
            chunk_overlap=200
        )
        
        # Verify chunks were created
        assert len(chunks) > 0
        
        # Verify multimodal markers are preserved
        combined_chunks = " ".join(chunks)
        assert "[IMAGE:" in combined_chunks
        assert "[TABLE:" in combined_chunks
        assert "quarterly sales data" in combined_chunks.lower()
        assert "sales figures" in combined_chunks.lower()
    
    @pytest.mark.asyncio
    @patch('managers.manager_qdrant.Globals')
    @patch('managers.manager_qdrant.QDrantManager.upsert')
    async def test_end_to_end_storage_workflow(self, mock_upsert, mock_globals,
                                              setup_managers, sample_document_content):
        """Test complete end-to-end storage workflow"""
        managers = setup_managers
        qdrant_manager = managers["qdrant"]
        retrieval_manager = managers["retrieval"]
        
        # Mock dependencies
        mock_node = MagicMock()
        mock_upsert.return_value = mock_node
        
        mock_index = MagicMock()
        mock_globals.get_index.return_value = mock_index
        
        # Test complete workflow
        doc_id = str(uuid4())
        
        # 1. Create chunks
        chunks = await retrieval_manager.chunk_multimodal_elements(sample_document_content)
        
        # 2. Store in vector database
        base_metadata = {
            "file_name": "test_document.pdf",
            "file_type": "application/pdf",
            "file_size": 2048000
        }
        
        nodes_created = await qdrant_manager.upsert_multimodal(
            doc_id=doc_id,
            text_chunks=chunks,
            multimodal_data=sample_document_content,
            base_metadata=base_metadata
        )
        
        # Verify workflow executed
        assert nodes_created > 0
        assert mock_upsert.call_count > 0
        mock_index.insert_nodes.assert_called_once()
        
        # Verify different types of nodes were created
        upsert_calls = mock_upsert.call_args_list
        
        # Should have text chunks, image summary, and table summary nodes
        call_ids = [call[1]['id'] for call in upsert_calls]
        
        # Check for different node types
        chunk_nodes = [id for id in call_ids if '_chunk_' in id]
        image_nodes = [id for id in call_ids if '_image_' in id]
        table_nodes = [id for id in call_ids if '_table_' in id]
        
        assert len(chunk_nodes) > 0
        assert len(image_nodes) > 0
        assert len(table_nodes) > 0
    
    @pytest.mark.asyncio
    @patch('managers.manager_qdrant.Globals')
    async def test_multimodal_search_workflow(self, mock_globals, setup_managers):
        """Test complete multimodal search workflow"""
        managers = setup_managers
        qdrant_manager = managers["qdrant"]
        
        # Mock search results
        mock_response = MagicMock()
        mock_response.__str__ = lambda x: "Found relevant multimodal content about sales data"
        
        mock_query_engine = MagicMock()
        mock_query_engine.query.return_value = mock_response
        
        mock_index = MagicMock()
        mock_index.as_query_engine.return_value = mock_query_engine
        mock_globals.get_index.return_value = mock_index
        
        # Test different search scenarios
        
        # 1. General multimodal search
        result = await qdrant_manager.search_multimodal(
            query="sales data",
            limit=5
        )
        assert result is not None
        
        # 2. Image-specific search
        result = await qdrant_manager.search_multimodal(
            query="charts and graphs",
            has_images=True,
            content_type="image_summary",
            limit=3
        )
        assert result is not None
        
        # 3. Table-specific search
        result = await qdrant_manager.search_multimodal(
            query="quarterly figures",
            has_tables=True,
            content_type="table_summary", 
            limit=3
        )
        assert result is not None
        
        # Verify search methods were called
        assert mock_index.as_query_engine.call_count == 3
        assert mock_query_engine.query.call_count == 3
    
    @pytest.mark.asyncio
    @patch('managers.manager_meltano.MultimodalManager')
    @patch('managers.manager_meltano.QDrantManager')
    @patch('managers.manager_meltano.RetrievalManager')
    async def test_meltano_multimodal_integration(self, mock_retrieval, mock_qdrant,
                                                 mock_multimodal, setup_managers,
                                                 temp_test_directory, sample_document_content):
        """Test Meltano manager multimodal integration"""
        managers = setup_managers
        meltano_manager = managers["meltano"]
        
        # Create test file
        test_file = temp_test_directory / "test_document.txt"
        test_file.write_text("Sample document content for testing")
        
        # Mock manager methods
        mock_multimodal_instance = MagicMock()
        mock_multimodal_instance.extract_multimodal_elements.return_value = sample_document_content
        mock_multimodal.get_instance.return_value = mock_multimodal_instance
        
        mock_retrieval_instance = MagicMock()
        mock_retrieval_instance.chunk_multimodal_elements.return_value = ["Chunk 1", "Chunk 2"]
        mock_retrieval.get_instance.return_value = mock_retrieval_instance
        
        mock_qdrant_instance = MagicMock()
        mock_qdrant_instance.upsert_multimodal.return_value = 3  # 3 nodes created
        mock_qdrant.get_instance.return_value = mock_qdrant_instance
        
        # Test multimodal processing enabled
        await meltano_manager.ConvertFilesToVectorStore(
            folder_path=str(temp_test_directory),
            enable_multimodal=True
        )
        
        # Verify multimodal pipeline was used
        mock_multimodal_instance.extract_multimodal_elements.assert_called()
        mock_retrieval_instance.chunk_multimodal_elements.assert_called()
        mock_qdrant_instance.upsert_multimodal.assert_called()
    
    @pytest.mark.asyncio
    async def test_asset_management_workflow(self, setup_managers, temp_test_directory):
        """Test complete asset management workflow"""
        managers = setup_managers
        multimodal_manager = managers["multimodal"]
        
        # Test asset creation and retrieval
        doc_id = str(uuid4())
        element_id = "test_image"
        test_image_data = b"fake_image_data_for_testing"
        
        # Create asset
        asset_path = await multimodal_manager._save_image_asset(
            test_image_data, doc_id, element_id
        )
        
        # Verify asset was created
        assert os_path.exists(asset_path)
        assert doc_id in asset_path
        assert element_id in asset_path
        
        # Test asset retrieval
        retrieved_path = await multimodal_manager.get_asset_path(doc_id, element_id)
        assert retrieved_path == asset_path
        
        # Test asset cleanup
        await multimodal_manager.cleanup_assets(doc_id)
        assert not os_path.exists(asset_path)
    
    @pytest.mark.asyncio
    async def test_error_handling_workflow(self, setup_managers):
        """Test error handling in multimodal workflow"""
        managers = setup_managers
        multimodal_manager = managers["multimodal"]
        
        # Test with invalid file path
        result = await multimodal_manager.extract_multimodal_elements(
            "/nonexistent/file.pdf", 
            str(uuid4())
        )
        
        # Should return error structure
        assert "error" in result
        assert result["images"] == []
        assert result["tables"] == []
        assert result["text_elements"] == []
    
    @pytest.mark.asyncio
    @patch('managers.manager_multimodal.OpenAI')
    async def test_vision_model_integration(self, mock_openai, setup_managers):
        """Test vision model integration workflow"""
        managers = setup_managers
        multimodal_manager = managers["multimodal"]
        
        # Mock OpenAI response
        mock_client = MagicMock()
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "This image shows a sales chart with quarterly data"
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client
        
        # Create test image file
        test_image_path = BASE_DIR() / "test_image.png"
        test_image_path.write_bytes(b"fake_image_data")
        
        try:
            # Test image summarization
            summary = await multimodal_manager.generate_image_summary(
                str(test_image_path),
                "Context about sales document"
            )
            
            # Verify vision model was called
            assert "sales chart" in summary.lower()
            mock_client.chat.completions.create.assert_called_once()
            
        finally:
            # Cleanup
            if test_image_path.exists():
                test_image_path.unlink()
    
    @pytest.mark.asyncio
    @patch('managers.manager_multimodal.OpenAI')
    async def test_table_summarization_workflow(self, mock_openai, setup_managers):
        """Test table summarization workflow"""
        managers = setup_managers
        multimodal_manager = managers["multimodal"]
        
        # Mock OpenAI response
        mock_client = MagicMock()
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "This table shows quarterly sales data with North region performing best"
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client
        
        # Test table summarization
        table_markdown = """| Quarter | North | South | East | West |
| --- | --- | --- | --- | --- |
| Q1 | 100K | 80K | 90K | 70K |
| Q2 | 120K | 90K | 95K | 75K |"""
        
        summary = await multimodal_manager.generate_table_summary(
            table_markdown,
            "Sales report context"
        )
        
        # Verify table model was called
        assert "quarterly sales" in summary.lower()
        mock_client.chat.completions.create.assert_called_once()

class TestMultimodalPerformance:
    """Performance tests for multimodal workflow"""

    @pytest_asyncio.fixture
    async def setup_managers(self):
        """Setup all required managers for performance testing"""
        # Setup managers
        multimodal_manager = MultimodalManager.get_instance()
        await multimodal_manager.setup()

        qdrant_manager = QDrantManager.get_instance()
        retrieval_manager = RetrievalManager.get_instance()
        meltano_manager = MeltanoManager.get_instance()

        return {
            "multimodal": multimodal_manager,
            "qdrant": qdrant_manager,
            "retrieval": retrieval_manager,
            "meltano": meltano_manager
        }

    @pytest.mark.asyncio
    async def test_chunking_performance(self, setup_managers):
        """Test performance of multimodal chunking with large documents"""
        managers = setup_managers
        retrieval_manager = managers["retrieval"]
        
        # Create large document simulation
        text_elements = [{"id": f"text_{i}", "type": "NarrativeText", "text": f"Text element {i} " * 100} for i in range(50)]
        image_elements = [{"id": f"img_{i}", "type": "Image", "summary": f"Image {i} description " * 20} for i in range(10)]
        table_elements = [{"id": f"tbl_{i}", "type": "Table", "summary": f"Table {i} summary " * 15, "markdown": f"| Column A | Column B |\n|----------|----------|\n| Data {i} | Value {i} |"} for i in range(5)]

        # Combine all elements for proper processing
        all_elements = []
        all_elements.extend(text_elements)
        all_elements.extend(image_elements)
        all_elements.extend(table_elements)

        large_doc_data = {
            "text_elements": text_elements,
            "images": image_elements,
            "tables": table_elements,
            "figures": [],
            "captions": [],
            "all_elements": all_elements
        }
        
        import time
        start_time = time.time()
        
        chunks = await retrieval_manager.chunk_multimodal_elements(large_doc_data)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Verify reasonable performance (should complete within 5 seconds)
        assert processing_time < 5.0
        assert len(chunks) > 0
        
        print(f"Chunking performance: {processing_time:.2f}s for {len(chunks)} chunks")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])