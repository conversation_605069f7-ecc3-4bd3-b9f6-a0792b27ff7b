from imports import *

from managers.manager_supervisors import Supervisor<PERSON><PERSON><PERSON>, SupervisorTask_SingleAgent, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from langchain_core.tools import BaseTool
from langchain_core.language_models.base import BaseLanguageModel


class CheckEmailTool(BaseTool):
    """Tool for checking emails via IMAP"""
    name: str = "check_email"
    description: str = "Check for new emails using IMAP and process them into the knowledge base"
    
    def _run(self, user_guid: str, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, user_guid: str, state: SupervisorTaskState = None) -> str:
        """Execute IMAP email check"""
        try:
            from managers.manager_supervisors import SupervisorManager, SupervisorTaskState
            from managers.manager_users import ZairaUserManager
            
            user = await ZairaUserManager.find_user(user_guid)
            if not user:
                return f"User {user_guid} not found"
            
            # Get the IMAP email check task
            imap_check_task = SupervisorManager.get_task("imap_email_check_task")
            if not imap_check_task:
                return "IMAP email check task not available"
            
            # Create state for the task
            check_state = SupervisorTaskState(
                original_input="Check emails",
                user_guid=user_guid,
                messages=[]
            )
            
            # Execute the email check
            result = await imap_check_task.llm_call(check_state)
            
            return f"Email check completed: {result}"
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Failed to check emails", None)
            return f"Failed to check emails: {str(e)}"


class SupervisorTask_EmailChecker(SupervisorTask_SingleAgent):
    """Task for immediate email checking operations"""
    
    def __init__(self, model: BaseLanguageModel = None):
        tools = [CheckEmailTool()]
        super().__init__(
            name="email_checker",
            #prompt_id="Task_Email_Checker",
            prompt="You are specialized in checking emails via IMAP. Use the check_email tool to check for new emails and process them into the knowledge base immediately.",
            tools=tools,
            model=model
        )


async def create_task_email_checker() -> SupervisorTask_SingleAgent:
    """Create and register the email checking task"""
    return SupervisorManager.register_task(
        SupervisorTask_EmailChecker()
    )