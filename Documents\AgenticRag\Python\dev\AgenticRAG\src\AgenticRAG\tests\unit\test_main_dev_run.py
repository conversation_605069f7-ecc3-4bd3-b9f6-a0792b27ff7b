from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, call
from pathlib import Path
import asyncio

class TestDevRun:
    """Test dev_run.py functionality"""
    
    @pytest.mark.asyncio
    async def test_main_function_debug_mode(self):
        """Test main function sets debug mode correctly"""
        with patch('dev_run.mainFunc') as mock_main_func, \
             patch('dev_run.oauth_endpoint_external_main') as mock_oauth_main, \
             patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('dev_run.ZairaSettings') as mock_settings, \
             patch('dev_run.Globals') as mock_globals:
            
            mock_main_func.return_value = AsyncMock(return_value=None)
            mock_oauth_main.return_value = AsyncMock(return_value=None)
            
            from dev_run import main
            await main()
            
            # Verify debug mode was set (the code sets ZairaSettings.IsDebugMode = True)
            # We just check that set_debug was called correctly
            mock_globals.set_debug.assert_called()
            
            # Verify OAuth endpoint was called (not Claude environment)
            mock_oauth_main.assert_called_once()
            mock_main_func.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_main_function_claude_environment(self):
        """Test main function in Claude environment"""
        with patch('dev_run.mainFunc') as mock_main_func, \
             patch('dev_run.oauth_endpoint_external_main') as mock_oauth_main, \
             patch('etc.helper_functions.is_claude_environment', return_value=True) as mock_is_claude, \
             patch('dev_run.ZairaSettings') as mock_settings, \
             patch('dev_run.Globals') as mock_globals, \
             patch('builtins.print') as mock_print:
            
            mock_main_func.return_value = AsyncMock(return_value=None)
            mock_oauth_main.return_value = AsyncMock(return_value=None)
            
            from dev_run import main
            await main()
            
            # Verify debug mode was set (the code sets ZairaSettings.IsDebugMode = True)
            # We just check that set_debug was called correctly
            mock_globals.set_debug.assert_called()
            
            # Verify OAuth endpoint was NOT called (Claude environment)
            mock_oauth_main.assert_not_called()
            mock_print.assert_any_call("Claude environment detected - skipping OAuth external endpoint")
            mock_main_func.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_main_function_production_mode_claude(self):
        """Test main function in production mode with Claude environment"""
        with patch('dev_run.mainFunc') as mock_main_func, \
             patch('dev_run.oauth_endpoint_external_main') as mock_oauth_main, \
             patch('etc.helper_functions.is_claude_environment', return_value=True) as mock_is_claude, \
             patch('dev_run.ZairaSettings') as mock_settings, \
             patch('dev_run.Globals') as mock_globals:
            
            mock_main_func.return_value = AsyncMock(return_value=None)
            mock_oauth_main.return_value = AsyncMock(return_value=None)
            
            # Set debug mode to False initially
            mock_settings.IsDebugMode = False
            
            from dev_run import main
            await main()
            
            # Verify debug mode was set to True in dev_run
            # The code sets ZairaSettings.IsDebugMode = True, we can't easily test the assignment with mocks
            mock_oauth_main.assert_not_called()
            mock_main_func.assert_called_once()

class TestMain:
    """Test main.py functionality"""
    
    @pytest.mark.asyncio
    async def test_main_func_claude_environment(self):
        """Test mainFunc in Claude environment"""
        with patch('etc.helper_functions.is_claude_environment', return_value=True) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.OAuth2Verifier') as mock_oauth, \
             patch('main.exists', return_value=False) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('os.getcwd', return_value="/test/path") as mock_getcwd, \
             patch('globals.BASE_DIR', return_value=Path("/test/base")) as mock_base_dir:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = True
            
            from main import mainFunc
            await mainFunc()
            
            # Basic verification - at least check that our key functions were called
            mock_is_claude.assert_called()
            mock_init.assert_called_once()
            mock_exec_loop.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_main_func_non_claude_environment(self):
        """Test mainFunc in non-Claude environment"""
        with patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.OAuth2Verifier') as mock_oauth, \
             patch('main.exists', return_value=True) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('os.getcwd', return_value="/test/path") as mock_getcwd, \
             patch('globals.BASE_DIR', return_value=Path("/test/base")) as mock_base_dir, \
             patch('builtins.print') as mock_print:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = False
            mock_globals.get_query_engine_default.return_value = MagicMock()
            mock_globals.get_query_engine_default.return_value.query.return_value = "Test response"
            
            from main import mainFunc
            await mainFunc()
            
            # Verify non-Claude environment was detected
            mock_print.assert_any_call("No Claude environment detected")
            
            # Verify query engine was used in non-debug mode
            mock_globals.get_query_engine_default.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_main_func_docker_environment(self):
        """Test mainFunc in Docker environment"""
        with patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.OAuth2Verifier') as mock_oauth, \
             patch('main.exists', return_value=False) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('globals.BASE_DIR', return_value=Path("/test/base")) as mock_base_dir, \
             patch('builtins.print') as mock_print:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = True
            mock_globals.is_debug.return_value = True
            mock_oauth.get_token = AsyncMock(return_value=True)
            
            from main import mainFunc
            await mainFunc()
            
            # Verify Docker paths were used
            mock_oauth.get_token.assert_called_once_with("debug")
            mock_globals.set_debug.assert_called()
            
            # Verify init was called
            mock_init.assert_called_once()
            mock_exec_loop.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_main_func_new_project(self):
        """Test mainFunc with new project (no docstore.json)"""
        with patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.exists', return_value=False) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('os.getcwd', return_value="/test/path") as mock_getcwd, \
             patch('globals.BASE_DIR', return_value=Path("/test/base")) as mock_base_dir, \
             patch('builtins.print') as mock_print:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = True
            
            from main import mainFunc
            await mainFunc()
            
            # Verify init was called with newProject=True
            init_call_args = mock_init.call_args[0]
            assert init_call_args[0] is True  # newProject should be True
    
    @pytest.mark.asyncio
    async def test_main_func_existing_project(self):
        """Test mainFunc with existing project (docstore.json exists)"""
        with patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.exists', return_value=True) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('os.getcwd', return_value="/test/path") as mock_getcwd, \
             patch('globals.BASE_DIR', return_value=Path("/test/base")) as mock_base_dir, \
             patch('builtins.print') as mock_print:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = True
            
            from main import mainFunc
            await mainFunc()
            
            # Verify init was called with newProject=False
            init_call_args = mock_init.call_args[0]
            assert init_call_args[0] is False  # newProject should be False
    
    @pytest.mark.asyncio
    async def test_main_func_directory_setup(self):
        """Test mainFunc directory setup logic"""
        with patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.exists', return_value=False) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('os.getcwd', return_value="/test/path") as mock_getcwd, \
             patch('globals.BASE_DIR', return_value=Path("/test/base")) as mock_base_dir, \
             patch('builtins.print') as mock_print:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = True
            
            from main import mainFunc
            await mainFunc()
            
            # Verify directory paths were printed
            mock_print.assert_any_call("Using data subfolder: AskZaira")
            
            # Verify init was called with correct parameters
            init_call_args = mock_init.call_args[0]
            assert len(init_call_args) == 4  # newProject, DATA_DIR, PERSIST_DIR, parsers
    
    @pytest.mark.asyncio
    async def test_main_func_dotenv_loading(self):
        """Test mainFunc .env file loading"""
        with patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.exists', return_value=False) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('os.getcwd', return_value="/test/path") as mock_getcwd, \
             patch('globals.BASE_DIR', return_value=Path("/test/base")) as mock_base_dir:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = True
            
            from main import mainFunc
            await mainFunc()
            
            # Verify dotenv was loaded correctly
            assert mock_load_dotenv.call_count == 2
            # First call should be for dev.env with specific path and encoding
            first_call = mock_load_dotenv.call_args_list[0]
            assert "dev.env" in str(first_call)
            assert "latin-1" in str(first_call)
            
            # Second call should be for general .env with encoding
            second_call = mock_load_dotenv.call_args_list[1]
            assert "latin-1" in str(second_call)
    
    @pytest.mark.asyncio
    async def test_main_func_docker_no_dev_env(self):
        """Test mainFunc in Docker (no dev.env loading)"""
        with patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.OAuth2Verifier') as mock_oauth, \
             patch('main.exists', return_value=False) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('os.getcwd', return_value="/test/path") as mock_getcwd:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = True
            mock_globals.is_debug.return_value = True
            mock_oauth.get_token = AsyncMock(return_value=True)
            
            from main import mainFunc
            await mainFunc()
            
            # Verify dotenv was only loaded once (not for dev.env in Docker)
            assert mock_load_dotenv.call_count == 1
            # Should only have the general .env call
            call_args = mock_load_dotenv.call_args_list[0]
            assert "latin-1" in str(call_args)
    
    def test_environment_variables_not_set_unconditionally(self):
        """Test that environment variables are NOT set unconditionally by main.py import"""
        import os
        
        # Clear any existing environment variables for this test
        old_claude_code = os.environ.pop('CLAUDE_CODE', None)
        old_anthropic_user_id = os.environ.pop('ANTHROPIC_USER_ID', None)
        
        try:
            # Import main.py should NOT set these environment variables
            from main import mainFunc
            
            # These should NOT be set by importing main.py
            assert os.environ.get('CLAUDE_CODE') is None
            assert os.environ.get('ANTHROPIC_USER_ID') is None
            
        finally:
            # Restore original environment variables if they existed
            if old_claude_code is not None:
                os.environ['CLAUDE_CODE'] = old_claude_code
            if old_anthropic_user_id is not None:
                os.environ['ANTHROPIC_USER_ID'] = old_anthropic_user_id

class TestMainEdgeCases:
    """Test edge cases for main and dev_run"""
    
    @pytest.mark.asyncio
    async def test_main_func_query_engine_in_production(self):
        """Test query engine usage in production mode"""
        with patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.exists', return_value=False) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('os.getcwd', return_value="/test/path") as mock_getcwd, \
             patch('globals.BASE_DIR', return_value=Path("/test/base")) as mock_base_dir, \
             patch('builtins.print') as mock_print:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = False  # Production mode
            
            # Mock query engine
            mock_query_engine = MagicMock()
            mock_query_engine.query.return_value = "Data information response"
            mock_globals.get_query_engine_default.return_value = mock_query_engine
            
            from main import mainFunc
            await mainFunc()
            
            # Verify query engine was used in production mode
            mock_globals.get_query_engine_default.assert_called_once()
            mock_query_engine.query.assert_called_once_with("Tell me about your data")
            mock_print.assert_any_call("Data information response")
    
    @pytest.mark.asyncio
    async def test_try_out_sections_disabled(self):
        """Test that try-out sections are disabled by default"""
        with patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.exists', return_value=False) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('os.getcwd', return_value="/test/path") as mock_getcwd, \
             patch('globals.BASE_DIR', return_value=Path("/test/base")) as mock_base_dir, \
             patch('builtins.exit') as mock_exit:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = True
            
            from main import mainFunc
            await mainFunc()
            
            # Verify exit() was not called (try-out sections are disabled)
            mock_exit.assert_not_called()
    
    @pytest.mark.asyncio 
    async def test_data_subfolder_handling(self):
        """Test data subfolder handling logic"""
        with patch('etc.helper_functions.is_claude_environment', return_value=False) as mock_is_claude, \
             patch('etc.ZairaSettings.ZairaSettings') as mock_settings, \
             patch('main.Globals') as mock_globals, \
             patch('main.init') as mock_init, \
             patch('main.exec_main_loop') as mock_exec_loop, \
             patch('main.exists', return_value=False) as mock_exists, \
             patch('dotenv.load_dotenv') as mock_load_dotenv, \
             patch('os.getcwd', return_value="/test/path") as mock_getcwd, \
             patch('globals.BASE_DIR', return_value=Path("/test/base")) as mock_base_dir, \
             patch('builtins.print') as mock_print:
            
            mock_init.return_value = AsyncMock(return_value=None)
            mock_exec_loop.return_value = AsyncMock(return_value=None)
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = True
            
            from main import mainFunc
            await mainFunc()
            
            # Verify data subfolder was set to "AskZaira"
            mock_print.assert_any_call("Using data subfolder: AskZaira")
            
            # Verify init was called (we can check the call was made)
            mock_init.assert_called_once()
            init_call_args = mock_init.call_args[0]
            
            # Check that DATA_DIR and PERSIST_DIR contain the subfolder
            data_dir = init_call_args[1]
            persist_dir = init_call_args[2]
            
            assert "AskZaira" in str(data_dir)
            assert "AskZaira" in str(persist_dir)