from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from userprofiles.ZairaMessage import ZairaMessage, MessageRole, MessageStatus, Priority
from langchain.schema import HumanMessage, AIMessage, SystemMessage, FunctionMessage
import pytest
from unittest.mock import patch, MagicMock
from uuid import uuid4
import json
from datetime import datetime

class TestZairaMessage:
    """Comprehensive tests for ZairaMessage"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_content = "Test message content"
        self.test_conversation_id = str(uuid4())
        self.test_session_id = str(uuid4())
        self.test_tokens_used = 100
    
    def test_create_user_message(self):
        """Test creating a user message"""
        message = ZairaMessage.create_user_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        assert message.content == self.test_content
        assert message.conversation_id == self.test_conversation_id
        assert message.session_id == self.test_session_id
        assert message.role == MessageRole.USER
        assert message.timestamp is not None
        assert message.message_id is not None
        assert message.status == MessageStatus.PROCESSED
        assert message.priority == Priority.NORMAL
    
    def test_create_assistant_message(self):
        """Test creating an assistant message"""
        message = ZairaMessage.create_assistant_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id,
            tokens_used=self.test_tokens_used
        )
        
        assert message.content == self.test_content
        assert message.conversation_id == self.test_conversation_id
        assert message.session_id == self.test_session_id
        assert message.role == MessageRole.ASSISTANT
        assert message.tokens_used == self.test_tokens_used
        assert message.timestamp is not None
        assert message.message_id is not None
    
    def test_create_system_message(self):
        """Test creating a system message"""
        message = ZairaMessage.create_system_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        assert message.content == self.test_content
        assert message.conversation_id == self.test_conversation_id
        assert message.session_id == self.test_session_id
        assert message.role == MessageRole.SYSTEM
        assert message.timestamp is not None
        assert message.message_id is not None
    
    def test_create_function_message(self):
        """Test creating a function message"""
        function_name = "test_function"
        message = ZairaMessage.create_function_message(
            self.test_content,
            function_name=function_name,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        assert message.content == self.test_content
        assert message.role == MessageRole.FUNCTION
        assert message.metadata['function_name'] == function_name
    
    def test_create_tool_message(self):
        """Test creating a tool message"""
        tool_call_id = "test_tool_call_123"
        message = ZairaMessage.create_tool_message(
            self.test_content,
            tool_call_id=tool_call_id,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        assert message.content == self.test_content
        assert message.role == MessageRole.TOOL
        assert message.metadata['tool_call_id'] == tool_call_id
    
    def test_direct_creation(self):
        """Test direct ZairaMessage creation"""
        message = ZairaMessage(
            role=MessageRole.USER,
            content=self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        assert message.content == self.test_content
        assert message.conversation_id == self.test_conversation_id
        assert message.session_id == self.test_session_id
        assert message.role == MessageRole.USER
        assert message.timestamp is not None
        assert message.message_id is not None
    
    def test_to_langchain_user_message(self):
        """Test converting user message to LangChain format"""
        message = ZairaMessage.create_user_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        langchain_msg = message.to_langchain()
        
        assert isinstance(langchain_msg, HumanMessage)
        assert langchain_msg.content == self.test_content
        assert 'message_id' in langchain_msg.additional_kwargs
    
    def test_to_langchain_assistant_message(self):
        """Test converting assistant message to LangChain format"""
        message = ZairaMessage.create_assistant_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id,
            tokens_used=self.test_tokens_used
        )
        
        langchain_msg = message.to_langchain()
        
        assert isinstance(langchain_msg, AIMessage)
        assert langchain_msg.content == self.test_content
        assert langchain_msg.additional_kwargs['tokens_used'] == self.test_tokens_used
    
    def test_to_langchain_system_message(self):
        """Test converting system message to LangChain format"""
        message = ZairaMessage.create_system_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        langchain_msg = message.to_langchain()
        
        assert isinstance(langchain_msg, SystemMessage)
        assert langchain_msg.content == self.test_content
    
    def test_to_langchain_function_message(self):
        """Test converting function message to LangChain format"""
        function_name = "test_function"
        message = ZairaMessage.create_function_message(
            self.test_content,
            function_name=function_name,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        langchain_msg = message.to_langchain()
        
        assert isinstance(langchain_msg, FunctionMessage)
        assert langchain_msg.content == self.test_content
        assert langchain_msg.name == function_name
    
    def test_to_langchain_tool_message(self):
        """Test converting tool message to LangChain format"""
        tool_call_id = "test_tool_call_123"
        message = ZairaMessage.create_tool_message(
            self.test_content,
            tool_call_id=tool_call_id,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        langchain_msg = message.to_langchain()
        
        # ToolMessage might not be available in this version of langchain
        # Should fall back to HumanMessage
        assert langchain_msg.content == self.test_content
    
    def test_to_langchain_unknown_role_fallback(self):
        """Test LangChain conversion with unknown role falls back to HumanMessage (covers line 374)"""
        from unittest.mock import MagicMock
        # Create a message with a valid role
        message = ZairaMessage.create_user_message(self.test_content)
        
        # Mock the role to be something that doesn't match any of the if conditions
        # We'll use a MagicMock that equals none of the MessageRole values
        mock_role = MagicMock()
        mock_role.__eq__ = lambda self, other: False  # Never equals any MessageRole
        
        # Temporarily bypass validation by setting the role directly on the dict
        message.__dict__['role'] = mock_role
        
        langchain_msg = message.to_langchain()
        
        # Should fall back to HumanMessage (line 374)
        from langchain.schema import HumanMessage
        assert isinstance(langchain_msg, HumanMessage)
        assert langchain_msg.content == self.test_content
    
    def test_to_openai_format(self):
        """Test converting to OpenAI API format"""
        message = ZairaMessage.create_user_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        openai_msg = message.to_openai_format()
        
        assert openai_msg['role'] == 'user'
        assert openai_msg['content'] == self.test_content
    
    def test_to_openai_format_function_with_metadata(self):
        """Test converting function message to OpenAI format with name (covers line 385)"""
        message = ZairaMessage.create_function_message(
            "Function result",
            function_name="test_function"
        )
        
        openai_msg = message.to_openai_format()
        
        assert openai_msg['role'] == 'function'
        assert openai_msg['content'] == "Function result"
        assert openai_msg['name'] == "test_function"
    
    def test_to_openai_format_tool_with_metadata(self):
        """Test converting tool message to OpenAI format with tool_call_id (covers line 387)"""
        message = ZairaMessage.create_tool_message(
            "Tool result",
            tool_call_id="test_tool_123"
        )
        
        openai_msg = message.to_openai_format()
        
        assert openai_msg['role'] == 'tool'
        assert openai_msg['content'] == "Tool result"
        assert openai_msg['tool_call_id'] == "test_tool_123"
    
    def test_to_anthropic_format(self):
        """Test converting to Anthropic API format"""
        message = ZairaMessage.create_assistant_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        anthropic_msg = message.to_anthropic_format()
        
        assert anthropic_msg['role'] == 'assistant'
        assert anthropic_msg['content'] == self.test_content
    
    def test_to_minimal_dict(self):
        """Test converting to minimal dictionary"""
        message = ZairaMessage.create_user_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        minimal_dict = message.to_minimal_dict()
        
        assert minimal_dict['role'] == 'user'
        assert minimal_dict['content'] == self.test_content
        assert minimal_dict['conversation_id'] == self.test_conversation_id
        assert 'id' in minimal_dict
        assert 'timestamp' in minimal_dict
    
    def test_computed_fields(self):
        """Test computed fields"""
        content = "This is a test message with multiple words"
        message = ZairaMessage.create_user_message(
            content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        assert message.word_count == len(content.split())
        assert message.character_count == len(content)
        assert message.age_seconds >= 0
    
    def test_content_hash_generation(self):
        """Test automatic content hash generation"""
        message = ZairaMessage.create_user_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        assert message.content_hash is not None
        assert len(message.content_hash) == 32  # MD5 hash length
    
    def test_message_role_validation(self):
        """Test message role validation"""
        # Test valid role string
        message = ZairaMessage(
            role="user",
            content=self.test_content,
            conversation_id=self.test_conversation_id
        )
        assert message.role == MessageRole.USER
        
        # Test enum value
        message2 = ZairaMessage(
            role=MessageRole.ASSISTANT,
            content=self.test_content,
            conversation_id=self.test_conversation_id
        )
        assert message2.role == MessageRole.ASSISTANT
    
    def test_empty_content_handling(self):
        """Test handling of empty content"""
        # Empty content allowed for system messages
        message = ZairaMessage.create_system_message(
            "",
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id
        )
        
        assert message.content == ""
        assert message.word_count == 0
        assert message.character_count == 0
    
    def test_tags_and_categories_validation(self):
        """Test tags and categories validation"""
        tags = ["tag1", "tag2", "tag1"]  # Duplicate should be removed
        categories = ["cat1", " cat2 ", "cat1"]  # Whitespace and duplicates
        
        message = ZairaMessage.create_user_message(
            self.test_content,
            conversation_id=self.test_conversation_id,
            session_id=self.test_session_id,
            tags=tags,
            categories=categories
        )
        
        # Should remove duplicates and strip whitespace
        assert len(message.tags) == 2
        assert "tag1" in message.tags
        assert "tag2" in message.tags
        
        assert len(message.categories) == 2
        assert "cat1" in message.categories
        assert "cat2" in message.categories

class TestZairaMessageFactoryMethods:
    """Test factory methods and conversions"""
    
    def test_from_langchain_human_message(self):
        """Test creating ZairaMessage from LangChain HumanMessage"""
        lc_msg = HumanMessage(content="Test content", additional_kwargs={"test": "value"})
        
        zaira_msg = ZairaMessage.from_langchain(
            lc_msg,
            conversation_id="test_conv",
            session_id="test_session"
        )
        
        assert zaira_msg.role == MessageRole.USER
        assert zaira_msg.content == "Test content"
        assert zaira_msg.metadata["test"] == "value"
    
    def test_from_langchain_ai_message(self):
        """Test creating ZairaMessage from LangChain AIMessage"""
        lc_msg = AIMessage(content="AI response")
        
        zaira_msg = ZairaMessage.from_langchain(lc_msg)
        
        assert zaira_msg.role == MessageRole.ASSISTANT
        assert zaira_msg.content == "AI response"
    
    def test_from_openai_format(self):
        """Test creating ZairaMessage from OpenAI format"""
        openai_msg = {
            "role": "user",
            "content": "OpenAI test message"
        }
        
        zaira_msg = ZairaMessage.from_openai_format(
            openai_msg,
            conversation_id="test_conv"
        )
        
        assert zaira_msg.role == MessageRole.USER
        assert zaira_msg.content == "OpenAI test message"
        assert zaira_msg.conversation_id == "test_conv"
    
    def test_from_openai_format_with_function_name(self):
        """Test creating ZairaMessage from OpenAI format with function name"""
        openai_msg = {
            "role": "function",
            "content": "Function result",
            "name": "test_function"
        }
        
        zaira_msg = ZairaMessage.from_openai_format(openai_msg)
        
        assert zaira_msg.role == MessageRole.FUNCTION
        assert zaira_msg.metadata['function_name'] == "test_function"
    
    def test_from_openai_format_with_tool_call_id(self):
        """Test creating ZairaMessage from OpenAI format with tool_call_id (covers line 608)"""
        openai_msg = {
            "role": "tool",
            "content": "Tool result",
            "tool_call_id": "test_tool_123"
        }
        
        zaira_msg = ZairaMessage.from_openai_format(openai_msg)
        
        assert zaira_msg.role == MessageRole.TOOL
        assert zaira_msg.metadata['tool_call_id'] == "test_tool_123"

class TestZairaMessageUtilityMethods:
    """Test utility methods"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.message = ZairaMessage.create_user_message(
            "Test content",
            conversation_id="test_conv",
            session_id="test_session"
        )
    
    def test_update_metadata(self):
        """Test updating metadata"""
        updated_msg = self.message.update_metadata(new_key="new_value", test="updated")
        
        assert updated_msg.metadata["new_key"] == "new_value"
        assert updated_msg.metadata["test"] == "updated"
        # Original message should be unchanged
        assert "new_key" not in self.message.metadata
    
    def test_mark_processed(self):
        """Test marking message as processed"""
        processed_msg = self.message.mark_processed(processing_time_ms=150.5)
        
        assert processed_msg.status == MessageStatus.PROCESSED
        assert processed_msg.processing_time_ms == 150.5
    
    def test_mark_failed(self):
        """Test marking message as failed"""
        failed_msg = self.message.mark_failed("Test error")
        
        assert failed_msg.status == MessageStatus.FAILED
        assert failed_msg.metadata['error_message'] == "Test error"
    
    def test_add_tags(self):
        """Test adding tags"""
        tagged_msg = self.message.add_tags("new_tag1", "new_tag2")
        
        assert "new_tag1" in tagged_msg.tags
        assert "new_tag2" in tagged_msg.tags
    
    def test_add_categories(self):
        """Test adding categories"""
        categorized_msg = self.message.add_categories("new_cat1", "new_cat2")
        
        assert "new_cat1" in categorized_msg.categories
        assert "new_cat2" in categorized_msg.categories
    
    def test_calculate_cost(self):
        """Test calculating cost"""
        msg_with_tokens = ZairaMessage.create_assistant_message(
            "Test content",
            tokens_used=100
        )
        
        cost = msg_with_tokens.calculate_cost(0.01)
        assert cost == 1.0
        
        # No tokens used
        cost_none = self.message.calculate_cost(0.01)
        assert cost_none is None
    
    def test_role_check_methods(self):
        """Test role checking methods"""
        user_msg = ZairaMessage.create_user_message("test")
        assert user_msg.is_from_user() is True
        assert user_msg.is_from_assistant() is False
        assert user_msg.is_system_message() is False
        
        assistant_msg = ZairaMessage.create_assistant_message("test")
        assert assistant_msg.is_from_user() is False
        assert assistant_msg.is_from_assistant() is True
        assert assistant_msg.is_system_message() is False
        
        system_msg = ZairaMessage.create_system_message("test")
        assert system_msg.is_from_user() is False
        assert system_msg.is_from_assistant() is False
        assert system_msg.is_system_message() is True

class TestZairaMessageValidation:
    """Test validation edge cases for ZairaMessage"""
    
    def test_enum_field_validators_with_non_string_input(self):
        """Test field validators with non-string inputs (covers lines 233, 241, 249)"""
        # Test creation with pre-validated enum values to trigger the non-string path
        # This should trigger the `return v` branches (lines 233, 241, 249)
        
        # Use model_validate with enum values to ensure validator path
        data = {
            "role": MessageRole.SYSTEM,  # Enum, not string
            "content": "Test",
            "status": MessageStatus.FAILED,  # Enum, not string  
            "priority": Priority.LOW  # Enum, not string
        }
        
        message = ZairaMessage.model_validate(data)
        assert message.role == MessageRole.SYSTEM
        assert message.status == MessageStatus.FAILED 
        assert message.priority == Priority.LOW
    
    def test_invalid_role_validation(self):
        """Test invalid role raises ValidationError"""
        with pytest.raises(ValueError, match="Invalid role"):
            ZairaMessage(
                role="invalid_role",
                content="Test content"
            )
    
    def test_empty_content_validation_for_user_message(self):
        """Test empty content validation for non-system messages"""
        from pydantic import ValidationError
        with pytest.raises(ValidationError):
            ZairaMessage(
                role=MessageRole.USER,
                content=""
            )
    
    def test_empty_content_validation_for_assistant_message(self):
        """Test empty content validation for assistant messages"""
        from pydantic import ValidationError
        with pytest.raises(ValidationError):
            ZairaMessage(
                role=MessageRole.ASSISTANT,
                content=""
            )
    
    def test_role_enum_passthrough(self):
        """Test that MessageRole enum values are passed through without conversion"""
        # Use direct constructor to trigger validator with enum input
        from userprofiles.ZairaMessage import ZairaMessage
        
        # Test direct enum input to ensure line 233 is hit
        message = ZairaMessage(
            role=MessageRole.ASSISTANT,  # Already an enum, should pass through line 233
            content="Test content"
        )
        assert message.role == MessageRole.ASSISTANT
    
    def test_status_string_validation(self):
        """Test status validation with string input"""
        message = ZairaMessage(
            role=MessageRole.USER,
            content="Test content",
            status="pending"  # String input, should convert through line 240
        )
        assert message.status == MessageStatus.PENDING
    
    def test_status_enum_passthrough(self):
        """Test that MessageStatus enum values are passed through without conversion"""
        message = ZairaMessage(
            role=MessageRole.USER,
            content="Test content",
            status=MessageStatus.PENDING  # Already an enum, should pass through line 241
        )
        assert message.status == MessageStatus.PENDING
    
    def test_priority_string_validation(self):
        """Test priority validation with string input"""
        message = ZairaMessage(
            role=MessageRole.USER,
            content="Test content",
            priority="high"  # String input, should convert through line 248
        )
        assert message.priority == Priority.HIGH
    
    def test_priority_enum_passthrough(self):
        """Test that Priority enum values are passed through without conversion"""
        message = ZairaMessage(
            role=MessageRole.USER,
            content="Test content",
            priority=Priority.HIGH  # Already an enum, should pass through line 249
        )
        assert message.priority == Priority.HIGH

class TestZairaMessageEdgeCases:
    """Test edge cases for ZairaMessage"""
    
    def test_string_representations(self):
        """Test string and repr representations"""
        message = ZairaMessage.create_user_message(
            "A very long message content that should be truncated in the string representation",
            conversation_id="test_conv"
        )
        
        str_repr = str(message)
        assert "ZairaMessage" in str_repr
        assert "user" in str_repr
        assert "..." in str_repr  # Content should be truncated
        
        repr_str = repr(message)
        assert "ZairaMessage" in repr_str
        assert message.message_id in repr_str
    
    def test_to_dict_options(self):
        """Test to_dict with different options"""
        message = ZairaMessage.create_assistant_message(
            "Test content",
            tokens_used=100,
            processing_time_ms=250.5,
            metadata={"custom": "value"}
        )
        
        # With metadata
        dict_with_meta = message.to_dict(include_metadata=True)
        assert "tokens_used" in dict_with_meta
        assert "processing_time_ms" in dict_with_meta
        
        # Without metadata
        dict_no_meta = message.to_dict(include_metadata=False)
        assert "tokens_used" not in dict_no_meta
        assert "processing_time_ms" not in dict_no_meta
        
        # With computed fields
        dict_computed = message.to_dict(include_computed=True)
        assert "word_count" in dict_computed
        assert "character_count" in dict_computed
        assert "age_seconds" in dict_computed
        
        # Without excluding None values (to cover line 308)
        dict_with_none = message.to_dict(exclude_none=False)
        assert isinstance(dict_with_none, dict)
    
    def test_to_json(self):
        """Test JSON serialization"""
        message = ZairaMessage.create_user_message(
            "Test content",
            conversation_id="test_conv"
        )
        
        json_str = message.to_json()
        assert isinstance(json_str, str)
        
        # Should be valid JSON
        parsed = json.loads(json_str)
        assert parsed['role'] == 'user'
        assert parsed['content'] == 'Test content'
    
    def test_very_long_content(self):
        """Test handling of very long content"""
        very_long_content = "word " * 10000  # Very long content
        
        message = ZairaMessage.create_user_message(very_long_content)
        
        assert message.content == very_long_content
        assert message.word_count == 10000
        assert message.character_count == len(very_long_content)
    
    def test_unicode_content(self):
        """Test handling of Unicode content"""
        unicode_content = "Hello 🌍 World! Testing unicode: αβγ 中文 العربية"
        
        message = ZairaMessage.create_user_message(unicode_content)
        
        assert message.content == unicode_content
        assert message.content_hash is not None
        
        # Should work with all conversions
        langchain_msg = message.to_langchain()
        assert langchain_msg.content == unicode_content
        
        openai_msg = message.to_openai_format()
        assert openai_msg['content'] == unicode_content