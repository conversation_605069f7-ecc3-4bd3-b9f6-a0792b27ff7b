<body>
    <!-- Particles Background -->
    <div class="particles" id="particles"></div>
    
    <!-- Right Side Menu -->
    <div class="right-menu" id="rightMenu">
        <button class="menu-toggle" id="menuToggle">
            <span id="menuIcon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                </svg>
            </span>
        </button>
        
        <div class="menu-items" id="menuItems">
            <a href="/dashboard" class="menu-item" data-page="overview">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="9" y1="9" x2="15" y2="9"></line>
                        <line x1="9" y1="15" x2="15" y2="15"></line>
                        <line x1="9" y1="12" x2="15" y2="12"></line>
                    </svg>
                </div>
                <div class="menu-item-text">Overview</div>
            </a>
            
            <a href="/dashboard/profile" class="menu-item" data-page="profile">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                </div>
                <div class="menu-item-text">Profile</div>
            </a>
            
            <a href="/dashboard/zaira" class="menu-item" data-page="zaira">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                </div>
                <div class="menu-item-text">Zaira</div>
            </a>
            
            <a href="/dashboard/account" class="menu-item" data-page="account">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"></circle>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                    </svg>
                </div>
                <div class="menu-item-text">Account</div>
            </a>
            
            <a href="/connectors" class="menu-item" data-page="connectors">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                        <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                    </svg>
                </div>
                <div class="menu-item-text">Connectors</div>
            </a>
            
            <a href="/dashboard/system" class="menu-item" data-page="system">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                        <line x1="8" y1="21" x2="16" y2="21"></line>
                        <line x1="12" y1="17" x2="12" y2="21"></line>
                    </svg>
                </div>
                <div class="menu-item-text">System Information</div>
            </a>
            
            <a href="/dashboard/subscription" class="menu-item" data-page="subscription">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                        <line x1="1" y1="10" x2="23" y2="10"></line>
                    </svg>
                </div>
                <div class="menu-item-text">Subscription</div>
            </a>
            
            <a href="/dashboard/chat" class="menu-item" data-page="chat" style="opacity: 0.01; height: 1px; overflow: hidden; margin: 0; padding: 1px;">
                <div class="menu-item-icon" style="display: none;">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        <circle cx="12" cy="16" r="1"></circle>
                    </svg>
                </div>
                <div class="menu-item-text" style="display: none;">Debug</div>
            </a>
        </div>
    </div>
    
    <!-- Main Container -->
    <div class="main-container" id="mainContainer">
        <!-- Original ZairaControl Header -->
        <div class="header" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); padding: 1rem 2rem; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5); border-bottom: 1px solid rgba(59, 130, 246, 0.2); z-index: 10000; position: relative;">
            <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <h1 style="background: linear-gradient(135deg, #60a5fa, #a78bfa); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-size: 2rem; font-weight: 700; text-shadow: 0 0 30px rgba(96, 165, 250, 0.5); margin: 0;">AskZaira Dashboard</h1>
                    <span class="status-badge" id="headerStatusBadge" style="display: inline-block; background: #6c757d; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; font-weight: 500; text-transform: uppercase;">Loading...</span>
                </div>
                
                <div style="background: linear-gradient(135deg, #60a5fa, #a78bfa); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-size: 1.25rem; font-weight: 600; text-shadow: 0 0 20px rgba(96, 165, 250, 0.3); letter-spacing: 0.05em;"><!--Navigation & Controls--></div>
                
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div class="auto-refresh" id="autoRefresh">
                        <span id="refreshTimer" style="color: #94a3b8; font-size: 0.85rem;">Auto-refresh: Disabled</span>
                        <div class="refresh-toggle" id="autoRefreshToggle" style="display: inline-block; margin-left: 10px; position: relative; width: 50px; height: 24px; background: rgba(100, 116, 139, 0.3); border-radius: 12px; cursor: pointer; transition: background 0.3s ease;">
                            <div class="refresh-toggle-slider" style="position: absolute; top: 2px; left: 2px; width: 20px; height: 20px; background: white; border-radius: 50%; transition: transform 0.3s ease;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Page Content Area -->
        <div class="page-content" id="pageContent">
            <div class="loading">Loading overview...</div>
        </div>
    </div>
    
    <script>
        // Global variables
        let isMenuExpanded = false;
        let autoRefreshEnabled = localStorage.getItem('autoRefresh') === 'true';
        let autoRefreshInterval = null;
        let currentPage = 'overview';
        
        // API Base URL - use window origin which will work for both Windows and WSL
        const API_BASE_URL = window.location.origin;
        
        // State persistence for F5 refresh
        const STATE_STORAGE_KEY = 'askzaira_dashboard_state';
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeParticles();
            initializeMenu();
            
            // Try to restore previous state first
            const stateRestored = restoreApplicationState();
            
            if (!stateRestored) {
                // No saved state, initialize normally
                setCurrentPage();
            }
            
            initializeAutoRefresh();
            initializeStatusCheck();
            
            // Set up state saving on various events
            setupStatePersistence();
        });
        
        // Particles animation
        function initializeParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                const size = Math.random() * 4 + 2;
                const x = Math.random() * 100;
                const y = Math.random() * 100;
                const duration = Math.random() * 3 + 4;
                const delay = Math.random() * 2;
                
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = x + '%';
                particle.style.top = y + '%';
                particle.style.animationDuration = duration + 's';
                particle.style.animationDelay = delay + 's';
                
                particlesContainer.appendChild(particle);
            }
        }
        
        // Menu functionality
        function initializeMenu() {
            const menuToggle = document.getElementById('menuToggle');
            const rightMenu = document.getElementById('rightMenu');
            const mainContainer = document.getElementById('mainContainer');
            const menuIcon = document.getElementById('menuIcon');
            
            menuToggle.addEventListener('click', function() {
                isMenuExpanded = !isMenuExpanded;
                
                if (isMenuExpanded) {
                    rightMenu.classList.add('expanded');
                    mainContainer.classList.add('menu-expanded');
                    menuIcon.innerHTML = `
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    `;
                } else {
                    rightMenu.classList.remove('expanded');
                    mainContainer.classList.remove('menu-expanded');
                    menuIcon.innerHTML = `
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="3" y1="6" x2="21" y2="6"></line>
                            <line x1="3" y1="12" x2="21" y2="12"></line>
                            <line x1="3" y1="18" x2="21" y2="18"></line>
                        </svg>
                    `;
                }
            });
            
            // Menu item click handling
            const menuItems = document.querySelectorAll('.menu-item');
            // console.log('Setting up menu click handlers for', menuItems.length, 'items'); // Debug log
            
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // console.log('Menu item clicked:', this.getAttribute('data-page'), 'href:', this.getAttribute('href')); // Debug log
                    
                    // Get page from data attribute
                    const page = this.getAttribute('data-page');
                    const href = this.getAttribute('href');
                    
                    // Handle special cases first
                    if (page === 'connectors') {
                        // console.log('Connectors page - allowing normal navigation'); // Debug log
                        // Will navigate to /connectors - don't prevent default
                        return;
                    }
                    
                    if (page === 'chat') {
                        // console.log('Debug chat page - checking password'); // Debug log
                        // Handle password prompt for debug chat
                        e.preventDefault();
                        const password = prompt('Enter password for Debug:');
                        if (password !== 'DEBUG') {
                            // Silently do nothing on incorrect password - maintain current selection
                            // console.log('Incorrect password - maintaining current page selection');
                            return;
                        }
                        // console.log('Debug password correct'); // Debug log
                    }
                    
                    // For all /dashboard pages, prevent default navigation and load content via JavaScript
                    if (href && href.startsWith('/dashboard')) {
                        // console.log('Preventing default navigation for /dashboard page:', page); // Debug log
                        e.preventDefault();
                        
                        // Update active state
                        menuItems.forEach(mi => mi.classList.remove('active'));
                        this.classList.add('active');
                        
                        // Update current page
                        currentPage = page;
                        
                        // No URL updates - keep URL at /dashboard for all pages
                        
                        // Load page content
                        // console.log('Loading page content for:', page); // Debug log
                        loadPageContent(page);
                        
                        // Save state after page change
                        setTimeout(saveApplicationState, 100);
                    } else {
                        // console.log('Allowing normal navigation for non-/dashboard page'); // Debug log
                    }
                });
            });
        }
        
        // Auto-refresh functionality with countdown timer
        let countdown = 30;
        
        function initializeAutoRefresh() {
            const toggle = document.getElementById('autoRefreshToggle');
            
            // Set initial state
            if (autoRefreshEnabled) {
                toggle.style.background = 'var(--brand-primary)';
                toggle.querySelector('.refresh-toggle-slider').style.transform = 'translateX(26px)';
                startAutoRefresh();
            }
            
            toggle.addEventListener('click', function() {
                toggleAutoRefresh();
            });
        }
        
        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;
            localStorage.setItem('autoRefresh', autoRefreshEnabled);
            
            const toggle = document.getElementById('autoRefreshToggle');
            const timer = document.getElementById('refreshTimer');
            
            if (autoRefreshEnabled) {
                toggle.style.background = 'var(--brand-primary)';
                toggle.querySelector('.refresh-toggle-slider').style.transform = 'translateX(26px)';
                timer.style.opacity = '1';
                countdown = 30; // Reset countdown when enabling
                timer.textContent = `Auto-refresh: ${countdown}s`;
                startAutoRefresh();
            } else {
                toggle.style.background = 'rgba(100, 116, 139, 0.3)';
                toggle.querySelector('.refresh-toggle-slider').style.transform = 'translateX(0)';
                timer.style.opacity = '0.5';
                timer.textContent = 'Auto-refresh: Disabled';
                stopAutoRefresh();
            }
        }
        
        function startAutoRefresh() {
            if (autoRefreshInterval) clearInterval(autoRefreshInterval);
            
            autoRefreshInterval = setInterval(() => {
                if (!autoRefreshEnabled) return;
                
                countdown--;
                document.getElementById('refreshTimer').textContent = `Auto-refresh: ${countdown}s`;
                
                if (countdown <= 0) {
                    // Refresh the current view
                    updateSystemStatus();
                    refreshCurrentPageData();
                    countdown = 30; // Reset countdown
                }
            }, 1000);
        }
        
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }
        
        // System status check
        function initializeStatusCheck() {
            updateSystemStatus();
        }
        
        async function updateSystemStatus() {
            try {
                // Get system status from dashboard overview endpoint
                const response = await fetch('/dashboard/api/dashboard-overview');
                const data = await response.json();
                
                const statusBox = document.getElementById('systemStatus');
                if (!statusBox) {
                    // Status box doesn't exist on this page, skip silently
                    return;
                }
                
                const indicator = statusBox.querySelector('.status-indicator');
                const text = statusBox.querySelector('span');
                
                // Extract status from system health data
                const systemHealth = data.system_health || {};
                const status = systemHealth.overall_status || 'unknown';
                
                if (status === 'healthy') {
                    statusBox.className = 'status-box status-badge success';
                    if (indicator) indicator.style.backgroundColor = 'var(--status-success)';
                    if (text) text.textContent = 'System Healthy';
                } else if (status === 'moderate' || status === 'warning') {
                    statusBox.className = 'status-box status-badge warning';
                    if (indicator) indicator.style.backgroundColor = 'var(--status-warning)';
                    if (text) text.textContent = 'System Warning';
                } else if (status === 'degraded' || status === 'critical') {
                    statusBox.className = 'status-box status-badge error';
                    if (indicator) indicator.style.backgroundColor = 'var(--status-error)';
                    if (text) text.textContent = 'System Error';
                } else {
                    statusBox.className = 'status-box status-badge error';
                    if (indicator) indicator.style.backgroundColor = 'var(--status-error)';
                    if (text) text.textContent = 'Status Unknown';
                }
            } catch (error) {
                // console.error('Failed to update system status:', error);
                const statusBox = document.getElementById('systemStatus');
                if (statusBox) {
                    statusBox.className = 'status-box status-badge error';
                    const text = statusBox.querySelector('span');
                    if (text) text.textContent = 'Network Error';
                }
            }
        }
        
        // Page content loading
        function setCurrentPage() {
            const path = window.location.pathname;
            if (path === '/dashboard') {
                currentPage = 'overview';
                // Load Overview page immediately
                loadPageContent('overview');
            } else if (path.startsWith('/dashboard/')) {
                currentPage = path.split('/')[2] || 'overview';
                // Load the specified page
                if (currentPage !== 'overview') {
                    loadPageContent(currentPage);
                }
            }
            
            // Set active menu item
            const activeItem = document.querySelector(`[data-page="${currentPage}"]`);
            if (activeItem) {
                document.querySelectorAll('.menu-item').forEach(item => item.classList.remove('active'));
                activeItem.classList.add('active');
            }
        }
        
        async function loadPageContent(page) {
            const contentArea = document.getElementById('pageContent');
            contentArea.innerHTML = '<div class="loading">Loading page content...</div>';
            
            try {
                if (page === 'overview') {
                    // Load Overview page with dashboard segments
                    await loadOverviewPage();
                } else if (page === 'zaira') {
                    // Load Zaira page with user management
                    await loadZairaPage();
                } else if (page === 'chat') {
                    // Load Chat page with input field and chat history
                    await loadChatPage();
                } else {
                    // Load other pages normally
                    let url = `/dashboard/api/page-content?page=${page}`;
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    if (data.html) {
                        contentArea.innerHTML = data.html;
                        // Initialize page-specific features after content is loaded
                        setTimeout(() => initializePageFeatures(), 100);
                    } else if (data.redirect) {
                        // Handle redirect to existing implementations
                        if (data.redirect === 'overview') {
                            await loadOverviewPage();
                        } else if (data.redirect === 'zaira') {
                            await loadZairaPage();
                        } else if (data.redirect === 'chat') {
                            await loadChatPage();
                        }
                        return;
                    } else {
                        showEmptyState(page);
                    }
                }
            } catch (error) {
                // console.error('Failed to load page content:', error);
                contentArea.innerHTML = `<div class="error">Failed to load page content: ${error.message}</div>`;
            }
        }
        
        async function loadOverviewPage() {
            const contentArea = document.getElementById('pageContent');
            
            try {
                // Fetch dashboard data
                const response = await fetch('/dashboard/api/dashboard-overview');
                const data = await response.json();
                
                // Generate Overview page HTML with the 4 segments + system info
                const overviewHTML = generateOverviewHTML(data);
                contentArea.innerHTML = overviewHTML;
            } catch (error) {
                // console.error('Failed to load overview page:', error);
                contentArea.innerHTML = `<div class="error">Failed to load overview: ${error.message}</div>`;
            }
        }
        
        async function loadZairaPage() {
            const contentArea = document.getElementById('pageContent');
            
            try {
                // Fetch user management data
                const response = await fetch(`${API_BASE_URL}/dashboard/api/user-management`);
                const data = await response.json();
                
                // Generate Zaira page HTML with user management section
                const zairaHTML = generateZairaHTML(data);
                contentArea.innerHTML = zairaHTML;
                
                // Auto-load user list after page is rendered
                setTimeout(() => {
                    loadUserList();
                }, 100);
            } catch (error) {
                // console.error('Failed to load zaira page:', error);
                contentArea.innerHTML = `<div class="error">Failed to load Zaira management: ${error.message}</div>`;
            }
        }
        
        async function loadChatPage() {
            const contentArea = document.getElementById('pageContent');
            
            try {
                // Generate Chat page HTML with input field and history display
                const chatHTML = generateChatHTML();
                contentArea.innerHTML = chatHTML;
                
                // Pre-select SYSTEM user and load their chat history
                setTimeout(() => {
                    const systemUserGuid = '00000000-0000-0000-0000-000000000001'; // System user GUID
                    const chatUserInput = document.getElementById('chatPageUserGuidInput');
                    if (chatUserInput) {
                        chatUserInput.value = systemUserGuid;
                        loadChatPageHistory();
                    }
                    
                    // Set up Enter key functionality for message input
                    setupChatMessageKeyHandler();
                    
                    // Initialize live log viewer directly (dashboard.js is now loaded statically)
                    console.log('[DEBUG] Chat page loaded, initializing live log viewer');
                    
                    // Since dashboard.js is loaded statically, we can call the function directly
                    if (typeof window.initializeLiveLogViewer === 'function') {
                        console.log('[DEBUG] initializeLiveLogViewer function found, calling it directly');
                        try {
                            window.initializeLiveLogViewer();
                            console.log('[DEBUG] Live log viewer initialized successfully');
                        } catch (error) {
                            console.error('[ERROR] Failed to initialize live log viewer:', error);
                        }
                    } else {
                        console.error('[ERROR] initializeLiveLogViewer function not found - dashboard.js may not be loaded properly');
                    }
                }, 100);
            } catch (error) {
                // console.error('Failed to load chat page:', error);
                contentArea.innerHTML = `<div class="error">Failed to load Chat page: ${error.message}</div>`;
            }
        }
        
        function generateChatHTML() {
            return `
                <div class="page-section">
                    <div class="section-title">
                        <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        Chat Interface
                    </div>
                    
                    <!-- Chat Input Section -->
                    <div class="card" style="margin-bottom: 2rem;">
                        <div class="card-title">Send Message</div>
                        <div class="card-content">
                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <!-- User Selection -->
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <label for="chatPageUserGuidInput" style="color: #93c5fd; font-weight: 600; min-width: 120px;">User GUID:</label>
                                    <input type="text" 
                                           id="chatPageUserGuidInput" 
                                           placeholder="Enter user GUID (SYSTEM user pre-selected)"
                                           style="flex: 1; padding: 0.75rem; background: rgba(15, 23, 42, 0.8); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 6px; color: #f8fafc; font-size: 0.9rem;"
                                           value="00000000-0000-0000-0000-000000000001">
                                </div>
                                
                                <!-- Message Input -->
                                <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                    <label for="chatPageMessageInput" style="color: #93c5fd; font-weight: 600;">Message:</label>
                                    <textarea id="chatPageMessageInput" 
                                              placeholder="Type your message here..."
                                              rows="4"
                                              style="width: 100%; padding: 0.75rem; background: rgba(15, 23, 42, 0.8); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 6px; color: #f8fafc; font-size: 0.9rem; resize: vertical; min-height: 100px; font-family: inherit;"></textarea>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div style="display: flex; gap: 1rem; align-items: center;">
                                    <button onclick="sendChatMessage()" 
                                            style="padding: 0.75rem 1.5rem; background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9rem; font-weight: 600; transition: all 0.2s ease;">
                                        Send Message
                                    </button>
                                    <button onclick="loadChatPageHistory()" 
                                            style="padding: 0.75rem 1.5rem; background: rgba(30, 41, 59, 0.8); color: #93c5fd; border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 6px; cursor: pointer; font-size: 0.9rem; font-weight: 500; transition: all 0.2s ease;">
                                        Refresh History
                                    </button>
                                    <button onclick="clearChatInput()" 
                                            style="padding: 0.75rem 1.5rem; background: rgba(239, 68, 68, 0.2); color: #fca5a5; border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 6px; cursor: pointer; font-size: 0.9rem; font-weight: 500; transition: all 0.2s ease;">
                                        Clear
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Chat History Section -->
                    <div class="card">
                        <div class="card-title">Chat History</div>
                        <div class="card-content">
                            <div id="chatPageContainer">
                                <div class="loading">Select a user and click "Refresh History" to view chat history</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Live Log Viewer Section -->
                    <div class="card" style="margin-top: 2rem;">
                        <div class="card-title">Live System Logs</div>
                        <div class="card-content">
                            <div id="liveLogViewer" style="margin-top: 0;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                    <div style="font-weight: 600; font-size: 1rem; color: #e2e8f0;">
                                        📋 Live System Logs
                                    </div>
                                    <div style="display: flex; gap: 0.5rem; align-items: center;">
                                        <div id="logStatus" style="font-size: 0.8rem; color: #94a3b8; display: flex; align-items: center; gap: 0.25rem;">
                                            <span id="logStatusIcon" style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></span>
                                            <span id="logStatusText">Connected</span>
                                        </div>
                                        <button id="testRealServerBtn" onclick="checkAndRedirectTestReal()" 
                                                title="Switch to test_real server (Debug mode only)"
                                                onmouseover="this.style.background='#7c3aed'"
                                                onmouseout="this.style.background='#8b5cf6'"
                                                style="padding: 0.25rem 0.5rem; background: #8b5cf6; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s; display: none;">
                                            🔄 Test_Real Server
                                        </button>
                                        <button id="logPauseBtn" onclick="toggleLogViewer()" 
                                                title="Pause/Resume real-time updates"
                                                onmouseover="this.style.background='#1f2937'"
                                                onmouseout="this.style.background='#374151'"
                                                style="padding: 0.25rem 0.5rem; background: #374151; color: #e2e8f0; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                                            ⏸️ Pause
                                        </button>
                                        <button onclick="clearLogViewer()" 
                                                title="Clear log display"
                                                onmouseover="this.style.background='#dc2626'"
                                                onmouseout="this.style.background='#ef4444'"
                                                style="padding: 0.25rem 0.5rem; background: #ef4444; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                                            🗑️ Clear
                                        </button>
                                        <button onclick="copyLogContent()" 
                                                title="Copy logs to clipboard"
                                                onmouseover="this.style.background='#0369a1'"
                                                onmouseout="this.style.background='#0284c7'"
                                                style="padding: 0.25rem 0.5rem; background: #0284c7; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                                            📋 Copy
                                        </button>
                                        <button id="rawModeToggle" onclick="toggleRawMode()" 
                                                title="Toggle Raw/Unfiltered mode (shows exact debug_trace.log content)"
                                                onmouseover="this.style.background='#059669'"
                                                onmouseout="updateRawModeButton()"
                                                style="padding: 0.25rem 0.5rem; background: #10b981; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                                            📄 Raw Mode
                                        </button>
                                        <button onclick="jumpToLatestLogs()" 
                                                title="Jump to most recent logs (like Python debugger shows)"
                                                onmouseover="this.style.background='#7c2d12'"
                                                onmouseout="this.style.background='#ea580c'"
                                                style="padding: 0.25rem 0.5rem; background: #ea580c; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                                            ⏭️ Jump to Latest
                                        </button>
                                    </div>
                                </div>
                                
                                <div style="margin-bottom: 1rem;">
                                    <input type="text" id="logSearchInput" placeholder="Search logs... (press Enter or type to filter)"
                                           onkeyup="filterLogContent()"
                                           style="width: 100%; padding: 0.5rem; background: #1e293b; border: 1px solid #374151; border-radius: 4px; color: #e2e8f0; font-size: 0.85rem;">
                                </div>
                                
                                <div id="logContent" 
                                     style="background: #0f172a; border: 1px solid #374151; border-radius: 6px; padding: 1rem; min-height: 300px; max-height: 400px; overflow-y: auto; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.8rem; line-height: 1.4; color: #e2e8f0; white-space: pre-wrap; word-break: break-all;">
                                    <div style="color: #94a3b8; text-align: center; margin-top: 2rem;">
                                        🔄 Loading system logs...
                                    </div>
                                </div>
                                
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 0.75rem; padding-top: 0.75rem; border-top: 1px solid #374151;">
                                    <div style="display: flex; gap: 1rem; align-items: center;">
                                        <label style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.8rem; color: #94a3b8;">
                                            <input type="checkbox" id="logAutoScroll" checked 
                                                   style="margin: 0; accent-color: #3b82f6;">
                                            Auto-scroll to bottom
                                        </label>
                                        <label style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.8rem; color: #94a3b8;">
                                            Update every:
                                            <select id="logUpdateInterval" onchange="updateLogInterval(this.value)"
                                                    style="background: #1e293b; border: 1px solid #374151; color: #e2e8f0; padding: 0.25rem; border-radius: 3px; font-size: 0.8rem;">
                                                <option value="1000" selected>1 second</option>
                                                <option value="2000">2 seconds</option>
                                                <option value="5000">5 seconds</option>
                                                <option value="10000">10 seconds</option>
                                            </select>
                                        </label>
                                    </div>
                                    <div id="logStats" style="font-size: 0.75rem; color: #94a3b8;">
                                        Lines: <span id="logLineCount">0</span> | 
                                        Size: <span id="logSizeInfo">0 KB</span> |
                                        Last Update: <span id="logLastUpdate">Never</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        async function loadChatPageHistory() {
            const userGuidInput = document.getElementById('chatPageUserGuidInput');
            const container = document.getElementById('chatPageContainer');
            
            if (!userGuidInput || !container) return Promise.resolve();
            
            const userGuid = userGuidInput.value.trim();
            if (!userGuid) {
                showNotification('Please enter a user GUID', 'warning');
                return Promise.resolve();
            }
            
            container.innerHTML = '<div class="loading">Loading chat history...</div>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/dashboard/api/user-chat-history?user_guid=${encodeURIComponent(userGuid)}`);
                const data = await response.json();
                
                // Debug log handled by LogFire on backend
                
                if (data.error) {
                    container.innerHTML = `<div class="error">${escapeHtml(data.error)}</div>`;
                    return;
                }
                
                // Handle different response formats (same logic as loadChatHistory)
                let chatHistory = [];
                if (data.chat_history && Array.isArray(data.chat_history)) {
                    chatHistory = data.chat_history;
                } else if (data.chat_sessions) {
                    chatHistory = [];
                    Object.entries(data.chat_sessions).forEach(([sessionId, messages]) => {
                        if (Array.isArray(messages)) {
                            const messagesWithSession = messages.map(msg => ({
                                ...msg,
                                session_id: sessionId
                            }));
                            chatHistory = chatHistory.concat(messagesWithSession);
                        }
                    });
                } else if (data.sessions) {
                    chatHistory = [];
                    Object.values(data.sessions).forEach(messages => {
                        if (Array.isArray(messages)) {
                            chatHistory = chatHistory.concat(messages);
                        }
                    });
                }
                
                // Check if we have any data
                let hasData = false;
                if (chatHistory.length > 0) {
                    hasData = true;
                } else if (data.chat_sessions && Object.keys(data.chat_sessions).length > 0) {
                    hasData = true;
                } else if (data.total_sessions && data.total_sessions > 0) {
                    hasData = true;
                }
                
                // Check if we have any sessions (not just messages)
                const hasSessions = data.sessions_list && data.sessions_list.length > 0;
                
                if (hasData && hasSessions) {
                    // Display chat sessions even if they're empty
                    displayChatHistoryWithSessions(data, 'chatPageContainer');
                } else if (hasData && !hasSessions && chatHistory.length > 0) {
                    // Fallback for old format with messages only (only when NO sessions)
                    displayChatHistory(chatHistory, 'chatPageContainer');
                } else if (hasData) {
                    container.innerHTML = `
                        <div class="loading">
                            <h3>Chat Sessions</h3>
                            <p>Total sessions: ${data.total_sessions || 0}</p>
                            <p>No sessions found for this user</p>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="loading">
                            <h3>${data.user_info ? data.user_info.username : 'User'}</h3>
                            <p>No chat history found for this user</p>
                            <p>Sessions: ${data.total_sessions || 0}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                // Error logging handled by LogFire on backend
                container.innerHTML = `<div class="error">Failed to load chat history: ${error.message}</div>`;
            }
        }
        
        
        function switchChatPageSession(sessionId) {
            // Session switching handled by LogFire on backend
            
            // Update tab buttons
            document.querySelectorAll('#chatPageContainer .session-tab').forEach(tab => {
                const tabSessionId = tab.dataset.sessionId;
                const isActive = tabSessionId === sessionId;
                
                tab.classList.toggle('active', isActive);
                tab.style.background = isActive ? 'linear-gradient(135deg, #3b82f6, #6366f1)' : 'rgba(30, 41, 59, 0.5)';
                tab.style.color = isActive ? 'white' : '#94a3b8';
                tab.style.borderBottom = `3px solid ${isActive ? '#3b82f6' : 'transparent'}`;
            });
            
            // Update content containers
            document.querySelectorAll('#chatPageContainer .session-content').forEach(content => {
                const contentSessionId = content.dataset.sessionId;
                const isActive = contentSessionId === sessionId;
                
                content.classList.toggle('active', isActive);
                content.style.display = isActive ? 'block' : 'none';
            });
            
            // Save state
            setTimeout(saveApplicationState, 100);
        }
        
        async function sendChatMessage() {
            // Debug trace to backend for troubleshooting
            try {
                await fetch(`${API_BASE_URL}/dashboard/api/debug-trace`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: 'sendChatMessage() called from frontend' })
                });
            } catch (e) { /* Ignore debug trace failures */ }
            
            const userGuidInput = document.getElementById('chatPageUserGuidInput');
            const messageInput = document.getElementById('chatPageMessageInput');
            
            if (!userGuidInput || !messageInput) {
                showNotification('Input fields not found', 'error');
                return;
            }
            
            const userGuid = userGuidInput.value.trim();
            const message = messageInput.value.trim();
            
            // Debug trace the values
            try {
                await fetch(`${API_BASE_URL}/dashboard/api/debug-trace`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: `Frontend values: userGuid="${userGuid}", message="${message.substring(0, 50)}..."` })
                });
            } catch (e) { /* Ignore debug trace failures */ }
            
            if (!userGuid) {
                showNotification('Please enter a user GUID', 'warning');
                return;
            }
            
            if (!message) {
                showNotification('Please enter a message', 'warning');
                return;
            }
            
            // Get button reference and original text before try block
            const sendButton = document.querySelector('[onclick="sendChatMessage()"]');
            const originalText = sendButton ? sendButton.textContent : 'Send Message';
            
            try {
                // Show sending state
                if (sendButton) {
                    sendButton.textContent = 'Sending...';
                    sendButton.disabled = true;
                }
                
                // Send message to the API (LogFire logging handled on backend)
                const response = await fetch(`${API_BASE_URL}/dashboard/api/send-message`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_guid: userGuid,
                        message: message
                    })
                });
                
                const result = await response.json();
                
                if (result.error) {
                    showNotification(`Failed to send message: ${result.error}`, 'error');
                } else {
                    // Clear the message input
                    messageInput.value = '';
                    
                    // Refresh chat history after a short delay
                    setTimeout(() => {
                        loadChatPageHistory();
                    }, 1000);
                }
                
            } catch (error) {
                // Error logging handled by LogFire on backend
                showNotification(`Failed to send message: ${error.message}`, 'error');
            } finally {
                // Restore button state
                if (sendButton) {
                    sendButton.textContent = originalText;
                    sendButton.disabled = false;
                }
            }
        }
        
        function clearChatInput() {
            const messageInput = document.getElementById('chatPageMessageInput');
            if (messageInput) {
                messageInput.value = '';
                messageInput.focus();
            }
        }
        
        function setupChatMessageKeyHandler() {
            const messageInput = document.getElementById('chatPageMessageInput');
            if (messageInput) {
                messageInput.addEventListener('keydown', function(event) {
                    if (event.key === 'Enter') {
                        if (event.shiftKey) {
                            // Shift+Enter: Allow default behavior (new line)
                            return;
                        } else {
                            // Enter only: Send message
                            event.preventDefault();
                            sendChatMessage();
                        }
                    }
                });
            }
        }
        
        function showNotification(message, type = 'info') {
            // Log notification to backend via LogFire
            try {
                fetch(`${API_BASE_URL}/dashboard/api/debug-trace`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: `NOTIFICATION_${type.toUpperCase()}: ${message}` })
                }).catch(e => { /* Ignore backend logging failures */ });
            } catch (e) { /* Ignore debug trace failures */ }
            
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 10000;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                animation: slideIn 0.3s ease-out;
            `;
            
            // Set background color based on type
            const colors = {
                success: 'linear-gradient(135deg, #10b981, #059669)',
                error: 'linear-gradient(135deg, #ef4444, #dc2626)',
                warning: 'linear-gradient(135deg, #f59e0b, #d97706)',
                info: 'linear-gradient(135deg, #3b82f6, #2563eb)'
            };
            
            notification.style.background = colors[type] || colors.info;
            notification.textContent = message;
            
            // Add to page
            document.body.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in forwards';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
        
        function generateOverviewHTML(data) {
            const systemHealth = data.system_health || {};
            const factoryMetrics = data.factory_metrics || {};
            const performanceMetrics = data.performance_metrics || {};
            
            const overallStatus = systemHealth.overall_status || 'unknown';
            const activeManagers = factoryMetrics.active_managers || 0;
            const totalRequests = factoryMetrics.total_requests_all_users || 0;
            const totalActiveTasks = factoryMetrics.total_active_tasks || 0;
            
            const statusColor = {
                'healthy': '#28a745',
                'moderate': '#ffc107', 
                'degraded': '#fd7e14',
                'critical': '#dc3545',
                'unknown': '#6c757d'
            }[overallStatus] || '#6c757d';
            
            return `
                <style>
                .metric-card:hover {
                    transform: translateY(-5px);
                    border-color: rgba(59, 130, 246, 0.4);
                    box-shadow: 0 12px 32px rgba(59, 130, 246, 0.2);
                }
                </style>
                <div class="container" style="max-width: 1400px; margin: 2rem auto; padding: 0 2rem;">
                    <div class="metrics-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                        <div class="metric-card glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 1.5rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2); transition: all 0.3s ease; cursor: pointer;">
                            <div class="metric-title" style="font-size: 0.9rem; color: #94a3b8; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">Active Managers</div>
                            <div class="metric-value" style="font-size: 2.5rem; font-weight: 700; color: #f8fafc; margin-bottom: 0.5rem;">${activeManagers}</div>
                            <div class="metric-subtitle" style="font-size: 0.85rem; color: #64748b;">User-specific managers</div>
                        </div>
                        
                        <div class="metric-card glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 1.5rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2); transition: all 0.3s ease; cursor: pointer;">
                            <div class="metric-title" style="font-size: 0.9rem; color: #94a3b8; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">Total Requests</div>
                            <div class="metric-value" style="font-size: 2.5rem; font-weight: 700; color: #f8fafc; margin-bottom: 0.5rem;">${totalRequests}</div>
                            <div class="metric-subtitle" style="font-size: 0.85rem; color: #64748b;">All-time requests</div>
                        </div>
                        
                        <div class="metric-card glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 1.5rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2); transition: all 0.3s ease; cursor: pointer;">
                            <div class="metric-title" style="font-size: 0.9rem; color: #94a3b8; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">Active Tasks</div>
                            <div class="metric-value" style="font-size: 2.5rem; font-weight: 700; color: #f8fafc; margin-bottom: 0.5rem;">${totalActiveTasks}</div>
                            <div class="metric-subtitle" style="font-size: 0.85rem; color: #64748b;">Currently running</div>
                        </div>
                        
                        <div class="metric-card glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 1.5rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2); transition: all 0.3s ease; cursor: pointer;">
                            <div class="metric-title" style="font-size: 0.9rem; color: #94a3b8; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">System Health</div>
                            <div class="metric-value" style="font-size: 1.8rem; font-weight: 700; color: ${statusColor}; margin-bottom: 0.5rem;">${overallStatus.toUpperCase()}</div>
                            <div class="metric-subtitle" style="font-size: 0.85rem; color: #64748b;">Overall system status</div>
                        </div>
                    </div>
                    
                    <div class="section glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <h2 style="color: #93c5fd; margin-bottom: 1.5rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">System Information</h2>
                        <div id="systemInfo">
                            <p><strong>Last Updated:</strong> ${performanceMetrics.timestamp || 'Unknown'}</p>
                            <p><strong>System Status:</strong> <span style="color: ${statusColor}">${overallStatus.charAt(0).toUpperCase() + overallStatus.slice(1)}</span></p>
                            <p><strong>Active User Managers:</strong> ${activeManagers}</p>
                            <p><strong>Architecture:</strong> Enterprise-grade user-isolated scheduled request management</p>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function generateZairaHTML(data) {
            return `
                <div class="container" style="max-width: 1400px; margin: 2rem auto; padding: 0 2rem;">
                    <div class="section glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <h2 style="color: #93c5fd; margin-bottom: 1.5rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">User Management</h2>
                        <div class="tab-container">
                            <div class="tabs" style="display: flex; border-bottom: 2px solid rgba(59, 130, 246, 0.2); margin-bottom: 1rem;">
                                <button class="tab-button active" onclick="showTab('user-list')" style="background: none; border: none; padding: 0.75rem 1.5rem; cursor: pointer; font-size: 0.9rem; color: #3b82f6; border-bottom: 2px solid #3b82f6; background: rgba(59, 130, 246, 0.1);">User List</button>
                                <button class="tab-button" onclick="showTab('user-requests')" style="background: none; border: none; padding: 0.75rem 1.5rem; cursor: pointer; font-size: 0.9rem; color: #94a3b8; border-bottom: 2px solid transparent;">Scheduled Requests</button>
                                <button class="tab-button" onclick="showTab('chat-history')" style="background: none; border: none; padding: 0.75rem 1.5rem; cursor: pointer; font-size: 0.9rem; color: #94a3b8; border-bottom: 2px solid transparent;">Chat History</button>
                            </div>
                            
                            <!-- User List Tab -->
                            <div id="user-list" class="tab-content active" style="display: block;">
                                <div class="user-search-controls" style="display: flex; gap: 0.5rem; margin-bottom: 1rem; align-items: center;">
                                    <input type="text" id="userSearchInput" placeholder="Search users by username..." onkeyup="filterUsers()" style="flex: 1; padding: 0.5rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; font-size: 0.9rem; background: rgba(15, 23, 42, 0.8); color: #f8fafc;" />
                                    <button onclick="loadUserList()" style="padding: 0.5rem 1rem; background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9rem; font-weight: 500;">Refresh User List</button>
                                </div>
                                <div id="userListContainer">
                                    <div class="loading">Click "Refresh User List" to load all users</div>
                                </div>
                            </div>
                            
                            <!-- User Requests Tab -->
                            <div id="user-requests" class="tab-content" style="display: none;">
                                <div class="user-search-controls" style="display: flex; gap: 0.5rem; margin-bottom: 1rem; align-items: center;">
                                    <input type="text" id="userGuidInput" placeholder="Enter user GUID..." style="flex: 1; padding: 0.5rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; font-size: 0.9rem; background: rgba(15, 23, 42, 0.8); color: #f8fafc;" />
                                    <button onclick="loadUserRequests()" style="padding: 0.5rem 1rem; background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9rem; font-weight: 500;">Load Requests</button>
                                    <button onclick="loadSystemOverview()" style="padding: 0.5rem 1rem; background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9rem; font-weight: 500;">System Overview</button>
                                </div>
                                <div id="requestsContainer">
                                    <div class="loading">Select a user from User List or enter GUID to view scheduled requests</div>
                                </div>
                            </div>
                            
                            <!-- Chat History Tab -->
                            <div id="chat-history" class="tab-content" style="display: none;">
                                <div class="user-search-controls" style="display: flex; gap: 0.5rem; margin-bottom: 1rem; align-items: center;">
                                    <input type="text" id="chatUserGuidInput" placeholder="Enter user GUID..." style="flex: 1; padding: 0.5rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; font-size: 0.9rem; background: rgba(15, 23, 42, 0.8); color: #f8fafc;" />
                                    <button onclick="loadChatHistory()" style="padding: 0.5rem 1rem; background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9rem; font-weight: 500;">Load Chat History</button>
                                </div>
                                <div id="chatContainer">
                                    <div class="loading">Select a user from User List or enter GUID to view chat history</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function showEmptyState(page) {
            const pageTitle = page.charAt(0).toUpperCase() + page.slice(1);
            let content = '';
            
            switch(page) {
                case 'profile':
                    content = generateProfileContent();
                    break;
                case 'account':
                    content = generateAccountContent();
                    break;
                case 'chat':
                    content = generateChatContent();
                    break;
                case 'system':
                    content = generateSystemContent();
                    break;
                case 'subscription':
                    content = generateSubscriptionContent();
                    break;
                default:
                    content = `
                        <div class="empty-state" style="text-align: center; margin: 4rem 0;">
                            <div class="empty-state-icon">
                                ${getPageIcon(page)}
                            </div>
                            <h3 class="empty-state-title" style="color: #93c5fd; margin: 1rem 0; font-weight: 600;">${pageTitle} Page</h3>
                            <p class="empty-state-description" style="color: #94a3b8; font-size: 1.1rem;">The ${pageTitle} page is currently being developed.</p>
                        </div>
                    `;
            }
            
            document.getElementById('pageContent').innerHTML = content;
        }
        
        function generateProfileContent() {
            return `
                <div class="container" style="max-width: 1400px; margin: 2rem auto; padding: 0 2rem;">
                    <div class="section glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <h2 style="color: #93c5fd; margin-bottom: 1.5rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">User Profile</h2>
                        
                        <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 2rem; align-items: start;">
                            <div class="profile-avatar" style="text-align: center;">
                                <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #60a5fa, #a78bfa); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
                                    <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </div>
                                <button style="background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;">Change Avatar</button>
                            </div>
                            
                            <div class="profile-form">
                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; color: #94a3b8; margin-bottom: 0.5rem; font-weight: 500;">Display Name</label>
                                    <input type="text" value="Administrator" style="width: 100%; padding: 0.75rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; background: rgba(15, 23, 42, 0.8); color: #f8fafc; font-size: 1rem;">
                                </div>
                                
                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; color: #94a3b8; margin-bottom: 0.5rem; font-weight: 500;">Email Address</label>
                                    <input type="email" value="<EMAIL>" style="width: 100%; padding: 0.75rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; background: rgba(15, 23, 42, 0.8); color: #f8fafc; font-size: 1rem;">
                                </div>
                                
                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; color: #94a3b8; margin-bottom: 0.5rem; font-weight: 500;">Role</label>
                                    <select style="width: 100%; padding: 0.75rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; background: rgba(15, 23, 42, 0.8); color: #f8fafc; font-size: 1rem;">
                                        <option>System Administrator</option>
                                        <option>User Manager</option>
                                        <option>Analyst</option>
                                    </select>
                                </div>
                                
                                <div style="margin-bottom: 1.5rem;">
                                    <label style="display: block; color: #94a3b8; margin-bottom: 0.5rem; font-weight: 500;">
                                        <input type="checkbox" checked style="margin-right: 0.5rem;"> Email notifications
                                    </label>
                                    <label style="display: block; color: #94a3b8; margin-bottom: 0.5rem; font-weight: 500;">
                                        <input type="checkbox" style="margin-right: 0.5rem;"> SMS notifications
                                    </label>
                                    <label style="display: block; color: #94a3b8; margin-bottom: 0.5rem; font-weight: 500;">
                                        <input type="checkbox" checked style="margin-right: 0.5rem;"> System alerts
                                    </label>
                                </div>
                                
                                <button style="background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-size: 1rem; font-weight: 500;">Save Changes</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function generateAccountContent() {
            return `
                <div class="container" style="max-width: 1400px; margin: 2rem auto; padding: 0 2rem;">
                    <div class="section glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <h2 style="color: #93c5fd; margin-bottom: 1.5rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">Account Settings</h2>
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                            <div>
                                <h3 style="color: #60a5fa; margin-bottom: 1rem; font-weight: 600;">Security</h3>
                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; color: #94a3b8; margin-bottom: 0.5rem; font-weight: 500;">Current Password</label>
                                    <input type="password" style="width: 100%; padding: 0.75rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; background: rgba(15, 23, 42, 0.8); color: #f8fafc;">
                                </div>
                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; color: #94a3b8; margin-bottom: 0.5rem; font-weight: 500;">New Password</label>
                                    <input type="password" style="width: 100%; padding: 0.75rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; background: rgba(15, 23, 42, 0.8); color: #f8fafc;">
                                </div>
                                <div style="margin-bottom: 1.5rem;">
                                    <label style="display: block; color: #94a3b8; margin-bottom: 0.5rem; font-weight: 500;">Confirm Password</label>
                                    <input type="password" style="width: 100%; padding: 0.75rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; background: rgba(15, 23, 42, 0.8); color: #f8fafc;">
                                </div>
                                <button style="background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-size: 1rem; font-weight: 500;">Update Password</button>
                            </div>
                            
                            <div>
                                <h3 style="color: #60a5fa; margin-bottom: 1rem; font-weight: 600;">Two-Factor Authentication</h3>
                                <div style="background: rgba(15, 23, 42, 0.8); padding: 1.5rem; border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2); margin-bottom: 1rem;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                        <span style="color: #f8fafc; font-weight: 500;">Status</span>
                                        <span style="background: #dc2626; color: white; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">Disabled</span>
                                    </div>
                                    <p style="color: #94a3b8; font-size: 0.9rem; margin-bottom: 1rem;">Secure your account with two-factor authentication using your mobile device.</p>
                                    <button style="background: linear-gradient(135deg, #10b981, #059669); color: white; border: none; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;">Enable 2FA</button>
                                </div>
                                
                                <h3 style="color: #60a5fa; margin-bottom: 1rem; font-weight: 600;">API Keys</h3>
                                <div style="background: rgba(15, 23, 42, 0.8); padding: 1.5rem; border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2);">
                                    <div style="margin-bottom: 1rem;">
                                        <label style="display: block; color: #94a3b8; margin-bottom: 0.5rem; font-weight: 500;">API Key</label>
                                        <div style="display: flex; gap: 0.5rem;">
                                            <input type="text" value="ak_...hidden..." readonly style="flex: 1; padding: 0.5rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; background: rgba(30, 41, 59, 0.5); color: #94a3b8;">
                                            <button style="background: linear-gradient(135deg, #0891b2, #0e7490); color: white; border: none; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem;">Regenerate</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function generateChatContent() {
            return `
                <div class="container" style="max-width: 1400px; margin: 2rem auto; padding: 0 2rem;">
                    <div class="section glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <h2 style="color: #93c5fd; margin-bottom: 1.5rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">Direct Chat with AskZaira</h2>
                        
                        <div class="chat-container" style="height: 500px; display: flex; flex-direction: column;">
                            <div class="chat-messages" style="flex: 1; background: rgba(15, 23, 42, 0.8); border-radius: 8px; padding: 1rem; overflow-y: auto; margin-bottom: 1rem; border: 1px solid rgba(59, 130, 246, 0.2);">
                                <div class="chat-message" style="margin-bottom: 1rem;">
                                    <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #10b981, #059669); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 0.75rem;">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                                <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                                <circle cx="12" cy="16" r="1"></circle>
                                            </svg>
                                        </div>
                                        <span style="color: #10b981; font-weight: 600;">AskZaira</span>
                                        <span style="color: #64748b; font-size: 0.8rem; margin-left: auto;">Just now</span>
                                    </div>
                                    <div style="margin-left: 2.75rem; color: #e2e8f0; line-height: 1.5;">
                                        Hello! I'm AskZaira, your AI assistant. This is a debug-enabled direct chat interface. You can ask me questions about system status, user management, or any other administrative tasks. What would you like to know?
                                    </div>
                                </div>
                            </div>
                            
                            <div class="chat-input-container" style="display: flex; gap: 0.5rem;">
                                <input type="text" placeholder="Type your message here..." style="flex: 1; padding: 0.75rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; background: rgba(15, 23, 42, 0.8); color: #f8fafc; font-size: 1rem;">
                                <button style="background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-size: 1rem; font-weight: 500;">Send</button>
                            </div>
                        </div>
                        
                        <div style="margin-top: 1rem; padding: 1rem; background: rgba(245, 158, 11, 0.1); border: 1px solid rgba(245, 158, 11, 0.3); border-radius: 8px;">
                            <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" stroke-width="2" style="margin-right: 0.5rem;">
                                    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                                    <line x1="12" y1="9" x2="12" y2="13"></line>
                                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                                </svg>
                                <strong style="color: #f59e0b;">Debug Mode Active</strong>
                            </div>
                            <p style="color: #f59e0b; font-size: 0.9rem;">This chat interface has elevated permissions and can access system internals. Use responsibly.</p>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function generateSystemContent() {
            return `
                <div class="container" style="max-width: 1400px; margin: 2rem auto; padding: 0 2rem;">
                    <div class="section glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <h2 style="color: #93c5fd; margin-bottom: 1.5rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">System Information</h2>
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                            <div>
                                <h3 style="color: #60a5fa; margin-bottom: 1rem; font-weight: 600;">Database Status</h3>
                                <div style="background: rgba(15, 23, 42, 0.8); padding: 1.5rem; border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2); margin-bottom: 1rem;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                        <span style="color: #94a3b8;">PostgreSQL</span>
                                        <span style="background: #10b981; color: white; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">Connected</span>
                                    </div>
                                    <div style="color: #64748b; font-size: 0.9rem;">
                                        Host: localhost:5432<br>
                                        Database: vectordb<br>
                                        Pool: 15/20 connections
                                    </div>
                                </div>
                                
                                <div style="background: rgba(15, 23, 42, 0.8); padding: 1.5rem; border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                        <span style="color: #94a3b8;">Qdrant Vector DB</span>
                                        <span style="background: #10b981; color: white; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">Connected</span>
                                    </div>
                                    <div style="color: #64748b; font-size: 0.9rem;">
                                        Host: localhost:6333<br>
                                        Collections: 5 active<br>
                                        Vectors: 127,432 indexed
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h3 style="color: #60a5fa; margin-bottom: 1rem; font-weight: 600;">Performance Metrics</h3>
                                <div style="background: rgba(15, 23, 42, 0.8); padding: 1.5rem; border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2); margin-bottom: 1rem;">
                                    <div style="margin-bottom: 1rem;">
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.25rem;">
                                            <span style="color: #94a3b8;">CPU Usage</span>
                                            <span style="color: #60a5fa;">34%</span>
                                        </div>
                                        <div style="width: 100%; background: rgba(30, 41, 59, 0.5); border-radius: 4px; height: 8px;">
                                            <div style="width: 34%; background: linear-gradient(90deg, #10b981, #059669); height: 100%; border-radius: 4px;"></div>
                                        </div>
                                    </div>
                                    
                                    <div style="margin-bottom: 1rem;">
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.25rem;">
                                            <span style="color: #94a3b8;">Memory Usage</span>
                                            <span style="color: #60a5fa;">67%</span>
                                        </div>
                                        <div style="width: 100%; background: rgba(30, 41, 59, 0.5); border-radius: 4px; height: 8px;">
                                            <div style="width: 67%; background: linear-gradient(90deg, #f59e0b, #d97706); height: 100%; border-radius: 4px;"></div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.25rem;">
                                            <span style="color: #94a3b8;">Disk Usage</span>
                                            <span style="color: #60a5fa;">23%</span>
                                        </div>
                                        <div style="width: 100%; background: rgba(30, 41, 59, 0.5); border-radius: 4px; height: 8px;">
                                            <div style="width: 23%; background: linear-gradient(90deg, #3b82f6, #2563eb); height: 100%; border-radius: 4px;"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="background: rgba(15, 23, 42, 0.8); padding: 1.5rem; border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2);">
                                    <h4 style="color: #60a5fa; margin-bottom: 0.5rem;">API Response Times</h4>
                                    <div style="color: #94a3b8; font-size: 0.9rem;">
                                        Average: 127ms<br>
                                        95th percentile: 342ms<br>
                                        Requests/min: 1,247
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 2rem;">
                            <h3 style="color: #60a5fa; margin-bottom: 1rem; font-weight: 600;">System Configuration</h3>
                            <div style="background: rgba(15, 23, 42, 0.8); padding: 1.5rem; border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2);">
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; color: #94a3b8; font-size: 0.9rem;">
                                    <div><strong style="color: #f8fafc;">Version:</strong> AskZaira v2.1.0</div>
                                    <div><strong style="color: #f8fafc;">Environment:</strong> Production</div>
                                    <div><strong style="color: #f8fafc;">Uptime:</strong> 14 days, 7 hours</div>
                                    <div><strong style="color: #f8fafc;">Last Restart:</strong> Dec 15, 2024</div>
                                    <div><strong style="color: #f8fafc;">Python:</strong> 3.11.5</div>
                                    <div><strong style="color: #f8fafc;">Architecture:</strong> x86_64</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function generateSubscriptionContent() {
            return `
                <div class="container" style="max-width: 1400px; margin: 2rem auto; padding: 0 2rem;">
                    <div class="section glass-container" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 8px 25px rgba(0,0,0,0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <h2 style="color: #93c5fd; margin-bottom: 1.5rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">Subscription Management</h2>
                        
                        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
                            <div>
                                <div style="background: rgba(15, 23, 42, 0.8); padding: 2rem; border-radius: 12px; border: 1px solid rgba(59, 130, 246, 0.3); margin-bottom: 2rem; position: relative; overflow: hidden;">
                                    <div style="position: absolute; top: 0; right: 0; background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 0.5rem 1rem; border-bottom-left-radius: 8px; font-size: 0.8rem; font-weight: 600;">ENTERPRISE</div>
                                    <h3 style="color: #60a5fa; margin-bottom: 1rem; font-weight: 600;">Current Plan</h3>
                                    <div style="margin-bottom: 1.5rem;">
                                        <div style="color: #f8fafc; font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem;">$299<span style="font-size: 1rem; color: #94a3b8; font-weight: 400;">/month</span></div>
                                        <div style="color: #94a3b8;">Enterprise Plan - Unlimited everything</div>
                                    </div>
                                    
                                    <div style="margin-bottom: 1.5rem;">
                                        <h4 style="color: #60a5fa; margin-bottom: 0.5rem;">Plan Features</h4>
                                        <ul style="color: #94a3b8; margin: 0; padding-left: 1.5rem;">
                                            <li style="margin-bottom: 0.25rem;">Unlimited API requests</li>
                                            <li style="margin-bottom: 0.25rem;">Advanced user management</li>
                                            <li style="margin-bottom: 0.25rem;">Priority support</li>
                                            <li style="margin-bottom: 0.25rem;">Custom integrations</li>
                                            <li style="margin-bottom: 0.25rem;">99.9% SLA guarantee</li>
                                        </ul>
                                    </div>
                                    
                                    <div style="display: flex; gap: 1rem;">
                                        <button style="background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-size: 1rem; font-weight: 500;">Manage Plan</button>
                                        <button style="background: transparent; color: #94a3b8; border: 1px solid rgba(59, 130, 246, 0.3); padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-size: 1rem;">View Invoice</button>
                                    </div>
                                </div>
                                
                                <div>
                                    <h3 style="color: #60a5fa; margin-bottom: 1rem; font-weight: 600;">Billing History</h3>
                                    <div style="background: rgba(15, 23, 42, 0.8); border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2); overflow: hidden;">
                                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 1rem; padding: 1rem; background: rgba(30, 41, 59, 0.5); color: #94a3b8; font-weight: 600; font-size: 0.9rem;">
                                            <div>Date</div>
                                            <div>Amount</div>
                                            <div>Status</div>
                                            <div>Invoice</div>
                                        </div>
                                        
                                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 1rem; padding: 1rem; border-bottom: 1px solid rgba(59, 130, 246, 0.1); color: #e2e8f0;">
                                            <div>Dec 1, 2024</div>
                                            <div>$299.00</div>
                                            <div><span style="background: #10b981; color: white; padding: 0.2rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">Paid</span></div>
                                            <div><button style="color: #60a5fa; background: none; border: none; cursor: pointer; text-decoration: underline;">Download</button></div>
                                        </div>
                                        
                                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 1rem; padding: 1rem; border-bottom: 1px solid rgba(59, 130, 246, 0.1); color: #e2e8f0;">
                                            <div>Nov 1, 2024</div>
                                            <div>$299.00</div>
                                            <div><span style="background: #10b981; color: white; padding: 0.2rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">Paid</span></div>
                                            <div><button style="color: #60a5fa; background: none; border: none; cursor: pointer; text-decoration: underline;">Download</button></div>
                                        </div>
                                        
                                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 1rem; padding: 1rem; color: #e2e8f0;">
                                            <div>Oct 1, 2024</div>
                                            <div>$299.00</div>
                                            <div><span style="background: #10b981; color: white; padding: 0.2rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">Paid</span></div>
                                            <div><button style="color: #60a5fa; background: none; border: none; cursor: pointer; text-decoration: underline;">Download</button></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h3 style="color: #60a5fa; margin-bottom: 1rem; font-weight: 600;">Usage This Month</h3>
                                <div style="background: rgba(15, 23, 42, 0.8); padding: 1.5rem; border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2); margin-bottom: 1rem;">
                                    <div style="margin-bottom: 1rem;">
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #94a3b8;">API Requests</span>
                                            <span style="color: #60a5fa;">127K / Unlimited</span>
                                        </div>
                                        <div style="width: 100%; background: rgba(30, 41, 59, 0.5); border-radius: 4px; height: 8px;">
                                            <div style="width: 65%; background: linear-gradient(90deg, #10b981, #059669); height: 100%; border-radius: 4px;"></div>
                                        </div>
                                    </div>
                                    
                                    <div style="margin-bottom: 1rem;">
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #94a3b8;">Storage</span>
                                            <span style="color: #60a5fa;">45GB / Unlimited</span>
                                        </div>
                                        <div style="width: 100%; background: rgba(30, 41, 59, 0.5); border-radius: 4px; height: 8px;">
                                            <div style="width: 23%; background: linear-gradient(90deg, #3b82f6, #2563eb); height: 100%; border-radius: 4px;"></div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="color: #94a3b8;">Users</span>
                                            <span style="color: #60a5fa;">23 / Unlimited</span>
                                        </div>
                                        <div style="width: 100%; background: rgba(30, 41, 59, 0.5); border-radius: 4px; height: 8px;">
                                            <div style="width: 8%; background: linear-gradient(90deg, #8b5cf6, #7c3aed); height: 100%; border-radius: 4px;"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div style="background: rgba(15, 23, 42, 0.8); padding: 1.5rem; border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2);">
                                    <h4 style="color: #60a5fa; margin-bottom: 1rem;">Payment Method</h4>
                                    <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                                        <div style="width: 40px; height: 28px; background: linear-gradient(135deg, #1e40af, #3b82f6); border-radius: 4px; display: flex; align-items: center; justify-content: center; margin-right: 0.75rem; color: white; font-weight: 600; font-size: 0.8rem;">VISA</div>
                                        <div>
                                            <div style="color: #f8fafc; font-weight: 500;">•••• •••• •••• 4242</div>
                                            <div style="color: #94a3b8; font-size: 0.8rem;">Expires 12/26</div>
                                        </div>
                                    </div>
                                    <button style="background: transparent; color: #60a5fa; border: 1px solid rgba(59, 130, 246, 0.3); padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem; width: 100%;">Update Payment Method</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function getPageIcon(page) {
            const icons = {
                overview: `<svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="9" y1="9" x2="15" y2="9"></line>
                    <line x1="9" y1="15" x2="15" y2="15"></line>
                    <line x1="9" y1="12" x2="15" y2="12"></line>
                </svg>`,
                profile: `<svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>`,
                zaira: `<svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                    <circle cx="12" cy="16" r="1"></circle>
                </svg>`,
                account: `<svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <circle cx="12" cy="12" r="3"></circle>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                </svg>`,
                chat: `<svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>`,
                system: `<svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                    <line x1="8" y1="21" x2="16" y2="21"></line>
                    <line x1="12" y1="17" x2="12" y2="21"></line>
                </svg>`,
                subscription: `<svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                    <line x1="1" y1="10" x2="23" y2="10"></line>
                </svg>`
            };
            return `<div style="color: #60a5fa; filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.3));">${icons[page] || '<div style="font-size: 64px;">📋</div>'}</div>`;
        }
        
        function refreshCurrentPageData() {
            // Smart refresh based on current page and state
            if (currentPage === 'overview') {
                loadOverviewPage();
            } else if (currentPage === 'zaira') {
                // For Zaira page, check which tab is active and refresh accordingly
                const activeTab = document.querySelector('.tab-button.active');
                if (activeTab) {
                    const activeTabText = activeTab.textContent.trim().toLowerCase();
                    if (activeTabText.includes('user list')) {
                        // Refresh user list if it's already loaded
                        const userListContainer = document.getElementById('userListContainer');
                        if (userListContainer && !userListContainer.innerHTML.includes('Click "Refresh User List"')) {
                            loadUserList();
                        }
                    } else if (activeTabText.includes('scheduled requests')) {
                        // Refresh requests if user GUID is entered - preserve expanded states
                        const userGuidInput = document.getElementById('userGuidInput');
                        if (userGuidInput && userGuidInput.value.trim()) {
                            // Save current expanded states before refresh
                            const expandedState = saveRequestsExpandedState();
                            loadUserRequests().then(() => {
                                // Restore expanded states after refresh
                                setTimeout(() => restoreRequestsExpandedState(expandedState), 100);
                            });
                        }
                    } else if (activeTabText.includes('chat history')) {
                        // Refresh chat history if user GUID is entered - preserve expanded states
                        const chatUserGuidInput = document.getElementById('chatUserGuidInput');
                        if (chatUserGuidInput && chatUserGuidInput.value.trim()) {
                            // Save current expanded states before refresh
                            const expandedState = saveChatExpandedState();
                            loadChatHistory().then(() => {
                                // Restore expanded states after refresh
                                setTimeout(() => restoreChatExpandedState(expandedState), 100);
                            });
                        }
                    }
                }
            } else if (currentPage === 'chat') {
                // For Chat page, refresh chat history if user GUID is entered
                const chatPageUserGuidInput = document.getElementById('chatPageUserGuidInput');
                if (chatPageUserGuidInput && chatPageUserGuidInput.value.trim()) {
                    // Save current active session before refresh
                    const activeSessionTab = document.querySelector('#chatPageContainer .session-tab.active');
                    const activeSessionId = activeSessionTab ? activeSessionTab.dataset.sessionId : null;
                    
                    loadChatPageHistory().then(() => {
                        // Restore active session after refresh
                        if (activeSessionId) {
                            setTimeout(() => {
                                const restoredTab = document.querySelector(`#chatPageContainer .session-tab[data-session-id="${activeSessionId}"]`);
                                if (restoredTab) {
                                    switchChatPageSession(activeSessionId);
                                }
                            }, 100);
                        }
                    });
                }
            } else {
                // For other pages, just reload the content
                loadPageContent(currentPage);
            }
        }
        
        // User Management Functions (for Zaira page)
        let allUsers = [];
        let selectedUserGuid = null;
        
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => {
                tab.classList.remove('active');
                tab.style.display = 'none';
            });
            
            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
                button.style.color = '#94a3b8';
                button.style.borderBottom = '2px solid transparent';
                button.style.background = 'none';
            });
            
            // Show selected tab content
            const selectedTab = document.getElementById(tabName);
            if (selectedTab) {
                selectedTab.classList.add('active');
                selectedTab.style.display = 'block';
            }
            
            // Add active class to selected button
            const selectedButton = document.querySelector(`[onclick="showTab('${tabName}')"]`);
            if (selectedButton) {
                selectedButton.classList.add('active');
                selectedButton.style.color = '#3b82f6';
                selectedButton.style.borderBottom = '2px solid #3b82f6';
                selectedButton.style.background = 'rgba(59, 130, 246, 0.1)';
            }
            
            // Auto-load data when switching to specific tabs
            if (tabName === 'user-list') {
                loadUserList();
            }
            
            // Save state after tab change
            setTimeout(saveApplicationState, 100);
        }
        
        async function loadUserList() {
            const container = document.getElementById('userListContainer');
            if (!container) return;
            
            container.innerHTML = '<div class="loading">Loading user list...</div>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/dashboard/api/user-list`);
                const data = await response.json();
                
                if (data.users && Array.isArray(data.users)) {
                    allUsers = data.users;
                    displayUserList(allUsers);
                } else {
                    container.innerHTML = '<div class="error">No users found or invalid response format</div>';
                }
                
            } catch (error) {
                // console.error('Failed to load user list:', error);
                container.innerHTML = `<div class="error">Failed to load user list: ${error.message}</div>`;
            }
        }
        
        function displayUserList(users) {
            const container = document.getElementById('userListContainer');
            if (!container) return;
            
            if (users.length === 0) {
                container.innerHTML = '<div class="loading">No users found</div>';
                return;
            }
            
            let html = '';
            users.forEach(user => {
                html += `
                    <div class="user-list-item" style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 8px; margin-bottom: 0.5rem; background: rgba(30, 41, 59, 0.5); transition: all 0.3s ease;">
                        <div class="user-info" style="flex: 1;">
                            <div class="user-name" style="font-weight: 600; color: #f8fafc; margin-bottom: 0.25rem;">${escapeHtml(user.username || 'Unknown')}</div>
                            <div class="user-details" style="font-size: 0.8rem; color: #94a3b8;">
                                Email: ${escapeHtml(user.email || 'N/A')} | GUID: ${escapeHtml(user.user_guid || 'N/A')}
                            </div>
                        </div>
                        <div class="user-actions" style="display: flex; gap: 0.5rem;">
                            <button onclick="switchToUserRequests('${escapeHtml(user.user_guid)}')" style="padding: 0.3rem 0.6rem; background: linear-gradient(135deg, #3b82f6, #6366f1); color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">Requests</button>
                            <button onclick="switchToUserChat('${escapeHtml(user.user_guid)}')" style="padding: 0.3rem 0.6rem; background: linear-gradient(135deg, #0891b2, #0e7490); color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">Chat</button>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function filterUsers() {
            const searchInput = document.getElementById('userSearchInput');
            if (!searchInput) return;
            
            const searchTerm = searchInput.value.toLowerCase();
            const filteredUsers = allUsers.filter(user => {
                const username = (user.username || '').toLowerCase();
                const email = (user.email || '').toLowerCase();
                const guid = (user.user_guid || '').toLowerCase();
                return username.includes(searchTerm) || email.includes(searchTerm) || guid.includes(searchTerm);
            });
            
            displayUserList(filteredUsers);
        }
        
        function switchToUserRequests(userGuid) {
            showTab('user-requests');
            const input = document.getElementById('userGuidInput');
            if (input) {
                input.value = userGuid;
                loadUserRequests();
            }
        }
        
        function switchToUserChat(userGuid) {
            showTab('chat-history');
            const input = document.getElementById('chatUserGuidInput');
            if (input) {
                input.value = userGuid;
                loadChatHistory();
            }
        }
        
        async function loadUserRequests() {
            const userGuidInput = document.getElementById('userGuidInput');
            const container = document.getElementById('requestsContainer');
            
            if (!userGuidInput || !container) return Promise.resolve();
            
            const userGuid = userGuidInput.value.trim();
            if (!userGuid) {
                alert('Please enter a user GUID');
                return Promise.resolve();
            }
            
            container.innerHTML = '<div class="loading">Loading scheduled requests...</div>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/dashboard/api/user-requests?user_guid=${encodeURIComponent(userGuid)}`);
                const data = await response.json();
                
                if (data.requests && Array.isArray(data.requests)) {
                    displayUserRequests(data.requests);
                } else {
                    container.innerHTML = '<div class="loading">No scheduled requests found for this user</div>';
                }
                
            } catch (error) {
                // console.error('Failed to load user requests:', error);
                container.innerHTML = `<div class="error">Failed to load requests: ${error.message}</div>`;
            }
        }
        
        function displayUserRequests(requests) {
            const container = document.getElementById('requestsContainer');
            if (!container) return;
            
            if (requests.length === 0) {
                container.innerHTML = '<div class="loading">No scheduled requests found</div>';
                return;
            }
            
            // Use TabManager for organized request display
            const requestTabManager = new TabManager('requestsContainer', {
                tabClass: 'request-tab',
                contentClass: 'request-content',
                onTabSwitch: (tabId, tab) => {
                    // Optional: Track tab switches for analytics
                    // console.log(`Switched to request tab: ${tabId}`);
                },
                persistState: true
            });
            
            // Group requests by status for better organization
            const groupedRequests = {
                active: requests.filter(r => r.status === 'active'),
                completed: requests.filter(r => r.status === 'completed' || r.status === 'finished'),
                failed: requests.filter(r => r.status === 'failed' || r.status === 'error'),
                other: requests.filter(r => !['active', 'completed', 'finished', 'failed', 'error'].includes(r.status))
            };
            
            // Initialize tab system
            requestTabManager.initialize('Scheduled Requests', `Total requests: ${requests.length}`);
            
            // Add tabs for each group that has requests
            Object.entries(groupedRequests).forEach(([status, statusRequests]) => {
                if (statusRequests.length === 0) return;
                
                const tabLabel = status.charAt(0).toUpperCase() + status.slice(1);
                const tabContent = generateRequestGroupContent(statusRequests, status);
                
                requestTabManager.addTab(
                    `requests-${status}`,
                    tabLabel,
                    tabContent,
                    {
                        subtitle: `${statusRequests.length} request${statusRequests.length !== 1 ? 's' : ''}`
                    }
                );
            });
            
            // If no groups have requests, show a fallback tab
            if (Object.values(groupedRequests).every(group => group.length === 0)) {
                requestTabManager.addTab('requests-all', 'All Requests', generateRequestGroupContent(requests, 'all'));
            }
        }
        
        function generateRequestGroupContent(requests, groupType) {
            if (requests.length === 0) {
                return '<div class="empty-state">No requests in this category</div>';
            }
            
            let html = '';
            
            requests.forEach((request, index) => {
                const statusClass = request.status === 'active' ? 'status-active' : 'status-inactive';
                const scheduleInfo = request.schedule_info || {};
                const callTrace = request.call_trace || [];
                const debugMessages = request.debug_messages || []; // Now contains both debug + logging messages
                const expandId = `expand-${groupType}-${index}`;
                
                // Parse call trace and debug messages (which now contains both debug and logging)
                let callTraceArray = [];
                let debugMessagesArray = [];
                
                if (Array.isArray(callTrace)) {
                    callTraceArray = callTrace.filter(trace => trace && trace.trim() !== '');
                } else if (typeof callTrace === 'string' && callTrace.trim() !== '' && callTrace !== 'No call trace available') {
                    callTraceArray = [callTrace];
                }
                
                if (Array.isArray(debugMessages)) {
                    debugMessagesArray = debugMessages;
                } else if (debugMessages && typeof debugMessages === 'object') {
                    debugMessagesArray = Object.values(debugMessages);
                }
                
                // Sort debug messages chronologically if timestamps are available  
                debugMessagesArray.sort((a, b) => {
                    if (!a.timestamp && !b.timestamp) return 0;
                    if (!a.timestamp) return 1;
                    if (!b.timestamp) return -1;
                    return new Date(a.timestamp) - new Date(b.timestamp);
                });
                
                const hasCallTrace = callTraceArray.length > 0;
                const hasDebugOrLoggingMessages = debugMessagesArray.length > 0;
                const schedulePrompt = scheduleInfo.schedule_prompt || request.schedule_prompt || 'N/A';
                const targetPrompt = scheduleInfo.target_prompt || request.target_prompt || 'N/A';
                
                html += `
                <div class="request-card" style="margin-bottom: 1.5rem; padding: 1.5rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 12px; background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5); transition: all 0.3s ease;">
                    <div class="request-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 2px solid rgba(59, 130, 246, 0.2);">
                        <div>
                            <h4 style="margin: 0; color: #93c5fd; font-size: 1.1rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">Request: ${(request.scheduled_guid || 'N/A').substring(0, 12)}...</h4>
                            <div style="display: flex; gap: 1rem; margin-top: 0.5rem;">
                                <span class="${statusClass}" style="padding: 0.25rem 0.75rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 600; background: ${request.status === 'active' ? 'rgba(16, 185, 129, 0.2)' : 'rgba(107, 114, 128, 0.2)'}; color: ${request.status === 'active' ? '#10b981' : '#6b7280'}; border: 1px solid ${request.status === 'active' ? '#10b981' : '#6b7280'};">${(request.status || 'unknown').toUpperCase()}</span>
                                <span style="color: #94a3b8; font-size: 0.85rem;">Type: ${scheduleInfo.schedule_type || request.schedule_type || 'N/A'}</span>
                                ${hasCallTrace ? `<span style="background: rgba(59, 130, 246, 0.2); color: #93c5fd; padding: 0.125rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 600; border: 1px solid rgba(59, 130, 246, 0.3);">HAS TRACE</span>` : ''}
                                ${hasDebugOrLoggingMessages ? `<span style="background: rgba(220, 38, 38, 0.1); color: #fca5a5; padding: 0.125rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 600; border: 1px solid rgba(220, 38, 38, 0.3);">HAS DEBUG/LOG</span>` : ''}
                            </div>
                        </div>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn-small" onclick="toggleRequestExpand('${expandId}')" style="background: #3b82f6; color: white; padding: 0.5rem 1rem; border: none; border-radius: 6px; cursor: pointer; font-size: 0.85rem; font-weight: 500; transition: all 0.3s ease;">
                                Expand
                            </button>
                            ${request.status === 'active' ? 
                                `<button class="btn-cancel" onclick="cancelRequest('${request.scheduled_guid}')" style="background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; padding: 0.5rem 1rem; border: none; border-radius: 6px; cursor: pointer; font-size: 0.85rem; font-weight: 500;">Cancel</button>` : 
                                ''
                            }
                        </div>
                    </div>
                    
                    <div class="request-summary" style="color: #e2e8f0; margin-bottom: 1rem;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div>
                                <strong style="color: #93c5fd; text-transform: uppercase; font-size: 12px; letter-spacing: 0.05em;">Schedule Prompt Preview:</strong>
                                <div style="color: #94a3b8; font-size: 0.9rem; margin-top: 0.25rem; line-height: 1.4;">
                                    ${schedulePrompt.length > 100 ? schedulePrompt.substring(0, 100) + '...' : schedulePrompt}
                                </div>
                            </div>
                            <div>
                                <strong style="color: #93c5fd; text-transform: uppercase; font-size: 12px; letter-spacing: 0.05em;">Target Prompt Preview:</strong>
                                <div style="color: #94a3b8; font-size: 0.9rem; margin-top: 0.25rem; line-height: 1.4;">
                                    ${targetPrompt.length > 100 ? targetPrompt.substring(0, 100) + '...' : targetPrompt}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="${expandId}" class="request-expanded" style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid rgba(59, 130, 246, 0.2);">
                        <div style="display: grid; gap: 1.5rem;">
                            <!-- Full Schedule Prompt -->
                            <div class="prompt-section">
                                <h5 style="margin: 0 0 0.5rem 0; color: #93c5fd; font-size: 1rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">Schedule Prompt (Full)</h5>
                                <div style="background: rgba(15, 23, 42, 0.8); border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; padding: 1rem; max-height: 300px; overflow-y: auto;">
                                    <div class="prompt-content" style="color: #e2e8f0; font-size: 0.9rem; font-family: 'Monaco', 'Consolas', 'Courier New', monospace; line-height: 1.5; white-space: pre-wrap;">
                                        ${escapeHtml(normalizeIndentation(schedulePrompt))}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Full Target Prompt -->
                            <div class="prompt-section">
                                <h5 style="margin: 0 0 0.5rem 0; color: #86efac; font-size: 1rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">Target Prompt (Full)</h5>
                                <div style="background: rgba(15, 23, 42, 0.8); border: 1px solid rgba(16, 185, 129, 0.2); border-radius: 6px; padding: 1rem; max-height: 300px; overflow-y: auto;">
                                    <div class="prompt-content" style="color: #e2e8f0; font-size: 0.9rem; font-family: 'Monaco', 'Consolas', 'Courier New', monospace; line-height: 1.5; white-space: pre-wrap;">
                                        ${escapeHtml(normalizeIndentation(targetPrompt))}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Call Trace, Debug Messages, and Chat History Buttons -->
                            <div style="display: flex; gap: 0.5rem; justify-content: center; flex-wrap: wrap;">
                                ${hasCallTrace ? `
                                <button onclick="toggleRequestTrace('trace-${expandId}')" 
                                        style="background: #3b82f6; color: white; padding: 0.5rem 1rem; border: none; border-radius: 6px; cursor: pointer; font-size: 0.85rem; font-weight: 500; transition: all 0.2s;"
                                        onmouseover="this.style.background='#2563eb'"
                                        onmouseout="this.style.background='#3b82f6'">
                                    View Call Trace (${callTraceArray.length} steps)
                                </button>` : ''}
                                ${hasDebugOrLoggingMessages ? `
                                <button onclick="toggleRequestDebug('debug-${expandId}')" 
                                        style="background: #dc2626; color: white; padding: 0.5rem 1rem; border: none; border-radius: 6px; cursor: pointer; font-size: 0.85rem; font-weight: 500; transition: all 0.2s;"
                                        onmouseover="this.style.background='#b91c1c'"
                                        onmouseout="this.style.background='#dc2626'">
                                    View Debug & Logging Messages (${debugMessagesArray.length})
                                </button>` : ''}
                                <button onclick="navigateToChatSession('${request.chat_session_guid || 'Unknown'}', '${request.user_guid || 'Unknown'}')" 
                                        title="Navigate to chat history for this request"
                                        style="background: #f59e0b; color: white; padding: 0.5rem 1rem; border: none; border-radius: 6px; cursor: pointer; font-size: 0.85rem; font-weight: 600; transition: all 0.2s; box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);"
                                        onmouseover="this.style.background='#d97706'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(245, 158, 11, 0.4)'"
                                        onmouseout="this.style.background='#f59e0b'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(245, 158, 11, 0.3)'">
                                    📋 Chat History
                                </button>
                            </div>
                            
                            <!-- Call Trace Section (Hidden by default) -->
                            ${hasCallTrace ? `
                            <div id="trace-${expandId}" class="trace-dropdown" style="position: relative;">
                                <div class="trace-section" style="background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; padding: 1rem;">
                                    <h5 style="margin: 0 0 0.5rem 0; color: #60a5fa; font-size: 1rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">Execution Call Trace</h5>
                                    <div style="max-height: 300px; overflow-y: auto;">
                                        ${callTraceArray.map((step, stepIndex) => `
                                            <div style="margin-bottom: 0.5rem; padding: 0.5rem; background: rgba(15, 23, 42, 0.8); border-radius: 4px; border-left: 3px solid #3b82f6;">
                                                <strong style="color: #93c5fd;">${stepIndex + 1}.</strong> <span style="color: #e2e8f0; font-family: monospace; font-size: 0.85rem;">${escapeHtml(step)}</span>
                                            </div>
                                        `).join('')}
                                    </div>
                                    <button onclick="toggleRequestTrace('trace-${expandId}')" style="margin-top: 0.5rem; background: #6b7280; color: white; padding: 0.25rem 0.5rem; border: none; border-radius: 4px; cursor: pointer; font-size: 0.75rem;">Close</button>
                                </div>
                            </div>` : ''}
                            
                            <!-- Debug & Logging Messages Section (Hidden by default) -->
                            ${hasDebugOrLoggingMessages ? `
                            <div id="debug-${expandId}" class="debug-dropdown" style="position: relative;">
                                <div class="debug-section" style="background: rgba(220, 38, 38, 0.1); border: 1px solid rgba(220, 38, 38, 0.2); border-radius: 6px; padding: 1rem;">
                                    <h5 style="margin: 0 0 0.5rem 0; color: #fca5a5; font-size: 1rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">Debug & Logging Messages</h5>
                                    <div style="max-height: 750px; overflow-y: auto; overflow-x: hidden; border: 1px solid rgba(220, 38, 38, 0.1); border-radius: 4px; padding: 0.5rem;">
                                        ${debugMessagesArray.map((msg, msgIndex) => {
                                            // Determine if this is a debug or logging message (matching Chat History logic)
                                            const isDebug = msg.role === 'debug' || msg.role === 'system' || 
                                                           msg.message_type === 'debug' || msg.content?.startsWith('DEBUG:');
                                            const isLogging = msg.role === 'logging' || msg.message_type === 'logging' || msg.content?.startsWith('LOGGING:');
                                            
                                            // Set colors and styling based on message type (matching Chat History exactly)
                                            const colors = isLogging ? {
                                                background: 'rgba(59, 130, 246, 0.1)',
                                                border: '#3b82f6',
                                                textColor: '#93c5fd',
                                                icon: '📋',
                                                label: 'Logging'
                                            } : {
                                                background: 'rgba(220, 38, 38, 0.1)',
                                                border: '#dc2626',
                                                textColor: '#fca5a5', 
                                                icon: '🐛',
                                                label: 'Debug'
                                            };
                                            
                                            return `
                                            <div style="margin-bottom: 0.75rem; padding: 0.5rem; background: ${colors.background}; border-radius: 6px; border-left: 4px solid ${colors.border};">
                                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.25rem;">
                                                    <span style="font-size: 0.9rem;">${colors.icon}</span>
                                                    <strong style="color: ${colors.textColor}; font-size: 0.85rem;">${colors.label} #${msgIndex + 1}</strong>
                                                    <span style="color: #6b7280; font-size: 0.75rem;">${msg.timestamp ? new Date(msg.timestamp).toLocaleString() : ''}</span>
                                                </div>
                                                <div style="font-family: monospace; font-size: 0.8rem; color: #e2e8f0; white-space: pre-wrap;">${escapeHtml(msg.content || msg)}</div>
                                            </div>`;
                                        }).join('')}
                                    </div>
                                    <button onclick="toggleRequestDebug('debug-${expandId}')" style="margin-top: 0.5rem; background: #6b7280; color: white; padding: 0.25rem 0.5rem; border: none; border-radius: 4px; cursor: pointer; font-size: 0.75rem;">Close</button>
                                </div>
                            </div>` : ''}
                            
                            <div style="text-align: right; margin-top: 1rem;">
                                <button class="btn-small" onclick="toggleRequestExpand('${expandId}')" style="background: #6b7280; color: white; padding: 0.5rem 1rem; border: none; border-radius: 6px; cursor: pointer; font-size: 0.85rem; font-weight: 500;">
                                    Collapse
                                </button>
                            </div>
                        </div>
                    </div>
                </div>`;
            });
            
            return html;
        }
        
        async function loadChatHistory() {
            const userGuidInput = document.getElementById('chatUserGuidInput');
            const container = document.getElementById('chatContainer');
            
            if (!userGuidInput || !container) return Promise.resolve();
            
            const userGuid = userGuidInput.value.trim();
            if (!userGuid) {
                alert('Please enter a user GUID');
                return Promise.resolve();
            }
            
            container.innerHTML = '<div class="loading">Loading chat history...</div>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/dashboard/api/user-chat-history?user_guid=${encodeURIComponent(userGuid)}`);
                const data = await response.json();
                
                // console.log('Chat history response:', data); // Debug log
                // console.log('Response keys:', Object.keys(data)); // Debug log
                
                if (data.error) {
                    container.innerHTML = `<div class="error">${escapeHtml(data.error)}</div>`;
                    return;
                }
                
                // Handle different response formats
                let chatHistory = [];
                if (data.chat_history && Array.isArray(data.chat_history)) {
                    chatHistory = data.chat_history;
                } else if (data.chat_sessions) {
                    // The API returns: { chat_sessions: { sessionId: [messages] } }
                    // Each session contains an array of messages
                    chatHistory = [];
                    Object.entries(data.chat_sessions).forEach(([sessionId, messages]) => {
                        if (Array.isArray(messages)) {
                            // Add session_id to each message for grouping
                            const messagesWithSession = messages.map(msg => ({
                                ...msg,
                                session_id: sessionId
                            }));
                            chatHistory = chatHistory.concat(messagesWithSession);
                        }
                    });
                } else if (data.sessions) {
                    // Alternative session format
                    chatHistory = [];
                    Object.values(data.sessions).forEach(messages => {
                        if (Array.isArray(messages)) {
                            chatHistory = chatHistory.concat(messages);
                        }
                    });
                }
                
                // console.log('Processed chat history:', chatHistory); // Debug log
                
                // Check if we have any data in different ways
                let hasData = false;
                if (chatHistory.length > 0) {
                    hasData = true;
                } else if (data.chat_sessions && Object.keys(data.chat_sessions).length > 0) {
                    hasData = true;
                } else if (data.total_sessions && data.total_sessions > 0) {
                    hasData = true;
                }
                
                // Check if we have any sessions (not just messages)
                const hasSessions = data.sessions_list && data.sessions_list.length > 0;
                
                if (hasData && hasSessions) {
                    // Display chat sessions even if they're empty
                    displayChatHistoryWithSessions(data);
                } else if (hasData && !hasSessions && chatHistory.length > 0) {
                    // Fallback for old format with messages only (only when NO sessions)
                    displayChatHistory(chatHistory);
                } else if (hasData) {
                    container.innerHTML = `
                        <div class="loading">
                            <h3>Chat Sessions</h3>
                            <p>Total sessions: ${data.total_sessions || 0}</p>
                            <p>No sessions found for this user</p>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="loading">
                            <h3>${data.user_info ? data.user_info.username : 'User'}</h3>
                            <p>No chat history found for this user</p>
                            <p>Sessions: ${data.total_sessions || 0}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                // console.error('Failed to load chat history:', error);
                container.innerHTML = `<div class="error">Failed to load chat history: ${error.message}</div>`;
            }
        }
        
        function displayChatHistory(chatHistory, containerId = 'chatContainer') {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            if (!chatHistory || chatHistory.length === 0) {
                container.innerHTML = '<div class="loading">No chat history found</div>';
                return;
            }
            
            // Determine which switch function to use based on containerId
            const switchFunction = containerId === 'chatPageContainer' ? 'switchChatPageSession' : 'switchChatSession';
            
            // Check if this is being called from within a session container (don't show header)
            const isSessionContainer = containerId.startsWith('session-messages-');
            
            // Group messages by session
            const sessions = {};
            chatHistory.forEach(message => {
                const sessionId = message.session_id || message.sessionId || 'default';
                if (!sessions[sessionId]) {
                    sessions[sessionId] = [];
                }
                sessions[sessionId].push(message);
            });
            
            // console.log('Grouped sessions:', sessions); // Debug log
            
            const sessionsList = Object.keys(sessions);
            
            let html = '';
            
            // Only show header and tabs if not within a session container
            if (!isSessionContainer) {
                html += `
                    <div style="margin-bottom: 2rem;">
                        <h3 style="color: #93c5fd; margin-bottom: 0.5rem; text-transform: uppercase; letter-spacing: 0.05em; font-weight: 600;">Chat History</h3>
                        <p style="color: #94a3b8;">Total messages: ${chatHistory.length} • Sessions: ${sessionsList.length}</p>
                    </div>`;
            }
            
            // Create session tabs (only show if not within a session container)
            if (sessionsList.length > 0 && !isSessionContainer) {
                html += `
                    <div class="session-tabs-container" style="margin-bottom: 1.5rem;">
                        <div class="session-tabs" style="display: flex; gap: 0.5rem; border-bottom: 2px solid rgba(59, 130, 246, 0.2); padding-bottom: 0; margin-bottom: 1rem; overflow-x: auto; overflow-y: hidden; max-width: 100%;">`;
                
                sessionsList.forEach((sessionId, index) => {
                    const sessionIdStr = String(sessionId || '');
                    const sessionMessages = sessions[sessionId] || [];
                    const messageCount = sessionMessages.length;
                    
                    // Get most recent message date
                    const lastActivity = sessionMessages.length > 0 ? 
                        new Date(Math.max(...sessionMessages.map(m => new Date(m.timestamp || 0).getTime()))).toLocaleDateString() : 'No activity';
                    
                    const isActive = index === 0 ? 'active' : '';
                    
                    html += `
                        <button class="session-tab ${isActive}" 
                                onclick="${switchFunction}('${sessionIdStr}')" 
                                data-session-id="${sessionIdStr}"
                                style="padding: 0.75rem 1.25rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 8px 8px 0 0; cursor: pointer; transition: all 0.2s ease; background: ${isActive ? 'linear-gradient(135deg, #3b82f6, #6366f1)' : 'rgba(30, 41, 59, 0.5)'}; color: ${isActive ? 'white' : '#94a3b8'}; border-bottom: 3px solid ${isActive ? '#3b82f6' : 'transparent'}; backdrop-filter: blur(5px); min-width: 140px; white-space: nowrap; font-size: 0.85rem;">
                            <div style="text-align: left;">
                                <div style="font-weight: 600; font-size: 0.9rem;">Session ${sessionIdStr.substring(0, 8)}</div>
                                <div style="font-size: 0.75rem; opacity: 0.8; margin-top: 0.25rem;">${messageCount} msg • ${lastActivity}</div>
                            </div>
                        </button>`;
                });
                
                html += `
                        </div>
                    </div>`;
            }
            
            // Create session content containers (only if not within a session container)
            sessionsList.forEach((sessionId, index) => {
                const sessionIdStr = String(sessionId || '');
                const sortedMessages = sessions[sessionId].sort((a, b) => {
                    const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
                    const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
                    return timeB - timeA;
                });
                
                const isActive = index === 0 ? 'active' : '';
                
                // If we're within a session container, just render messages directly without wrappers
                if (!isSessionContainer) {
                    html += `
                        <div class="session-content ${isActive}" data-session-id="${sessionIdStr}" style="display: ${index === 0 ? 'block' : 'none'};">
                            <div class="session-messages">`;
                }
                
                // Process messages and group debug messages appropriately
                const processedMessages = [];
                let pendingDebugMessages = [];
                
                // Check if we start with debug/logging messages (indicating first HumanMessage is not in data)
                let startsWithDebugMessages = false;
                if (sortedMessages.length > 0) {
                    const firstMessage = sortedMessages[0];
                    const isFirstMessageDebug = firstMessage.role === 'debug' || firstMessage.role === 'system' || 
                                              firstMessage.message_type === 'debug' || firstMessage.content?.startsWith('DEBUG:');
                    const isFirstMessageLogging = firstMessage.role === 'logging' || firstMessage.message_type === 'logging' || firstMessage.content?.startsWith('LOGGING:');
                    if (isFirstMessageDebug || isFirstMessageLogging) {
                        startsWithDebugMessages = true;
                    }
                }
                
                // DEBUG: Add logging to understand message flow
                try {
                    fetch(`${API_BASE_URL}/dashboard/api/debug-trace`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message: `SORTED MESSAGES: ${sortedMessages.map((m, i) => `${i}: ${m.role}/${m.message_type} "${m.content?.substring(0, 30)}..." [${m.timestamp}]`).join(' | ')}` })
                    }).catch(e => {});
                } catch (e) {}
                
                // First pass: process all messages
                for (let i = 0; i < sortedMessages.length; i++) {
                    const message = sortedMessages[i];
                    const isDebugMessage = message.role === 'debug' || message.role === 'system' || 
                                         message.message_type === 'debug' || message.content?.startsWith('DEBUG:');
                    const isLoggingMessage = message.role === 'logging' || message.message_type === 'logging' || message.content?.startsWith('LOGGING:');
                    const isAnyDebugOrLoggingMessage = isDebugMessage || isLoggingMessage;
                    const isHumanMessage = message.role === 'user' || message.role === 'human';
                    
                    try {
                        fetch(`${API_BASE_URL}/dashboard/api/debug-trace`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ message: `PROCESSING ${i}: role="${message.role}" type="${message.message_type}" isDebug=${isDebugMessage} isHuman=${isHumanMessage} pendingCount=${pendingDebugMessages.length}` })
                        }).catch(e => {});
                    } catch (e) {}
                    
                    if (isAnyDebugOrLoggingMessage) {
                        // Accumulate debug/logging messages
                        pendingDebugMessages.push(message);
                        try {
                            fetch(`${API_BASE_URL}/dashboard/api/debug-trace`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ message: `ACCUMULATED DEBUG: now ${pendingDebugMessages.length} pending` })
                            }).catch(e => {});
                        } catch (e) {}
                    } else if (isHumanMessage) {
                        // ALWAYS create In-Progress segment for any accumulated debug messages when encountering a HumanMessage
                        if (pendingDebugMessages.length > 0) {
                            try {
                                fetch(`${API_BASE_URL}/dashboard/api/debug-trace`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({ message: `CREATING IN-PROGRESS: ${pendingDebugMessages.length} debug messages before HumanMessage` })
                                }).catch(e => {});
                            } catch (e) {}
                            // Sort accumulated debug messages chronologically (oldest first)
                            const sortedAccumulatedMessages = pendingDebugMessages.sort((a, b) => {
                                const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
                                const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
                                return timeA - timeB;
                            });
                            
                            // Create In-Progress segment for accumulated debug messages
                            const inProgressForAccumulated = {
                                role: 'assistant',
                                content: 'Finding parent message...',
                                timestamp: sortedAccumulatedMessages[sortedAccumulatedMessages.length - 1].timestamp, // Use timestamp of latest debug message
                                debug_messages: sortedAccumulatedMessages,
                                message_type: 'in-progress',
                                tokens_used: 0
                            };
                            processedMessages.push(inProgressForAccumulated);
                            pendingDebugMessages = [];
                        }
                        
                        // Add the human message
                        processedMessages.push(message);
                        
                        // Collect any debug messages that follow this human message
                        const debugMessagesAfterHuman = [];
                        let j = i + 1;
                        while (j < sortedMessages.length) {
                            const nextMessage = sortedMessages[j];
                            const isNextDebug = nextMessage.role === 'debug' || nextMessage.role === 'system' || 
                                              nextMessage.message_type === 'debug' || nextMessage.content?.startsWith('DEBUG:');
                            
                            if (isNextDebug) {
                                debugMessagesAfterHuman.push(nextMessage);
                                j++;
                            } else {
                                break;
                            }
                        }
                        
                        // If there are debug messages after this human message, create In-Progress segment
                        if (debugMessagesAfterHuman.length > 0) {
                            // Sort debug messages chronologically (oldest first)
                            const sortedDebugMessages = debugMessagesAfterHuman.sort((a, b) => {
                                const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
                                const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
                                return timeA - timeB;
                            });
                            
                            const inProgressMessage = {
                                role: 'assistant',
                                content: 'Finding parent message...',
                                timestamp: sortedDebugMessages[0].timestamp,
                                debug_messages: sortedDebugMessages,
                                message_type: 'in-progress',
                                tokens_used: 0
                            };
                            processedMessages.push(inProgressMessage);
                            
                            // Skip these debug messages in the main loop
                            i = j - 1;
                        }
                        
                        // Clear any pending debug messages (they shouldn't be attached to human messages)
                        pendingDebugMessages = [];
                    } else {
                        // Non-human, non-debug message (likely assistant)
                        if (pendingDebugMessages.length > 0) {
                            // Sort pending debug messages chronologically (oldest first)
                            const sortedPendingMessages = pendingDebugMessages.sort((a, b) => {
                                const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
                                const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
                                return timeA - timeB;
                            });
                            
                            // Attach sorted debug messages to this message
                            if (!message.debug_messages) {
                                message.debug_messages = [];
                            }
                            message.debug_messages.push(...sortedPendingMessages);
                            pendingDebugMessages = [];
                        }
                        processedMessages.push(message);
                    }
                }
                
                // Handle any remaining debug messages at the end
                if (pendingDebugMessages.length > 0) {
                    // Sort remaining debug messages chronologically (oldest first)
                    const sortedRemainingMessages = pendingDebugMessages.sort((a, b) => {
                        const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
                        const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
                        return timeA - timeB;
                    });
                    
                    const inProgressMessage = {
                        role: 'assistant',
                        content: 'Finding parent message...',
                        timestamp: sortedRemainingMessages[0].timestamp,
                        debug_messages: sortedRemainingMessages,
                        message_type: 'in-progress',
                        tokens_used: 0
                    };
                    processedMessages.push(inProgressMessage);
                }
                
                processedMessages.forEach((message, messageIndex) => {
                    const roleClass = message.role ? `chat-role-${message.role}` : '';
                    const timestamp = message.timestamp ? new Date(message.timestamp).toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true
                    }) : 'Unknown time';
                    
                    // Special handling for in-progress messages
                    const isInProgress = message.message_type === 'in-progress';
                    const roleDisplay = message.role === 'user' ? 'User' : 
                                      message.role === 'assistant' ? (isInProgress ? 'In-Progress' : 'Assistant') : 
                                      message.role || 'Unknown';
                    const roleIcon = message.role === 'user' ? '👤' : 
                                   message.role === 'assistant' ? (isInProgress ? '⏳' : '🤖') : '❓';
                    
                    // Color coding based on role
                    const messageColors = message.role === 'user' ? {
                        background: 'rgba(59, 130, 246, 0.15)',
                        border: 'rgba(59, 130, 246, 0.4)',
                        headerBorder: 'rgba(59, 130, 246, 0.2)',
                        tokenBorder: 'rgba(59, 130, 246, 0.2)'
                    } : message.role === 'assistant' ? (isInProgress ? {
                        background: 'rgba(251, 191, 36, 0.15)',
                        border: 'rgba(251, 191, 36, 0.4)',
                        headerBorder: 'rgba(251, 191, 36, 0.2)',
                        tokenBorder: 'rgba(251, 191, 36, 0.2)'
                    } : {
                        background: 'rgba(16, 185, 129, 0.15)',
                        border: 'rgba(16, 185, 129, 0.4)',
                        headerBorder: 'rgba(16, 185, 129, 0.2)',
                        tokenBorder: 'rgba(16, 185, 129, 0.2)'
                    }) : {
                        background: 'rgba(30, 41, 59, 0.8)',
                        border: 'rgba(59, 130, 246, 0.2)',
                        headerBorder: 'rgba(59, 130, 246, 0.1)',
                        tokenBorder: 'rgba(59, 130, 246, 0.1)'
                    };
                    
                    const contentDisplay = normalizeIndentation(message.content || 'No content');
                    
                    // Handle call_trace display for assistant messages
                    const hasCallTrace = message.call_trace && Array.isArray(message.call_trace) && message.call_trace.length > 0;
                    const traceId = `chat-trace-${sessionIdStr}-${messageIndex}`;
                    
                    // Handle debug messages attached to assistant messages
                    const hasDebugMessages = message.debug_messages && Array.isArray(message.debug_messages) && message.debug_messages.length > 0;
                    const debugId = `chat-debug-${sessionIdStr}-${messageIndex}`;
                    
                    html += `
                    <div class="chat-message ${roleClass}" style="background: ${messageColors.background}; border: 1px solid ${messageColors.border}; border-radius: 12px; padding: 1.5rem; margin-bottom: 1rem; backdrop-filter: blur(10px); box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5);">
                        <div class="chat-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid ${messageColors.headerBorder};">
                            <span style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="font-size: 1.25rem;">${roleIcon}</span>
                                <strong style="color: #93c5fd; font-size: 1rem;">${roleDisplay}</strong>
                                <span style="color: #94a3b8; font-weight: 400;">• ${message.message_type || 'Message'}</span>
                                ${hasCallTrace ? `<span style="background: rgba(59, 130, 246, 0.2); color: #93c5fd; padding: 0.125rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 600; border: 1px solid rgba(59, 130, 246, 0.3);">HAS TRACE</span>` : ''}
                                ${hasDebugMessages ? `<span style="background: rgba(220, 38, 38, 0.1); color: #fca5a5; padding: 0.125rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 600; border: 1px solid rgba(220, 38, 38, 0.3);">HAS DEBUG</span>` : ''}
                            </span>
                            <span style="color: #94a3b8; font-weight: 500;">${timestamp}</span>
                        </div>
                        <div class="chat-content" style="color: #e2e8f0; font-size: 0.95rem; line-height: 1.6; white-space: pre-wrap; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                            ${escapeHtml(contentDisplay)}
                        </div>
                        <div class="chat-tokens" style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid ${messageColors.tokenBorder};">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; gap: 1rem; font-size: 0.85rem; color: #94a3b8;">
                                    ${message.tokens_used ? `<span><strong>Tokens:</strong> ${message.tokens_used}</span>` : ''}
                                    <span><strong>Session:</strong> ${sessionIdStr}</span>
                                    <span><strong>Length:</strong> ${message.content ? message.content.length : 0} chars</span>
                                </div>
                                <div style="display: flex; gap: 0.5rem;">
                                    ${hasCallTrace ? `
                                    <button class="btn-trace" onclick="toggleChatCallTrace('${traceId}')" style="font-size: 0.75rem; padding: 0.25rem 0.5rem; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                        View Call Trace (${message.call_trace.length} steps)
                                    </button>` : ''}
                                    ${hasDebugMessages ? `
                                    <button class="btn-debug" onclick="toggleDebugMessage('${debugId}')" style="font-size: 0.75rem; padding: 0.25rem 0.5rem; background: #dc2626; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                        Debug & Logging (${message.debug_messages.length})
                                    </button>` : ''}
                                </div>
                            </div>
                            ${hasCallTrace ? `
                            <div id="${traceId}" class="call-trace-dropdown" style="position: relative; margin-top: 0.75rem;">
                                <div class="call-trace-content" style="background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; padding: 1rem;">
                                    <h4 style="color: #60a5fa; margin: 0 0 0.5rem 0; font-size: 0.9rem; font-weight: 600;">Execution Call Trace</h4>
                                    <div style="background: rgba(15, 23, 42, 0.8); border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 6px; padding: 0.75rem; max-height: 200px; overflow-y: auto;">
                                        ${message.call_trace.map((step, stepIndex) => `
                                            <div style="margin-bottom: 0.25rem; font-size: 0.85rem; color: #e2e8f0; padding: 0.25rem; background: rgba(59, 130, 246, 0.1); border-radius: 3px; border-left: 3px solid #3b82f6;">
                                                <strong style="color: #93c5fd;">${stepIndex + 1}.</strong> ${escapeHtml(step)}
                                            </div>
                                        `).join('')}
                                    </div>
                                    <button class="btn-small" onclick="toggleChatCallTrace('${traceId}')" style="margin-top: 0.5rem; background: #6b7280; color: white; padding: 0.25rem 0.5rem; border: none; border-radius: 4px; cursor: pointer; font-size: 0.75rem;">Close</button>
                                </div>
                            </div>` : ''}
                            ${hasDebugMessages ? `
                            <div id="${debugId}" class="debug-messages-dropdown" style="position: relative; margin-top: 0.75rem;">
                                <div class="debug-messages-content" style="background: rgba(220, 38, 38, 0.1); border: 1px solid rgba(220, 38, 38, 0.2); border-radius: 6px; padding: 1rem;">
                                    <h4 style="color: #dc2626; margin: 0 0 0.5rem 0; font-size: 0.9rem; font-weight: 600;">Debug & Logging Messages</h4>
                                    <div style="background: rgba(15, 23, 42, 0.8); border: 1px solid rgba(220, 38, 38, 0.2); border-radius: 6px; padding: 0.75rem; max-height: 300px; overflow-y: auto;">
                                        ${message.debug_messages.map((debugMsg, debugIndex) => {
                                            // Determine if this is a debug or logging message
                                            const isDebug = debugMsg.role === 'debug' || debugMsg.role === 'system' || 
                                                           debugMsg.message_type === 'debug' || debugMsg.content?.startsWith('DEBUG:');
                                            const isLogging = debugMsg.role === 'logging' || debugMsg.message_type === 'logging' || debugMsg.content?.startsWith('LOGGING:');
                                            
                                            // Set colors and styling based on message type
                                            const colors = isLogging ? {
                                                background: 'rgba(59, 130, 246, 0.1)',
                                                border: '#3b82f6',
                                                textColor: '#93c5fd',
                                                icon: '📋',
                                                label: 'Logging'
                                            } : {
                                                background: 'rgba(220, 38, 38, 0.1)',
                                                border: '#dc2626',
                                                textColor: '#fca5a5', 
                                                icon: '🐛',
                                                label: 'Debug'
                                            };
                                            
                                            return `
                                            <div style="margin-bottom: 0.75rem; padding: 0.5rem; background: ${colors.background}; border-radius: 6px; border-left: 4px solid ${colors.border};">
                                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.25rem;">
                                                    <span style="font-size: 0.9rem;">${colors.icon}</span>
                                                    <strong style="color: ${colors.textColor}; font-size: 0.85rem;">${colors.label} #${debugIndex + 1}</strong>
                                                    <span style="color: #6b7280; font-size: 0.75rem;">${debugMsg.timestamp ? new Date(debugMsg.timestamp).toLocaleString() : ''}</span>
                                                </div>
                                                <div style="font-family: monospace; font-size: 0.8rem; color: #e2e8f0; white-space: pre-wrap;">${escapeHtml(debugMsg.content || debugMsg)}</div>
                                            </div>`;
                                        }).join('')}
                                    </div>
                                    <button class="btn-small" onclick="toggleDebugMessage('${debugId}')" style="margin-top: 0.5rem; background: #6b7280; color: white; padding: 0.25rem 0.5rem; border: none; border-radius: 4px; cursor: pointer; font-size: 0.75rem;">Close</button>
                                </div>
                            </div>` : ''}
                        </div>
                    </div>`;
                });
                
                // Close session content containers (only if not within a session container)
                if (!isSessionContainer) {
                    html += `
                            </div>
                        </div>`;  // Close session-content
                }
            });
            
            container.innerHTML = html;
        }
        
        function displayChatHistoryWithSessions(data, containerId = 'chatContainer') {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const sessions_list = data.sessions_list || [];
            const chat_sessions = data.chat_sessions || {};
            const sessions_metadata = data.sessions_metadata || {};
            
            if (sessions_list.length === 0) {
                container.innerHTML = '<div class="loading">No chat sessions found</div>';
                return;
            }
            
            // Determine which switch function to use based on containerId
            const switchFunction = containerId === 'chatPageContainer' ? 'switchChatPageSession' : 'switchChatSession';
            
            let html = `
                <div style="margin-bottom: 2rem;">
                    <h3 style="color: #93c5fd; margin-bottom: 0.5rem; text-transform: uppercase; letter-spacing: 0.05em; font-weight: 600;">Chat Sessions</h3>
                    <p style="color: #94a3b8;">Total sessions: ${sessions_list.length}</p>
                </div>`;
            
            // Create session tabs for all sessions (including empty ones)
            html += `
                <div class="session-tabs-container" style="margin-bottom: 1.5rem;">
                    <div class="session-tabs" style="display: flex; gap: 0.5rem; border-bottom: 2px solid rgba(59, 130, 246, 0.2); padding-bottom: 0; margin-bottom: 1rem; overflow-x: auto; overflow-y: hidden; max-width: 100%;">`;
            
            sessions_list.forEach((sessionId, index) => {
                const sessionIdStr = String(sessionId || '');
                const metadata = sessions_metadata[sessionId] || {};
                const sessionTitle = metadata.title || `Session ${sessionIdStr.substring(0, 8)}`;
                
                // Calculate actual message count from chat_sessions data instead of relying on metadata
                const sessionMessages = chat_sessions[sessionId] || [];
                const messageCount = sessionMessages.length;
                
                const lastActivity = metadata.last_activity ? 
                    new Date(metadata.last_activity).toLocaleDateString() : 'No activity';
                
                const isActive = index === 0 ? 'active' : '';
                
                html += `
                    <button class="session-tab ${isActive}" 
                            onclick="${switchFunction}('${sessionIdStr}')" 
                            data-session-id="${sessionIdStr}"
                            style="padding: 0.75rem 1.25rem; border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 8px 8px 0 0; cursor: pointer; transition: all 0.2s ease; background: ${isActive ? 'linear-gradient(135deg, #3b82f6, #6366f1)' : 'rgba(30, 41, 59, 0.5)'}; color: ${isActive ? 'white' : '#94a3b8'}; border-bottom: 3px solid ${isActive ? '#3b82f6' : 'transparent'}; backdrop-filter: blur(5px); min-width: 140px; white-space: nowrap; font-size: 0.85rem;">
                        <div style="text-align: left;">
                            <div style="font-weight: 600; font-size: 0.9rem;">${escapeHtml(sessionTitle)}</div>
                            <div style="font-size: 0.75rem; opacity: 0.8; margin-top: 0.25rem;">${messageCount} msg • ${lastActivity}</div>
                        </div>
                    </button>`;
            });
            
            html += `
                    </div>
                </div>`;
            
            // Create session content containers for all sessions (including empty ones)
            sessions_list.forEach((sessionId, index) => {
                const sessionIdStr = String(sessionId || '');
                const messages = chat_sessions[sessionId] || [];
                const metadata = sessions_metadata[sessionId] || {};
                const sessionTitle = metadata.title || `Session ${sessionIdStr.substring(0, 8)}`;
                const isActive = index === 0 ? 'active' : '';
                
                html += `
                    <div class="session-content ${isActive}" data-session-id="${sessionIdStr}" style="display: ${index === 0 ? 'block' : 'none'};">`;
                
                if (messages.length === 0) {
                    // Display empty session placeholder
                    html += `
                        <div class="session-messages">
                            <div style="background: rgba(30, 41, 59, 0.8); border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 8px; padding: 2rem; text-align: center;">
                                <h4 style="color: #93c5fd; margin-bottom: 1rem;">${escapeHtml(sessionTitle)}</h4>
                                <p style="color: #94a3b8; margin-bottom: 0.5rem;">This session is empty</p>
                                <p style="color: #6b7280; font-size: 0.9rem;">Session ID: ${sessionIdStr}</p>
                                <p style="color: #6b7280; font-size: 0.9rem;">Start a conversation to see messages here</p>
                            </div>
                        </div>`;
                } else {
                    // CRITICAL FIX: Use the working displayChatHistory logic for messages with debug attachments
                    // Create unique container for each session to avoid ID conflicts
                    const uniqueContainerId = `session-messages-${sessionIdStr}`;
                    html += `<div id="${uniqueContainerId}" class="session-messages"></div>`;
                }
                
                html += `</div>`;  // Close session-content
            });
            
            container.innerHTML = html;
            
            // CRITICAL FIX: For sessions with messages, call the working displayChatHistory function
            // Use unique container IDs to avoid conflicts between sessions
            sessions_list.forEach((sessionId, index) => {
                const messages = chat_sessions[sessionId] || [];
                if (messages.length > 0) {
                    const sessionIdStr = String(sessionId || '');
                    const uniqueContainerId = `session-messages-${sessionIdStr}`;
                    // Use setTimeout to ensure DOM is ready after innerHTML assignment
                    setTimeout(() => {
                        displayChatHistory(messages, uniqueContainerId);
                    }, 0);
                }
            });
        }
        
        function switchChatSession(sessionId) {
            // console.log('=== CHAT SESSION SWITCH DEBUG ===');
            // console.log('Switching to session:', sessionId, '(type:', typeof sessionId, ')');
            
            // Hide all session contents
            const allSessions = document.querySelectorAll('.session-content');
            // console.log('Found session contents:', allSessions.length);
            allSessions.forEach((session, index) => {
                // console.log(`  Session ${index}: data-session-id="${session.getAttribute('data-session-id')}" (type: ${typeof session.getAttribute('data-session-id')})`);
                session.style.display = 'none';
                session.classList.remove('active');
            });
            
            // Remove active class from all tabs
            const allTabs = document.querySelectorAll('.session-tab');
            // console.log('Found session tabs:', allTabs.length);
            allTabs.forEach((tab, index) => {
                // console.log(`  Tab ${index}: data-session-id="${tab.getAttribute('data-session-id')}" (type: ${typeof tab.getAttribute('data-session-id')})`);
                tab.classList.remove('active');
                tab.style.background = 'rgba(30, 41, 59, 0.5)';
                tab.style.color = '#94a3b8';
                tab.style.borderBottom = '3px solid transparent';
            });
            
            // Find and activate the target session content
            const targetSession = document.querySelector(`.session-content[data-session-id="${sessionId}"]`);
            // console.log('Target session found:', !!targetSession);
            
            if (targetSession) {
                targetSession.style.display = 'block';
                targetSession.classList.add('active');
                // console.log('✓ Activated session content for:', sessionId);
            } else {
                // console.log('✗ Could not find session content for:', sessionId);
                // Try string comparison fallback
                const fallbackSession = Array.from(allSessions).find(el => 
                    String(el.getAttribute('data-session-id')) === String(sessionId)
                );
                if (fallbackSession) {
                    fallbackSession.style.display = 'block';
                    fallbackSession.classList.add('active');
                    // console.log('✓ Found session via fallback for:', sessionId);
                } else {
                    // console.log('✗ No fallback session found');
                }
            }
            
            // Find and activate the target tab
            const targetTab = document.querySelector(`.session-tab[data-session-id="${sessionId}"]`);
            // console.log('Target tab found:', !!targetTab);
            
            if (targetTab) {
                targetTab.classList.add('active');
                targetTab.style.background = 'linear-gradient(135deg, #3b82f6, #6366f1)';
                targetTab.style.color = 'white';
                targetTab.style.borderBottom = '3px solid #3b82f6';
                // console.log('✓ Activated session tab for:', sessionId);
            } else {
                // console.log('✗ Could not find session tab for:', sessionId);
                // Try string comparison fallback  
                const fallbackTab = Array.from(allTabs).find(el => 
                    String(el.getAttribute('data-session-id')) === String(sessionId)
                );
                if (fallbackTab) {
                    fallbackTab.classList.add('active');
                    fallbackTab.style.background = 'linear-gradient(135deg, #3b82f6, #6366f1)';
                    fallbackTab.style.color = 'white';
                    fallbackTab.style.borderBottom = '3px solid #3b82f6';
                    // console.log('✓ Found tab via fallback for:', sessionId);
                } else {
                    // console.log('✗ No fallback tab found');
                }
            }
            
            // console.log('=== END CHAT SESSION SWITCH DEBUG ===');
            
            // Save state after session switch
            setTimeout(saveApplicationState, 100);
        }
        
        
        
        // HTML escape function for safe display
        function escapeHtml(text) {
            if (typeof text !== 'string') return '';
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
        
        // Helper functions for tab switching from user actions
        function switchToUserRequests(userGuid) {
            // Switch to requests tab
            showTab('user-requests');
            
            // Fill in the GUID and load requests
            const input = document.getElementById('userGuidInput');
            if (input) {
                input.value = userGuid;
                loadUserRequests();
            }
        }
        
        function switchToUserChat(userGuid) {
            // Switch to chat tab
            showTab('chat-history');
            
            // Fill in the GUID and load chat history
            const input = document.getElementById('chatUserGuidInput');
            if (input) {
                input.value = userGuid;
                loadChatHistory();
            }
        }
        
        // Cancel scheduled request
        async function cancelRequest(requestGuid) {
            if (!confirm('Are you sure you want to cancel this scheduled request?')) {
                return;
            }
            
            // Debug log sent to backend
            try {
                await fetch(`${API_BASE_URL}/dashboard/api/debug-trace`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: `Cancelling request: ${requestGuid}` })
                });
            } catch (e) { /* Ignore debug trace failures */ }
            
            try {
                const response = await fetch(`${API_BASE_URL}/dashboard/api/cancel-scheduled-request`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ scheduled_guid: requestGuid })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Request cancelled successfully');
                    loadUserRequests(); // Reload the requests
                } else {
                    alert(`Failed to cancel request: ${data.error}`);
                }
                
            } catch (error) {
                alert(`Error cancelling request: ${error.message}`);
            }
        }
        
        async function loadSystemOverview() {
            const container = document.getElementById('requestsContainer');
            if (!container) return;
            
            container.innerHTML = '<div class="loading">Loading system overview...</div>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/dashboard/api/dashboard-overview`);
                const data = await response.json();
                
                const html = `
                    <div class="system-overview" style="background: rgba(30, 41, 59, 0.5); border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 8px; padding: 1.5rem;">
                        <h3 style="color: #93c5fd; margin-bottom: 1rem;">System Overview</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            <div>
                                <strong style="color: #f8fafc;">Active Managers:</strong>
                                <div style="color: #60a5fa; font-size: 1.5rem; font-weight: 700;">${data.factory_metrics?.active_managers || 0}</div>
                            </div>
                            <div>
                                <strong style="color: #f8fafc;">Total Requests:</strong>
                                <div style="color: #60a5fa; font-size: 1.5rem; font-weight: 700;">${data.factory_metrics?.total_requests_all_users || 0}</div>
                            </div>
                            <div>
                                <strong style="color: #f8fafc;">Active Tasks:</strong>
                                <div style="color: #60a5fa; font-size: 1.5rem; font-weight: 700;">${data.factory_metrics?.total_active_tasks || 0}</div>
                            </div>
                            <div>
                                <strong style="color: #f8fafc;">System Health:</strong>
                                <div style="color: #10b981; font-size: 1.2rem; font-weight: 700;">${(data.system_health?.overall_status || 'unknown').toUpperCase()}</div>
                            </div>
                        </div>
                    </div>
                `;
                
                container.innerHTML = html;
                
            } catch (error) {
                // console.error('Failed to load system overview:', error);
                container.innerHTML = `<div class="error">Failed to load system overview: ${error.message}</div>`;
            }
        }
        
        // Load system overview for scheduled requests
        async function loadSystemOverview() {
            const container = document.getElementById('requestsContainer');
            if (!container) return;
            
            container.innerHTML = '<div class="loading">Loading system overview...</div>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/dashboard/api/dashboard-overview`);
                const data = await response.json();
                
                if (data.error) {
                    container.innerHTML = `<div class="error">${escapeHtml(data.error)}</div>`;
                    return;
                }
                
                let html = '<h3 style="color: #93c5fd; margin-bottom: 1.5rem; text-transform: uppercase; letter-spacing: 0.05em; font-weight: 600;">System Overview</h3>';
                html += '<div class="metrics-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">';
                
                // Factory metrics
                if (data.factory_metrics) {
                    html += `<div class="metric-card" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 12px; padding: 1.5rem; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <div class="metric-title" style="font-size: 0.9rem; color: #94a3b8; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">Active Managers</div>
                        <div class="metric-value" style="font-size: 2.5rem; font-weight: 700; color: #f8fafc; margin-bottom: 0.5rem;">${data.factory_metrics.active_managers || 0}</div>
                        <div class="metric-subtitle" style="font-size: 0.85rem; color: #64748b;">User-specific managers</div>
                    </div>`;
                    
                    html += `<div class="metric-card" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 12px; padding: 1.5rem; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <div class="metric-title" style="font-size: 0.9rem; color: #94a3b8; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">Total Active Tasks</div>
                        <div class="metric-value" style="font-size: 2.5rem; font-weight: 700; color: #f8fafc; margin-bottom: 0.5rem;">${data.factory_metrics.total_active_tasks || 0}</div>
                        <div class="metric-subtitle" style="font-size: 0.85rem; color: #64748b;">Across all users</div>
                    </div>`;
                    
                    html += `<div class="metric-card" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 12px; padding: 1.5rem; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <div class="metric-title" style="font-size: 0.9rem; color: #94a3b8; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">Total Requests</div>
                        <div class="metric-value" style="font-size: 2.5rem; font-weight: 700; color: #f8fafc; margin-bottom: 0.5rem;">${data.factory_metrics.total_requests_all_users || 0}</div>
                        <div class="metric-subtitle" style="font-size: 0.85rem; color: #64748b;">Lifetime requests</div>
                    </div>`;
                }
                
                // Scheduled requests specific metrics
                if (data.scheduled_requests) {
                    html += `<div class="metric-card" style="background: rgba(16, 185, 129, 0.1); backdrop-filter: blur(10px); border-radius: 12px; padding: 1.5rem; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5); border: 1px solid rgba(16, 185, 129, 0.2);">
                        <div class="metric-title" style="font-size: 0.9rem; color: #86efac; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">Active Scheduled</div>
                        <div class="metric-value" style="font-size: 2.5rem; font-weight: 700; color: #86efac; margin-bottom: 0.5rem;">${data.scheduled_requests.active || 0}</div>
                        <div class="metric-subtitle" style="font-size: 0.85rem; color: #64748b;">Currently scheduled</div>
                    </div>`;
                }
                
                html += '</div>';
                
                // Quota overview
                if (data.quota_overview) {
                    html += `
                    <div class="quota-section" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 12px; padding: 1.5rem; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5); border: 1px solid rgba(59, 130, 246, 0.2); margin-bottom: 2rem;">
                        <h4 style="color: #93c5fd; margin-bottom: 1rem; text-transform: uppercase; letter-spacing: 0.05em; font-size: 1rem;">Quota Status</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            ${Object.entries(data.quota_overview).map(([key, value]) => `
                                <div style="background: rgba(15, 23, 42, 0.8); padding: 1rem; border-radius: 8px; border: 1px solid rgba(59, 130, 246, 0.2);">
                                    <div style="color: #94a3b8; font-size: 0.85rem; margin-bottom: 0.3rem;">${key.replace(/_/g, ' ').toUpperCase()}</div>
                                    <div style="color: #f8fafc; font-size: 1.5rem; font-weight: 600;">${value}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>`;
                }
                
                // System health
                if (data.system_health) {
                    const healthColor = {
                        'healthy': '#10b981',
                        'moderate': '#f59e0b',
                        'degraded': '#ef4444',
                        'critical': '#dc2626',
                        'unknown': '#6b7280'
                    }[data.system_health.overall_status || 'unknown'] || '#6b7280';
                    
                    html += `
                    <div class="health-section" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); border-radius: 12px; padding: 1.5rem; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5); border: 1px solid rgba(59, 130, 246, 0.2);">
                        <h4 style="color: #93c5fd; margin-bottom: 1rem; text-transform: uppercase; letter-spacing: 0.05em; font-size: 1rem;">System Health</h4>
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <div style="width: 12px; height: 12px; background: ${healthColor}; border-radius: 50%; box-shadow: 0 0 10px ${healthColor};"></div>
                            <span style="color: ${healthColor}; font-size: 1.1rem; font-weight: 600; text-transform: uppercase;">${data.system_health.overall_status || 'Unknown'}</span>
                        </div>
                        ${data.performance_metrics ? `
                        <div style="margin-top: 1rem; display: grid; grid-template-columns: repeat(2, 1fr); gap: 0.5rem; font-size: 0.85rem; color: #94a3b8;">
                            <div>CPU Usage: ${data.performance_metrics.cpu_percent || 0}%</div>
                            <div>Memory Usage: ${data.performance_metrics.memory_percent || 0}%</div>
                            <div>Disk Usage: ${data.performance_metrics.disk_usage_percent || 0}%</div>
                            <div>Network I/O: ${data.performance_metrics.network_io_mb || 0} MB</div>
                        </div>
                        ` : ''}
                    </div>`;
                }
                
                html += '<p style="color: #64748b; margin-top: 2rem; font-size: 0.9rem; text-align: center;">Enter a specific user GUID above to view individual scheduled requests.</p>';
                
                container.innerHTML = html;
            } catch (error) {
                // console.error('Failed to load system overview:', error);
                container.innerHTML = `<div class="error">Failed to load system overview: ${error.message}</div>`;
            }
        }
        
        // Normalize indentation to 5 spaces per level
        function normalizeIndentation(text) {
            if (!text || typeof text !== 'string') return text;
            
            const lines = text.split('\n');
            const processedLines = lines.map(line => {
                // Keep empty lines empty
                if (line.trim() === '') return '';
                
                // Count leading whitespace (tabs count as 4 spaces)
                const leadingWhitespace = line.match(/^\s*/)[0];
                const spaceCount = leadingWhitespace.replace(/\t/g, '    ').length;
                
                // Calculate indentation level (every 4 spaces = 1 level)
                const indentLevel = Math.floor(spaceCount / 4);
                
                // Apply 5 spaces per level
                const newIndent = '     '.repeat(indentLevel); // 5 spaces
                
                return newIndent + line.trimStart();
            });
            
            return processedLines.join('\n');
        }
        
        // Toggle request expand/collapse with animation
        function toggleRequestExpand(expandId) {
            const expandElement = document.getElementById(expandId);
            
            if (!expandElement) {
                return;
            }
            
            // Close all other expanded requests
            document.querySelectorAll('.request-expanded.show').forEach(element => {
                if (element.id !== expandId) {
                    element.classList.remove('show');
                    // Update buttons for closed elements
                    const otherId = element.id;
                    document.querySelectorAll(`button[onclick*="${otherId}"]`).forEach(btn => {
                        if (btn.textContent.trim() === 'Collapse') {
                            btn.textContent = 'Expand';
                            btn.style.background = '#3b82f6';
                        }
                    });
                }
            });
            
            // Find all buttons that control this expand element
            const expandButtons = document.querySelectorAll(`button[onclick*="${expandId}"]`);
            
            if (expandElement.classList.contains('show')) {
                // Hide expanded content
                expandElement.classList.remove('show');
                expandButtons.forEach(button => {
                    if (button.textContent.trim() === 'Collapse') {
                        button.textContent = 'Expand';
                        button.style.background = '#3b82f6';
                    }
                });
                // Also close any open trace/debug sections
                expandElement.querySelectorAll('.trace-dropdown.show, .debug-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            } else {
                // Show expanded content
                expandElement.classList.add('show');
                expandButtons.forEach(button => {
                    if (button.textContent.trim() === 'Expand') {
                        button.textContent = 'Collapse';
                        button.style.background = '#6b7280';
                    }
                });
            }
            
            // Save state after expand/collapse
            setTimeout(saveApplicationState, 100);
        }
        
        // Toggle request trace visibility
        function toggleRequestTrace(traceId) {
            const traceElement = document.getElementById(traceId);
            if (!traceElement) return;
            
            // Close all other dropdowns in the same request
            const requestCard = traceElement.closest('.request-expanded');
            if (requestCard) {
                requestCard.querySelectorAll('.debug-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
            
            if (traceElement.classList.contains('show')) {
                traceElement.classList.remove('show');
            } else {
                traceElement.classList.add('show');
            }
            
            // Save state after trace toggle
            setTimeout(saveApplicationState, 100);
        }
        
        // Toggle request debug visibility
        function toggleRequestDebug(debugId) {
            const debugElement = document.getElementById(debugId);
            if (!debugElement) return;
            
            // Close all other dropdowns in the same request
            const requestCard = debugElement.closest('.request-expanded');
            if (requestCard) {
                requestCard.querySelectorAll('.trace-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
            
            if (debugElement.classList.contains('show')) {
                debugElement.classList.remove('show');
            } else {
                debugElement.classList.add('show');
            }
            
            // Save state after debug toggle
            setTimeout(saveApplicationState, 100);
        }
        
        // Toggle chat call trace visibility
        function toggleChatCallTrace(traceId) {
            const traceElement = document.getElementById(traceId);
            if (!traceElement) return;
            
            // Close all other dropdowns in the same message
            const messageContainer = traceElement.closest('.chat-message');
            if (messageContainer) {
                messageContainer.querySelectorAll('.debug-messages-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
            
            if (traceElement.classList.contains('show')) {
                traceElement.classList.remove('show');
            } else {
                traceElement.classList.add('show');
            }
            
            // Save state after chat trace toggle
            setTimeout(saveApplicationState, 100);
        }
        
        // Toggle chat debug message visibility
        function toggleDebugMessage(debugId) {
            const debugElement = document.getElementById(debugId);
            if (!debugElement) return;
            
            // Close all other dropdowns in the same message
            const messageContainer = debugElement.closest('.chat-message');
            if (messageContainer) {
                messageContainer.querySelectorAll('.call-trace-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
            
            if (debugElement.classList.contains('show')) {
                debugElement.classList.remove('show');
            } else {
                debugElement.classList.add('show');
            }
            
            // Save state after chat debug toggle
            setTimeout(saveApplicationState, 100);
        }
        
        // State preservation functions for auto-refresh
        function saveRequestsExpandedState() {
            const state = {
                expanded: [],
                traceVisible: [],
                debugVisible: []
            };
            
            // Save expanded requests
            document.querySelectorAll('.request-expanded.show').forEach(element => {
                state.expanded.push(element.id);
            });
            
            // Save visible trace dropdowns
            document.querySelectorAll('.trace-dropdown.show').forEach(element => {
                state.traceVisible.push(element.id);
            });
            
            // Save visible debug dropdowns
            document.querySelectorAll('.debug-dropdown.show').forEach(element => {
                state.debugVisible.push(element.id);
            });
            
            return state;
        }
        
        function restoreRequestsExpandedState(state) {
            if (!state) return;
            
            // Restore expanded requests
            state.expanded.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.classList.add('show');
                    
                    // Update button text and styling for restored expanded requests
                    const expandButtons = document.querySelectorAll(`button[onclick*="${elementId}"]`);
                    expandButtons.forEach(button => {
                        if (button.textContent.trim() === 'Expand') {
                            button.textContent = 'Collapse';
                            button.style.background = '#6b7280';
                        }
                    });
                }
            });
            
            // Restore visible trace dropdowns
            state.traceVisible.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.classList.add('show');
                }
            });
            
            // Restore visible debug dropdowns
            state.debugVisible.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.classList.add('show');
                }
            });
        }
        
        function saveChatExpandedState() {
            const state = {
                traceVisible: [],
                debugVisible: []
            };
            
            // Save visible chat trace dropdowns (using correct selector)
            document.querySelectorAll('#chatContainer .call-trace-dropdown.show').forEach(element => {
                state.traceVisible.push(element.id);
            });
            
            // Save visible chat debug dropdowns (using correct selector)
            document.querySelectorAll('#chatContainer .debug-messages-dropdown.show').forEach(element => {
                state.debugVisible.push(element.id);
            });
            
            return state;
        }
        
        function restoreChatExpandedState(state) {
            if (!state) return;
            
            // Restore visible chat trace dropdowns
            state.traceVisible.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.classList.add('show');
                }
            });
            
            // Restore visible chat debug dropdowns
            state.debugVisible.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.classList.add('show');
                }
            });
        }
        
        // Complete application state persistence for F5 refresh
        function saveApplicationState() {
            const state = {
                currentPage: currentPage,
                activeTab: null,
                formData: {},
                expandedStates: {},
                autoRefresh: autoRefreshEnabled,
                timestamp: Date.now()
            };
            
            // Save active tab in Zaira page
            if (currentPage === 'zaira') {
                const activeTab = document.querySelector('.tab-button.active');
                if (activeTab) {
                    state.activeTab = activeTab.textContent.trim().toLowerCase();
                }
            }
            
            // Save form data
            const userGuidInput = document.getElementById('userGuidInput');
            if (userGuidInput) {
                state.formData.userGuid = userGuidInput.value;
            }
            
            const chatUserGuidInput = document.getElementById('chatUserGuidInput');
            if (chatUserGuidInput) {
                state.formData.chatUserGuid = chatUserGuidInput.value;
            }
            
            // Save active session tab (Zaira page)
            const activeSessionTab = document.querySelector('.session-tab.active');
            if (activeSessionTab) {
                state.formData.activeSessionId = activeSessionTab.dataset.sessionId;
            }
            
            // Save Chat page form data
            const chatPageUserGuidInput = document.getElementById('chatPageUserGuidInput');
            if (chatPageUserGuidInput) {
                state.formData.chatPageUserGuid = chatPageUserGuidInput.value;
            }
            
            // Save active Chat page session tab
            const activeChatPageSessionTab = document.querySelector('#chatPageContainer .session-tab.active');
            if (activeChatPageSessionTab) {
                state.formData.activeChatPageSessionId = activeChatPageSessionTab.dataset.sessionId;
            }
            
            // Save expanded states
            if (currentPage === 'zaira') {
                state.expandedStates.requests = saveRequestsExpandedState();
                state.expandedStates.chat = saveChatExpandedState();
            }
            
            // Save to localStorage
            localStorage.setItem(STATE_STORAGE_KEY, JSON.stringify(state));
        }
        
        function restoreApplicationState() {
            try {
                const savedState = localStorage.getItem(STATE_STORAGE_KEY);
                if (!savedState) return false;
                
                const state = JSON.parse(savedState);
                
                // Don't restore if state is too old (older than 1 hour)
                if (Date.now() - state.timestamp > 3600000) {
                    localStorage.removeItem(STATE_STORAGE_KEY);
                    return false;
                }
                
                // Restore current page (without URL changes)
                if (state.currentPage && state.currentPage !== 'overview') {
                    currentPage = state.currentPage;
                    // URL stays at /dashboard - no URL manipulation
                    
                    // Set active menu item
                    document.querySelectorAll('.menu-item').forEach(item => item.classList.remove('active'));
                    const activeItem = document.querySelector(`[data-page="${state.currentPage}"]`);
                    if (activeItem) {
                        activeItem.classList.add('active');
                    }
                    
                    // Load the page content
                    loadPageContent(state.currentPage).then(() => {
                        // Restore page-specific state after content loads
                        setTimeout(() => restorePageSpecificState(state), 200);
                    });
                } else {
                    // For overview page, just restore basic state
                    restoreBasicState(state);
                }
                
                return true;
            } catch (error) {
                // console.error('Failed to restore application state:', error);
                localStorage.removeItem(STATE_STORAGE_KEY);
                return false;
            }
        }
        
        function restorePageSpecificState(state) {
            if (state.currentPage === 'zaira') {
                // Restore active tab
                if (state.activeTab) {
                    if (state.activeTab.includes('user list')) {
                        showTab('user-list');
                    } else if (state.activeTab.includes('scheduled requests')) {
                        showTab('user-requests');
                        // Restore form data and load requests
                        const userGuidInput = document.getElementById('userGuidInput');
                        if (userGuidInput && state.formData.userGuid) {
                            userGuidInput.value = state.formData.userGuid;
                            loadUserRequests().then(() => {
                                // Restore expanded states after data loads
                                if (state.expandedStates.requests) {
                                    setTimeout(() => restoreRequestsExpandedState(state.expandedStates.requests), 100);
                                }
                            });
                        }
                    } else if (state.activeTab.includes('chat history')) {
                        showTab('chat-history');
                        // Restore form data and load chat history
                        const chatUserGuidInput = document.getElementById('chatUserGuidInput');
                        if (chatUserGuidInput && state.formData.chatUserGuid) {
                            chatUserGuidInput.value = state.formData.chatUserGuid;
                            loadChatHistory().then(() => {
                                // Restore active session tab after data loads
                                if (state.formData.activeSessionId) {
                                    setTimeout(() => switchChatSession(state.formData.activeSessionId), 150);
                                }
                                // Restore expanded states after data loads
                                if (state.expandedStates.chat) {
                                    setTimeout(() => restoreChatExpandedState(state.expandedStates.chat), 200);
                                }
                            });
                        }
                    }
                }
            } else if (state.currentPage === 'chat') {
                // Restore chat page state
                const chatPageUserGuidInput = document.getElementById('chatPageUserGuidInput');
                if (chatPageUserGuidInput && state.formData.chatPageUserGuid) {
                    chatPageUserGuidInput.value = state.formData.chatPageUserGuid;
                    loadChatPageHistory().then(() => {
                        // Restore active session tab after data loads
                        if (state.formData.activeChatPageSessionId) {
                            setTimeout(() => {
                                const activeTab = document.querySelector(`#chatPageContainer .session-tab[data-session-id="${state.formData.activeChatPageSessionId}"]`);
                                if (activeTab) {
                                    switchChatPageSession(state.formData.activeChatPageSessionId);
                                }
                            }, 150);
                        }
                    });
                }
            }
            
            // Restore auto-refresh setting
            if (state.autoRefresh !== undefined) {
                autoRefreshEnabled = state.autoRefresh;
                const toggle = document.getElementById('autoRefreshToggle');
                if (toggle) {
                    if (autoRefreshEnabled) {
                        toggle.style.background = 'var(--brand-primary)';
                        toggle.querySelector('.refresh-toggle-slider').style.transform = 'translateX(26px)';
                        document.getElementById('refreshTimer').style.opacity = '1';
                        startAutoRefresh();
                    } else {
                        toggle.style.background = 'rgba(100, 116, 139, 0.3)';
                        toggle.querySelector('.refresh-toggle-slider').style.transform = 'translateX(0)';
                        document.getElementById('refreshTimer').style.opacity = '0.5';
                    }
                }
            }
        }
        
        function restoreBasicState(state) {
            // Restore auto-refresh setting for overview page
            if (state.autoRefresh !== undefined) {
                autoRefreshEnabled = state.autoRefresh;
                localStorage.setItem('autoRefresh', autoRefreshEnabled);
            }
        }
        
        function setupStatePersistence() {
            // Save state periodically
            setInterval(saveApplicationState, 5000); // Every 5 seconds
            
            // Save state on page unload (F5, close, navigate)
            window.addEventListener('beforeunload', saveApplicationState);
            
            // Save state on visibility change (alt-tab, minimize)
            document.addEventListener('visibilitychange', saveApplicationState);
            
            // Save state when forms change
            document.addEventListener('input', function(e) {
                if (e.target.id === 'userGuidInput' || e.target.id === 'chatUserGuidInput') {
                    // Debounce form changes
                    clearTimeout(window.stateDebounceTimer);
                    window.stateDebounceTimer = setTimeout(saveApplicationState, 1000);
                }
            });
            
            // Save state when tabs change
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('tab-button') || e.target.classList.contains('menu-item')) {
                    setTimeout(saveApplicationState, 100); // Small delay to capture state after change
                }
            });
        }
        
        // Generalized Tab Management System for Reusability
        class TabManager {
            constructor(containerId, options = {}) {
                this.containerId = containerId;
                this.container = document.getElementById(containerId);
                this.options = {
                    tabClass: 'generic-tab',
                    contentClass: 'generic-content',
                    activeClass: 'active',
                    onTabSwitch: null,
                    persistState: true,
                    ...options
                };
                this.tabs = new Map();
                this.activeTabId = null;
            }
            
            // Create tab system with header and content area
            initialize(title, subtitle = '') {
                if (!this.container) return;
                
                const html = `
                    <div class="tab-system-header" style="margin-bottom: 2rem;">
                        <h3 style="color: #93c5fd; margin-bottom: 0.5rem; text-transform: uppercase; letter-spacing: 0.05em; font-weight: 600;">${title}</h3>
                        ${subtitle ? `<p style="color: #94a3b8;">${subtitle}</p>` : ''}
                    </div>
                    <div class="tab-navigation" style="display: flex; gap: 0.5rem; border-bottom: 2px solid rgba(59, 130, 246, 0.2); padding-bottom: 0; margin-bottom: 1rem; overflow-x: auto; overflow-y: hidden; max-width: 100%;"></div>
                    <div class="tab-content-area"></div>
                `;
                
                this.container.innerHTML = html;
                return this;
            }
            
            // Add a tab with content
            addTab(id, label, content, metadata = {}) {
                const tabButton = this.createTabButton(id, label, metadata);
                const contentContainer = this.createContentContainer(id, content);
                
                this.tabs.set(id, {
                    button: tabButton,
                    content: contentContainer,
                    metadata: metadata
                });
                
                // Add to DOM
                const navigation = this.container.querySelector('.tab-navigation');
                const contentArea = this.container.querySelector('.tab-content-area');
                
                if (navigation && contentArea) {
                    navigation.appendChild(tabButton);
                    contentArea.appendChild(contentContainer);
                }
                
                // Set first tab as active
                if (this.tabs.size === 1) {
                    this.switchTab(id);
                }
                
                return this;
            }
            
            // Create tab button
            createTabButton(id, label, metadata) {
                const button = document.createElement('button');
                button.className = `${this.options.tabClass}`;
                button.dataset.tabId = id;
                button.style.cssText = `
                    padding: 0.75rem 1.25rem; 
                    border: 1px solid rgba(59, 130, 246, 0.2); 
                    border-radius: 8px 8px 0 0; 
                    cursor: pointer; 
                    transition: all 0.2s ease; 
                    background: rgba(30, 41, 59, 0.5); 
                    color: #94a3b8; 
                    border-bottom: 3px solid transparent; 
                    backdrop-filter: blur(5px); 
                    min-width: 140px; 
                    white-space: nowrap; 
                    font-size: 0.85rem;
                `;
                
                // Create tab content with metadata
                const tabContent = document.createElement('div');
                tabContent.style.textAlign = 'left';
                
                const mainLabel = document.createElement('div');
                mainLabel.style.cssText = 'font-weight: 600; font-size: 0.9rem;';
                mainLabel.textContent = label;
                
                tabContent.appendChild(mainLabel);
                
                if (metadata.subtitle) {
                    const subtitle = document.createElement('div');
                    subtitle.style.cssText = 'font-size: 0.75rem; opacity: 0.8; margin-top: 0.25rem;';
                    subtitle.textContent = metadata.subtitle;
                    tabContent.appendChild(subtitle);
                }
                
                button.appendChild(tabContent);
                
                // Add click handler
                button.addEventListener('click', () => this.switchTab(id));
                
                return button;
            }
            
            // Create content container
            createContentContainer(id, content) {
                const container = document.createElement('div');
                container.className = `${this.options.contentClass}`;
                container.dataset.tabId = id;
                container.style.display = 'none';
                
                if (typeof content === 'string') {
                    container.innerHTML = content;
                } else if (content instanceof HTMLElement) {
                    container.appendChild(content);
                }
                
                return container;
            }
            
            // Switch to a specific tab
            switchTab(id) {
                if (!this.tabs.has(id)) return;
                
                // Hide all content and deactivate all tabs
                this.tabs.forEach((tab, tabId) => {
                    tab.content.style.display = 'none';
                    tab.content.classList.remove(this.options.activeClass);
                    
                    tab.button.classList.remove(this.options.activeClass);
                    tab.button.style.background = 'rgba(30, 41, 59, 0.5)';
                    tab.button.style.color = '#94a3b8';
                    tab.button.style.borderBottom = '3px solid transparent';
                });
                
                // Activate selected tab
                const selectedTab = this.tabs.get(id);
                selectedTab.content.style.display = 'block';
                selectedTab.content.classList.add(this.options.activeClass);
                
                selectedTab.button.classList.add(this.options.activeClass);
                selectedTab.button.style.background = 'linear-gradient(135deg, #3b82f6, #6366f1)';
                selectedTab.button.style.color = 'white';
                selectedTab.button.style.borderBottom = '3px solid #3b82f6';
                
                this.activeTabId = id;
                
                // Call callback if provided
                if (this.options.onTabSwitch) {
                    this.options.onTabSwitch(id, selectedTab);
                }
                
                // Save state if enabled
                if (this.options.persistState) {
                    setTimeout(saveApplicationState, 100);
                }
            }
            
            // Remove a tab
            removeTab(id) {
                if (!this.tabs.has(id)) return;
                
                const tab = this.tabs.get(id);
                tab.button.remove();
                tab.content.remove();
                this.tabs.delete(id);
                
                // If this was the active tab, switch to another
                if (this.activeTabId === id && this.tabs.size > 0) {
                    const firstTabId = this.tabs.keys().next().value;
                    this.switchTab(firstTabId);
                }
                
                return this;
            }
            
            // Get active tab ID
            getActiveTab() {
                return this.activeTabId;
            }
            
            // Update tab content
            updateTabContent(id, content) {
                if (!this.tabs.has(id)) return;
                
                const tab = this.tabs.get(id);
                if (typeof content === 'string') {
                    tab.content.innerHTML = content;
                } else if (content instanceof HTMLElement) {
                    tab.content.innerHTML = '';
                    tab.content.appendChild(content);
                }
                
                return this;
            }
            
            // Get all tab IDs
            getTabIds() {
                return Array.from(this.tabs.keys());
            }
        }
        
        // Expose TabManager globally to prevent duplicate declarations in footer
        window.TabManager = TabManager;
        
        // Utility functions
        function showError(message) {
            const contentArea = document.getElementById('pageContent');
            contentArea.innerHTML = `<div class="error">${message}</div>`;
        }
        
        function showLoading() {
            const contentArea = document.getElementById('pageContent');
            contentArea.innerHTML = '<div class="loading">Loading...</div>';
        }
        
        // Navigate to Chat History tab for specific chat session
        function navigateToChatSession(chatSessionGuid, userGuid) {
            // Try to get user GUID from parameter, then from input field
            let effectiveUserGuid = userGuid;
            
            if (!effectiveUserGuid || effectiveUserGuid === 'Unknown') {
                const userGuidInput = document.getElementById('userGuidInput');
                if (userGuidInput && userGuidInput.value && userGuidInput.value.trim()) {
                    effectiveUserGuid = userGuidInput.value.trim();
                }
            }
            
            // If still no user GUID available, show helpful message but continue
            if (!effectiveUserGuid || effectiveUserGuid === 'Unknown') {
                // Don't block navigation, just inform user
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed; top: 20px; right: 20px; z-index: 10000;
                    background: #f59e0b; color: white; padding: 0.75rem 1rem;
                    border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    font-size: 0.9rem; max-width: 300px;
                `;
                notification.textContent = `Navigating to chat history. Please select a user first for best results.`;
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 4000);
                
                effectiveUserGuid = 'system-user'; // Fallback to system user
            }
            
            // Switch to chat-history tab if showTab function exists
            if (typeof showTab === 'function') {
                showTab('chat-history');
            }
            
            // Pre-fill the user GUID input if it exists
            const chatUserGuidInput = document.getElementById('chatUserGuidInput');
            if (chatUserGuidInput) {
                chatUserGuidInput.value = effectiveUserGuid;
            }
            
            // Load the chat history automatically if function exists
            if (typeof loadChatHistory === 'function') {
                // Use setTimeout to ensure the tab switching completes first
                setTimeout(async () => {
                    try {
                        await loadChatHistory();
                        
                        // If we have a specific chat session, navigate to it
                        if (chatSessionGuid && chatSessionGuid !== 'Unknown') {
                            // Wait for chat history to render, then switch to the specific session
                            setTimeout(() => {
                                if (typeof switchChatSession === 'function') {
                                    switchChatSession(chatSessionGuid);
                                } else {
                                    // Fallback: find and click the session tab manually
                                    findAndActivateSessionTab(chatSessionGuid);
                                }
                            }, 500);
                        }
                    } catch (error) {
                        console.error('Error loading chat history:', error);
                    }
                }, 100);
            }
            
            // Show success notification
            const successNotification = document.createElement('div');
            successNotification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                background: #10b981; color: white; padding: 0.75rem 1rem;
                border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-size: 0.9rem; max-width: 300px;
            `;
            
            // Show user information in notification
            const userInfo = effectiveUserGuid === userGuid ? 'user' : `user (${effectiveUserGuid.substring(0, 8)}...)`;
            
            if (chatSessionGuid && chatSessionGuid !== 'Unknown') {
                successNotification.textContent = `Navigated to chat history for ${userInfo}. Looking for session ${chatSessionGuid.substring(0, 8)}...`;
            } else {
                successNotification.textContent = `Navigated to chat history for ${userInfo}.`;
            }
            
            document.body.appendChild(successNotification);
            
            // Remove notification after 3 seconds
            setTimeout(() => {
                if (successNotification.parentNode) {
                    successNotification.parentNode.removeChild(successNotification);
                }
            }, 3000);
        }
        
        // Helper function to find and activate a specific chat session tab
        function findAndActivateSessionTab(sessionId) {
            try {
                // Look for session buttons/tabs that might contain the session ID
                const chatContainer = document.getElementById('chatContainer');
                if (!chatContainer) return false;
                
                // Search for clickable elements (buttons, tabs) with session ID
                const sessionIdShort = sessionId.substring(0, 8);
                const possibleSelectors = [
                    `[data-session-id="${sessionId}"]`,
                    `[data-session="${sessionId}"]`,
                    `#session-${sessionId}`,
                    `#${sessionId}`,
                    `.session-tab[data-id="${sessionId}"]`,
                    `.chat-session-tab[data-session-id="${sessionId}"]`
                ];
                
                // Try direct selector matches first
                for (const selector of possibleSelectors) {
                    const element = chatContainer.querySelector(selector);
                    if (element && (element.tagName === 'BUTTON' || element.onclick || element.click)) {
                        element.click();
                        return true;
                    }
                }
                
                // Fallback: search for buttons containing the session ID in text or attributes
                const allButtons = chatContainer.querySelectorAll('button, .clickable, [onclick]');
                for (const button of allButtons) {
                    const text = button.textContent || '';
                    const dataAttrs = [
                        button.dataset.sessionId,
                        button.dataset.session,
                        button.id,
                        button.getAttribute('data-session-id'),
                        button.getAttribute('data-session')
                    ].filter(Boolean);
                    
                    // Check if any of these match our session ID (full or short)
                    if (text.includes(sessionId) || text.includes(sessionIdShort) ||
                        dataAttrs.some(attr => attr === sessionId || attr.includes(sessionIdShort))) {
                        button.click();
                        return true;
                    }
                }
                
                return false;
                
            } catch (error) {
                console.error('Error finding session tab:', error);
                return false;
            }
        }
    </script>