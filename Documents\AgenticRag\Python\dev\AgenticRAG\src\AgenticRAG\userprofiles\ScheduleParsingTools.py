from imports import *

import re
from datetime import datetime, timedelta
from langchain_core.tools import BaseTool
from managers.manager_supervisors import SupervisorTaskState
from typing import Optional
from enum import Enum

class ScheduleType(Enum):
    ONCE = "once"
    RECURRING = "recurring"

class ParseRecurringScheduleTool(BaseTool):
    """Tool for parsing recurring schedule patterns"""
    name: str = "parse_recurring_schedule"
    description: str = "Parse recurring schedule patterns like 'every X minutes/hours/days' or 'trigger X every Y time_unit'"
    
    def _run(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Parse recurring schedule patterns"""
        try:
            prompt_lower = prompt.lower().strip()
            
            # Pattern: "trigger IMAP IDLE every 30 minutes"
            trigger_match = re.search(r'trigger\s+(.+?)\s+every\s+(\d+)\s+(second|minute|hour|day)s?', prompt_lower)
            if trigger_match:
                action = trigger_match.group(1)
                interval = int(trigger_match.group(2))
                unit = trigger_match.group(3)
                delay_seconds = self._convert_to_seconds(interval, unit)
                
                return f"TARGET_PROMPT: trigger {action}\nDELAY_SECONDS: {delay_seconds}\nSCHEDULE_TYPE: recurring"
            
            # Pattern: "do X every Y time_unit"
            generic_match = re.search(r'(.+?)\s+every\s+(\d+)\s+(second|minute|hour|day)s?', prompt_lower)
            if generic_match:
                action = generic_match.group(1).strip()
                interval = int(generic_match.group(2))
                unit = generic_match.group(3)
                delay_seconds = self._convert_to_seconds(interval, unit)
                
                return f"TARGET_PROMPT: {action}\nDELAY_SECONDS: {delay_seconds}\nSCHEDULE_TYPE: recurring"
            
            return "No recurring pattern found"
            
        except Exception as e:
            return f"Error parsing recurring schedule: {str(e)}"
    
    def _convert_to_seconds(self, interval: int, unit: str) -> float:
        """Convert time interval to seconds"""
        multipliers = {
            'second': 1,
            'minute': 60,
            'hour': 3600,
            'day': 86400
        }
        return float(interval * multipliers.get(unit, 1))


class ParseDailyScheduleTool(BaseTool):
    """Tool for parsing daily schedule patterns"""
    name: str = "parse_daily_schedule"
    description: str = "Parse daily schedule patterns like 'at 9am' or 'monday to friday' or 'send me X at Y time'"
    
    def _run(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Parse daily schedule patterns"""
        try:
            prompt_lower = prompt.lower().strip()
            
            # Pattern: "send me a good morning message at 9am monday to friday"
            daily_match = re.search(r'send\s+me\s+(.+?)\s+at\s+(\d{1,2})(am|pm)\s+(monday\s+to\s+friday|daily|weekdays?)', prompt_lower)
            if daily_match:
                message = daily_match.group(1)
                hour = int(daily_match.group(2))
                am_pm = daily_match.group(3)
                frequency = daily_match.group(4)
                
                # Convert to 24-hour format
                if am_pm == 'pm' and hour != 12:
                    hour += 12
                elif am_pm == 'am' and hour == 12:
                    hour = 0
                
                # Calculate delay until next occurrence
                now = datetime.now()
                target_time = now.replace(hour=hour, minute=0, second=0, microsecond=0)
                
                # If time has passed today, schedule for tomorrow
                if target_time <= now:
                    target_time += timedelta(days=1)
                
                # For weekdays only, adjust to next weekday if needed
                if 'monday to friday' in frequency or 'weekday' in frequency:
                    while target_time.weekday() >= 5:  # Saturday=5, Sunday=6
                        target_time += timedelta(days=1)
                
                delay_seconds = (target_time - now).total_seconds()
                
                return f"TARGET_PROMPT: send message: {message}\nDELAY_SECONDS: {delay_seconds}\nSCHEDULE_TYPE: recurring"
            
            return "No daily pattern found"
            
        except Exception as e:
            return f"Error parsing daily schedule: {str(e)}"


class ParseMonthlyScheduleTool(BaseTool):
    """Tool for parsing monthly schedule patterns"""
    name: str = "parse_monthly_schedule"
    description: str = "Parse monthly schedule patterns like 'first of the month' or 'every 15th' or 'email me X every Y of the month'"
    
    def _run(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Parse monthly schedule patterns"""
        try:
            prompt_lower = prompt.lower().strip()
            
            # Pattern: "email me a report every first of the month"
            monthly_match = re.search(r'email\s+me\s+(.+?)\s+every\s+(first|last|\d{1,2}(?:st|nd|rd|th)?)\s+of\s+the\s+month', prompt_lower)
            if monthly_match:
                content = monthly_match.group(1)
                day_spec = monthly_match.group(2)
                
                # Calculate next monthly occurrence
                now = datetime.now()
                if day_spec == 'first':
                    target_day = 1
                elif day_spec == 'last':
                    # Get last day of current month
                    next_month = now.replace(day=28) + timedelta(days=4)
                    target_day = (next_month - timedelta(days=next_month.day)).day
                else:
                    target_day = int(re.sub(r'[^\d]', '', day_spec))
                
                # Create target datetime for this month or next
                try:
                    target_time = now.replace(day=target_day, hour=9, minute=0, second=0, microsecond=0)
                    if target_time <= now:
                        # Move to next month
                        if now.month == 12:
                            target_time = target_time.replace(year=now.year + 1, month=1)
                        else:
                            target_time = target_time.replace(month=now.month + 1)
                except ValueError:
                    # Day doesn't exist in current month, try next month
                    if now.month == 12:
                        target_time = datetime(now.year + 1, 1, target_day, 9, 0, 0)
                    else:
                        target_time = datetime(now.year, now.month + 1, target_day, 9, 0, 0)
                
                delay_seconds = (target_time - now).total_seconds()
                
                return f"TARGET_PROMPT: email report: {content}\nDELAY_SECONDS: {delay_seconds}\nSCHEDULE_TYPE: recurring"
            
            return "No monthly pattern found"
            
        except Exception as e:
            return f"Error parsing monthly schedule: {str(e)}"


class ParseGenericScheduleTool(BaseTool):
    """Tool for parsing generic schedule patterns"""
    name: str = "parse_generic_schedule"
    description: str = "Parse generic schedule patterns like 'in X minutes' or immediate tasks, or any other schedule format"
    
    def _run(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Parse generic schedule patterns"""
        try:
            prompt_lower = prompt.lower().strip()
            
            # Pattern: "do X in Y time_unit"
            once_match = re.search(r'(.+?)\s+in\s+(\d+)\s+(second|minute|hour|day)s?', prompt_lower)
            if once_match:
                action = once_match.group(1).strip()
                interval = int(once_match.group(2))
                unit = once_match.group(3)
                delay_seconds = self._convert_to_seconds(interval, unit)
                
                return f"TARGET_PROMPT: {action}\nDELAY_SECONDS: {delay_seconds}\nSCHEDULE_TYPE: once"
            
            # Fallback: treat as immediate one-time task
            return f"TARGET_PROMPT: {prompt}\nDELAY_SECONDS: 0\nSCHEDULE_TYPE: once"
            
        except Exception as e:
            return f"Error parsing generic schedule: {str(e)}"
    
    def _convert_to_seconds(self, interval: int, unit: str) -> float:
        """Convert time interval to seconds"""
        multipliers = {
            'second': 1,
            'minute': 60,
            'hour': 3600,
            'day': 86400
        }
        return float(interval * multipliers.get(unit, 1))