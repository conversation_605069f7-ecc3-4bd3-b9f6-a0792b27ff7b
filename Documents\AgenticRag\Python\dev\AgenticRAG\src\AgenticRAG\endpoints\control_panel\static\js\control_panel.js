/* Control Panel JavaScript - Extracted from Dashboard_endpoint.py */

// Global variables
let refreshInterval;
let countdown = 10; // Set to 10 seconds
let autoRefreshEnabled = false; // Disabled by default
let currentView = 'overview';
let allUsers = [];

// HTML escape function for safe display
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
}

// Auto-refresh functions
function startAutoRefresh() {
    if (refreshInterval) clearInterval(refreshInterval);
    countdown = 10;
    
    refreshInterval = setInterval(() => {
        if (!autoRefreshEnabled) return;
        
        countdown--;
        document.getElementById('refreshTimer').textContent = `Auto-refresh: ${countdown}s`;
        
        if (countdown <= 0) {
            // Instead of full page reload, just refresh the current view
            refreshCurrentView();
            countdown = 10; // Reset countdown
        }
    }, 1000);
}

function toggleAutoRefresh() {
    autoRefreshEnabled = !autoRefreshEnabled;
    const toggleBtn = document.getElementById('refreshToggle');
    const timer = document.getElementById('refreshTimer');
    
    if (autoRefreshEnabled) {
        toggleBtn.textContent = 'Disable';
        timer.style.opacity = '1';
        countdown = 10; // Reset countdown when enabling
        timer.textContent = `Auto-refresh: ${countdown}s`;
    } else {
        toggleBtn.textContent = 'Enable';
        timer.style.opacity = '0.5';
        timer.textContent = 'Auto-refresh: Disabled';
    }
}

function refreshCurrentView() {
    // Store current scroll positions and active states before refresh
    const stateToRestore = {
        mainScrollTop: window.pageYOffset,
        chatContainerScroll: null,
        requestsContainerScroll: null,
        activeSessionTab: null,
        currentView: currentView
    };
    
    // Store chat container scroll position
    const chatContainer = document.getElementById('chatContainer');
    if (chatContainer) {
        stateToRestore.chatContainerScroll = chatContainer.scrollTop;
    }
    
    // Store requests container scroll position
    const requestsContainer = document.getElementById('requestsContainer');
    if (requestsContainer) {
        stateToRestore.requestsContainerScroll = requestsContainer.scrollTop;
    }
    
    // Store active session tab
    const activeSessionTab = document.querySelector('.session-tab.active');
    if (activeSessionTab) {
        stateToRestore.activeSessionTab = activeSessionTab.dataset.sessionId;
    }
    
    // Function to restore state after refresh
    const restoreState = () => {
        setTimeout(() => {
            // Restore main scroll position
            window.scrollTo(0, stateToRestore.mainScrollTop);
            
            // Restore chat container scroll
            if (stateToRestore.chatContainerScroll !== null) {
                const chatContainer = document.getElementById('chatContainer');
                if (chatContainer) {
                    chatContainer.scrollTop = stateToRestore.chatContainerScroll;
                }
            }
            
            // Restore requests container scroll
            if (stateToRestore.requestsContainerScroll !== null) {
                const requestsContainer = document.getElementById('requestsContainer');
                if (requestsContainer) {
                    requestsContainer.scrollTop = stateToRestore.requestsContainerScroll;
                }
            }
            
            // Restore active session tab
            if (stateToRestore.activeSessionTab) {
                const targetTab = document.querySelector(`[data-session-id="${stateToRestore.activeSessionTab}"]`);
                if (targetTab) {
                    switchChatSession(stateToRestore.activeSessionTab);
                }
            }
        }, 100); // Small delay to ensure content is loaded
    };
    
    // Perform the refresh based on current view
    if (currentView === 'overview') {
        loadSystemOverview().finally(restoreState);
    } else if (currentView === 'user-requests' && document.getElementById('userGuidInput').value.trim()) {
        loadUserRequests().finally(restoreState);
    } else if (currentView === 'chat-history' && document.getElementById('chatUserGuidInput').value.trim()) {
        loadChatHistory().finally(restoreState);
    } else {
        // For other views or when no specific action is needed
        restoreState();
    }
}

// Tab Management Functions
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(tab => tab.classList.remove('active'));
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    
    // Show selected tab content
    document.getElementById(tabName).classList.add('active');
    
    // Add active class to clicked button
    event.target.classList.add('active');
    
    // Update current view for refresh purposes
    currentView = tabName;
    
    // Auto-load data when switching to specific tabs
    if (tabName === 'user-list') {
        loadUserList();
    }
}

function showTabByName(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(tab => tab.classList.remove('active'));
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    
    // Show selected tab content
    document.getElementById(tabName).classList.add('active');
    
    // Add active class to corresponding button
    const buttons = document.querySelectorAll('.tab-button');
    buttons.forEach(btn => {
        if (btn.textContent.toLowerCase().includes(tabName.split('-')[1])) {
            btn.classList.add('active');
        }
    });
    
    currentView = tabName;
    
    // Auto-load data when switching to specific tabs
    if (tabName === 'user-list') {
        loadUserList();
    }
}

// User Management Functions
async function loadUserList() {
    const container = document.getElementById('userListContainer');
    container.innerHTML = '<div class="loading">Loading user list...</div>';
    
    try {
        const response = await fetch('/dashboard/api/user-list');
        const data = await response.json();
        
        if (data.error) {
            container.innerHTML = `<div class="error">Error: ${data.error}</div>`;
            return;
        }
        
        allUsers = data.users || [];
        displayUserList(allUsers);
        
    } catch (error) {
        container.innerHTML = `<div class="error">Failed to load user list: ${error.message}</div>`;
    }
}

function displayUserList(users) {
    const container = document.getElementById('userListContainer');
    
    if (!users || users.length === 0) {
        container.innerHTML = '<div class="no-data">No users found</div>';
        return;
    }
    
    let html = `<div style="margin-bottom: 1rem; color: #666; font-size: 0.9rem;">
        Found ${users.length} user(s)
    </div>`;
    
    users.forEach(user => {
        html += `
        <div class="user-list-item">
            <div class="user-info">
                <div class="user-name">${user.username}</div>
                <div class="user-details">
                    Rank: ${user.rank} | GUID: ${user.user_guid.substring(0, 8)}...
                    ${user.device_guid ? ` | Device: ${user.device_guid.substring(0, 8)}...` : ''}
                </div>
            </div>
            <div class="user-actions">
                <button class="btn-small btn-requests" onclick="switchToUserRequests('${user.user_guid}')">
                    Requests
                </button>
                <button class="btn-small btn-chat" onclick="switchToUserChat('${user.user_guid}')">
                    Chat
                </button>
            </div>
        </div>`;
    });
    
    container.innerHTML = html;
}

function filterUsers() {
    const searchTerm = document.getElementById('userSearchInput').value.toLowerCase();
    
    if (!searchTerm) {
        displayUserList(allUsers);
        return;
    }
    
    const filteredUsers = allUsers.filter(user => 
        user.username.toLowerCase().includes(searchTerm) ||
        user.user_guid.toLowerCase().includes(searchTerm) ||
        user.rank.toLowerCase().includes(searchTerm)
    );
    
    displayUserList(filteredUsers);
}

function switchToUserRequests(userGuid) {
    // Switch to requests tab
    showTabByName('user-requests');
    
    // Fill in the GUID and load requests
    document.getElementById('userGuidInput').value = userGuid;
    loadUserRequests();
}

function switchToUserChat(userGuid) {
    // Switch to chat tab
    showTabByName('chat-history');
    
    // Fill in the GUID and load chat history
    document.getElementById('chatUserGuidInput').value = userGuid;
    loadChatHistory();
}

// Scheduled Request Functions
async function loadUserRequests() {
    const userGuid = document.getElementById('userGuidInput').value.trim();
    if (!userGuid) {
        alert('Please enter a user GUID');
        return;
    }
    
    currentView = 'user-requests'; // Track current view
    const container = document.getElementById('requestsContainer');
    container.innerHTML = '<div class="loading">Loading scheduled requests...</div>';
    
    try {
        const response = await fetch(`/dashboard/api/user-requests?user_guid=${encodeURIComponent(userGuid)}`);
        const data = await response.json();
        
        if (data.error) {
            container.innerHTML = `<div class="error">Error: ${data.error}</div>`;
            return;
        }
        
        displayUserRequests(data);
        
    } catch (error) {
        container.innerHTML = `<div class="error">Failed to load requests: ${error.message}</div>`;
    }
}

function displayUserRequests(data) {
    const container = document.getElementById('requestsContainer');
    
    if (!data.requests || data.requests.length === 0) {
        container.innerHTML = '<div class="no-requests">No scheduled requests found for this user</div>';
        return;
    }
    
    let html = `
        <div class="user-info-header">
            <strong>User:</strong> ${data.username || 'Unknown'} 
            <strong>GUID:</strong> ${data.user_guid}
        </div>
        <table class="requests-table">
            <thead>
                <tr>
                    <th>Status</th>
                    <th>Schedule</th>
                    <th>Target</th>
                    <th>Next Run</th>
                    <th>Call Trace</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>`;
    
    data.requests.forEach(request => {
        const callTrace = request.call_trace || [];
        const callTraceHtml = callTrace.length > 0 
            ? `<div class="call-trace">${callTrace.map(item => escapeHtml(item)).join('<br>')}</div>`
            : '<span style="color: #666;">No trace</span>';
        
        html += `
            <tr>
                <td><span class="status-${request.status}">${request.status}</span></td>
                <td>${escapeHtml(request.schedule_prompt || 'N/A')}</td>
                <td>${escapeHtml(request.target_prompt || 'N/A')}</td>
                <td>${request.next_execution || 'N/A'}</td>
                <td>${callTraceHtml}</td>
                <td>
                    ${request.status === 'active' ? 
                        `<button class="btn-action btn-danger" onclick="cancelScheduledRequest('${request.scheduled_guid}')">Cancel</button>` 
                        : ''}
                </td>
            </tr>`;
    });
    
    html += '</tbody></table>';
    container.innerHTML = html;
}

async function cancelScheduledRequest(scheduledGuid) {
    if (!confirm('Are you sure you want to cancel this scheduled request?')) {
        return;
    }
    
    try {
        const response = await fetch('/dashboard/api/cancel-request', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ scheduled_guid: scheduledGuid })
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Request cancelled successfully');
            loadUserRequests(); // Reload the requests
        } else {
            alert(`Failed to cancel request: ${result.error || 'Unknown error'}`);
        }
    } catch (error) {
        alert(`Error cancelling request: ${error.message}`);
    }
}

// Chat History Functions
async function loadChatHistory() {
    const userGuid = document.getElementById('chatUserGuidInput').value.trim();
    if (!userGuid) {
        alert('Please enter a user GUID');
        return;
    }
    
    currentView = 'chat-history'; // Track current view
    const container = document.getElementById('chatContainer');
    container.innerHTML = '<div class="loading">Loading chat history...</div>';
    
    try {
        const response = await fetch(`/dashboard/api/user-chat-history?user_guid=${encodeURIComponent(userGuid)}`);
        const data = await response.json();
        
        if (data.error) {
            container.innerHTML = `<div class="error">Error: ${data.error}</div>`;
            return;
        }
        
        displayChatHistory(data);
        
    } catch (error) {
        container.innerHTML = `<div class="error">Failed to load chat history: ${error.message}</div>`;
    }
}

function displayChatHistory(data) {
    const container = document.getElementById('chatContainer');
    
    if (!data.sessions || data.sessions.length === 0) {
        container.innerHTML = '<div class="no-data">No chat history found for this user</div>';
        return;
    }
    
    // Display chat sessions
    let html = `
        <div class="chat-sessions">
            <div class="session-tabs">
                ${data.sessions.map((session, index) => `
                    <button class="session-tab ${index === 0 ? 'active' : ''}" 
                            data-session-id="${session.session_id}"
                            onclick="switchChatSession('${session.session_id}')">
                        Session ${index + 1}
                        <span class="session-time">${new Date(session.start_time).toLocaleString()}</span>
                    </button>
                `).join('')}
            </div>
            <div class="messages-container" id="messagesContainer">
                ${displayChatMessages(data.sessions[0].messages)}
            </div>
        </div>`;
    
    container.innerHTML = html;
    
    // Store sessions for switching
    window.chatSessions = data.sessions;
}

function displayChatMessages(messages) {
    if (!messages || messages.length === 0) {
        return '<div class="no-messages">No messages in this session</div>';
    }
    
    return messages.map(msg => `
        <div class="message ${msg.role}">
            <div class="message-header">
                <span class="message-role">${msg.role}</span>
                <span class="message-time">${new Date(msg.timestamp).toLocaleTimeString()}</span>
            </div>
            <div class="message-content">${escapeHtml(msg.content)}</div>
        </div>
    `).join('');
}

function switchChatSession(sessionId) {
    // Update active tab
    document.querySelectorAll('.session-tab').forEach(tab => {
        tab.classList.toggle('active', tab.dataset.sessionId === sessionId);
    });
    
    // Find and display session messages
    const session = window.chatSessions.find(s => s.session_id === sessionId);
    if (session) {
        document.getElementById('messagesContainer').innerHTML = displayChatMessages(session.messages);
    }
}

// System Overview Functions
async function loadSystemOverview() {
    currentView = 'overview';
    const container = document.getElementById('overview');
    
    try {
        const response = await fetch('/dashboard/api/system-status');
        const data = await response.json();
        
        if (data.error) {
            container.innerHTML = `<div class="error">Error: ${data.error}</div>`;
            return;
        }
        
        displaySystemOverview(data);
        
    } catch (error) {
        container.innerHTML = `<div class="error">Failed to load system overview: ${error.message}</div>`;
    }
}

function displaySystemOverview(data) {
    // This would update the overview metrics
    // Implementation depends on specific metrics structure
    console.log('System overview data:', data);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Start auto-refresh timer (disabled by default)
    startAutoRefresh();
    
    // Set initial active tab
    const firstTab = document.querySelector('.tab-button');
    if (firstTab) {
        firstTab.classList.add('active');
    }
});

// Export functions for use in HTML
window.toggleAutoRefresh = toggleAutoRefresh;
window.showTab = showTab;
window.loadUserRequests = loadUserRequests;
window.loadChatHistory = loadChatHistory;
window.loadUserList = loadUserList;
window.filterUsers = filterUsers;
window.cancelScheduledRequest = cancelScheduledRequest;
window.switchChatSession = switchChatSession;
window.switchToUserRequests = switchToUserRequests;
window.switchToUserChat = switchToUserChat;