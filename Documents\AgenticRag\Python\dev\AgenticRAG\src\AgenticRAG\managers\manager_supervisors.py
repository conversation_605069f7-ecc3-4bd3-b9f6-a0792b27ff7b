from imports import *

import bisect
from threading import Lock
from uuid import UUID, uuid4
import operator

from typing import (
    Callable,
    Optional,
    Sequence,
    Union,
    Annotated,
    cast,
    Any,
    Tuple,
    Dict,
    Optional,
)
from typing_extensions import TypedDict

from langgraph.types import Command
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.language_models.base import BaseLanguageModel
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage, AnyMessage
from langchain_core.tools import BaseTool
from langgraph.graph.state import CompiledStateGraph
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field, ConfigDict
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent
from managers.manager_users import ZairaUserManager
from managers.manager_prompts import PromptManager
from userprofiles.ZairaMessage import ZairaMessage

class SupervisorSection(BaseModel):
    name: str = Field(
        None, description="Name for this section of the report.",
    )
    description: str = Field(
        None, description="Brief overview of the main topics and concepts to be covered in this section.",
    )
    description_list: Annotated[list[str], None] = Field(
        default_factory=list, description="Brief overview of the main topics and concepts to be covered in this section.",
    )

    @classmethod
    def default(cls):
        """Constructor with no arguments."""
        return cls()

    @classmethod
    def from_values(cls, name: str, description):
        """Constructor with arguments."""
        if isinstance(description, str):
            return cls(name=name, description=description, description_list=[])
        if isinstance(description, list):
            return cls(name=name, description="", description_list=description)
        
    def get_description(self):
        if not self.description or self.description == "":
            return self.description_list
        return self.description
    
    def __str__(self):
        description = self.get_description()
        if isinstance(description, str):
            return description
        else:
            return ", ".join(description)

class SupervisorRouteState(BaseModel):
    step: str = Field(
        None, description="The next step in the routing process"
    )



class SupervisorTaskInput(BaseModel):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.ZairaUser import ZairaUser

    original_input: str = Field(None, description="Query that was entered initially by the user.")
    user_guid: str = Field(None, description="User ID")

class SupervisorTaskOutput(BaseModel):
    messages: Annotated[list[AnyMessage], add_messages] = Field(
        None, description="Add to this when returning from a tool or agent"
    )

class SupervisorTaskState(SupervisorTaskInput, SupervisorTaskOutput):
    # next: str = Field(
    #     None, description="What node to execute next. LLM does not use this variable.",
    # )
    scheduled_guid: UUID = Field(None, description="Task identifier matching ZairaUser.my_requests key")
    chat_session_guid: Optional[UUID] = Field(None, description="Chat session GUID for routing messages to specific session")
    original_source: Optional[str] = Field(None, description="Source/origin of the request (e.g., bot name)")
    sections: dict[str, SupervisorSection] = Field(
        default_factory=dict, description="Sections of processed data.",
    ) # List of report sections. Unsure if this should be its own class
    completed_sections: Annotated[list, None] = Field(
        default_factory=list, description="Completed sections of the report. LLM does not use this variable.",
    ) # All workers write to this key in parallel. Unsure if this should be its own class
    completed_tasks: Annotated[list[str], None] = Field(
        default_factory=list, 
        description="Tasks that have already been executed.",
    ) # All workers write to this key in parallel. Unsure if this should be its own class
    call_trace: Annotated[list[str], None] = Field(
        default_factory=list, description="The task or supervisor that was last called. Currently used in debug only."
    )
    reasoning_steps: Annotated[list[str], None] = Field(
        default_factory=list, description="Chain of thought reasoning steps for transparent decision making."
    )
    conversation_history: Annotated[list[AnyMessage], None] = Field(
        default_factory=list, description="Full conversation history including human messages and supervisor outputs for context preservation across supervisor transitions."
    )
    cot_chosen_task: Optional[str] = Field(None, description="Task chosen by CoT reasoning, persisted across executions")
    cot_workflow_stage: str = Field("initial", description="Current stage in CoT workflow: initial, always_first, chosen_task, always_last, complete")
    
    async def update_request_call_trace(self, call_trace=None, overwrite_message=None):
        """Update the associated request's call trace with the current supervisor data"""
        if not self.scheduled_guid or not self.user_guid:
            return
        
        # Use provided call_trace or fall back to self.call_trace
        trace_to_use = call_trace if call_trace is not None else self.call_trace
        
        try:
            # Find the user and their request
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(self.user_guid)
            
            if user and hasattr(user, 'my_requests') and self.scheduled_guid in user.my_requests:
                request = user.my_requests[self.scheduled_guid]
                
                # Update the request's call trace
                if hasattr(request, 'call_trace'):
                    request.call_trace = trace_to_use.copy() if isinstance(trace_to_use, list) else []
                
        except Exception:
            # Silently fail - don't disrupt supervisor execution if update fails
            pass

class SupervisorTaskConfig(TypedDict):
    complexity: int = 1



class SupervisorTask_Base:
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.ZairaUser import ZairaUser
    scheduled_guid: UUID = None
    name: str = ""
    _prompt: str = ""
    prompt_id: str = ""
    model: BaseLanguageModel = None
    always_call_FIRST: bool = False
    always_call_LAST: bool = False

    langgraph: StateGraph = None
    compiled_langgraph: CompiledStateGraph = None
    thread_config: dict = {}

    def __init__(self, name: str, prompt: str = "", scheduled_guid: UUID = None, prompt_id: str = "", model: BaseLanguageModel = None):
        if model is None:
            model = SupervisorManager.get_instance().default_model
        self.scheduled_guid = scheduled_guid
        self.name = name
        self._prompt = prompt
        self.prompt_id = prompt_id
        self.model = model
        self.langgraph = StateGraph(SupervisorTaskState)
        self.thread_config = {"thread_id": uuid4()}

    def __repr__(self):
        return f"Task({self.scheduled_guid}, {self.name})"
    
    def get_tools(self):
        return []

    def get_prompt(self, prefix_zaira_personality: bool = False):
        ret_val = PromptManager.get_prompt(self.prompt_id) if self.prompt_id else self._prompt
        if prefix_zaira_personality:
            ret_val = PromptManager.get_prompt("AskZaira_Prompt") + ret_val
        return ret_val
        
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        raise RuntimeError(f"{self.name} NOT_IMPLEMENTED! Do not use the Base class directly.")
    
    @staticmethod
    async def get_chat_session_from_state(state: SupervisorTaskState):
        """Helper method to get the correct chat session from state - specific session or user's current session"""
        user = await ZairaUserManager.find_user(state.user_guid) if state.user_guid else None
        if not user:
            return None
        
        # Use specific session if provided and exists, otherwise fall back to user's current session
        if hasattr(state, 'chat_session_guid') and state.chat_session_guid and state.chat_session_guid in user.chat_history:
            return user.chat_history[state.chat_session_guid]
        else:
            return user.get_current_chat_session()
    
    async def llm_call_wrapper(self, state: SupervisorTaskState):
        user = await ZairaUserManager.find_user(state.user_guid)
        if Globals.is_debug_values():
            # Get the correct chat session for logging
            chat_session = await self.get_chat_session_from_state(state)
            LogFire.log("TASK", f"Task {self.name} [scheduled_guid: {getattr(state, 'scheduled_guid', 'NOT_FOUND')}] ", state.messages[-1].content.strip() + "'", chat=chat_session)
        result = await self.llm_call(state)
        if result is None:
            result = ""
        if isinstance(result, Command):
            return result
        # If string or similar was returned, wrap it neatly with the langgraph Command class
        ret_val = {}
        updated_call_trace = state.call_trace + [f"{self.name}: llm_call"]
        ret_val.update({"call_trace": updated_call_trace})
        # Update request's call trace when supervisor call trace is modified
        await state.update_request_call_trace(updated_call_trace)
        if result != "":
            ret_val.update({"messages": result})
        ret_val.update({"completed_tasks": state.completed_tasks + [self.name]})
        
        result_to_show = result if result else '""'
        # Use specific session if provided in state, otherwise fall back to user's active session
        session_for_debug = state.chat_session_guid if hasattr(state, 'chat_session_guid') and state.chat_session_guid else user.session_guid
        user.receive_debug_message(f"[{self.name}] {state.messages[-1].content} >>>>>> {result_to_show}", session_for_debug, state.call_trace)
        return Command(update=ret_val)
    
    def compile_default(self) -> "SupervisorTask_Base":
        self.langgraph.add_node("llm_call", self.llm_call_wrapper)
        self.langgraph.add_edge(START, "llm_call")
        self.langgraph.add_edge("llm_call", END)

        self.compiled_langgraph = self.langgraph.compile()
        return self
    
    async def llm_call_internal(self, state: SupervisorTaskState):
        #state.call_trace = []
        #state.completed_sections = []
        #state.completed_tasks = []
        if self.compiled_langgraph is None:
            self.compile_default()
        result = await self.compiled_langgraph.ainvoke(input=state, stream_mode="updates", config=self.thread_config)
        return Command(update=result[-1])
    
    async def call_task_with_query(self, query: str = "", user: "ZairaUser" = None, original_source: Optional[str] = None):
        """Function to be called from outwith the Supervisors"""
        langgraph = StateGraph(SupervisorTaskState)
        langgraph.add_node("llm_call_internal", self.llm_call_internal)
        langgraph.add_edge(START, "llm_call_internal")
        langgraph.add_edge("llm_call_internal", END)

        user_guid = str(user.user_guid) if user else ""
        state = SupervisorTaskState(original_input=query, user_guid=user_guid, messages=[HumanMessage(query)], call_trace=[f"{self.name}: call_task"], original_source=original_source)
        compiled_langgraph = langgraph.compile()
        result = await compiled_langgraph.ainvoke(state)
        response = result["messages"][-1]
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        
        return {"result": response, "call_trace": result["call_trace"]}
    
    def compile_default(self) -> "SupervisorTask_Base":
        self.langgraph.add_node("llm_call", self.llm_call_wrapper)
        self.langgraph.add_edge(START, "llm_call")
        self.langgraph.add_edge("llm_call", END)

        self.compiled_langgraph = self.langgraph.compile()
        return self
    
    async def llm_call_internal(self, state: SupervisorTaskState):
        #state.call_trace = []
        #state.completed_sections = []
        #state.completed_tasks = []
        if self.compiled_langgraph is None:
            self.compile_default()
        result = await self.compiled_langgraph.ainvoke(input=state, stream_mode="updates", config=self.thread_config)
        return Command(update=result[-1])
    
    async def call_task(self, state: SupervisorTaskState):
        """Function to be called from outwith the Supervisors"""
        langgraph = StateGraph(SupervisorTaskState)
        langgraph.add_node("llm_call_internal", self.llm_call_internal)
        langgraph.add_edge(START, "llm_call_internal")
        langgraph.add_edge("llm_call_internal", END)
        compiled_langgraph = langgraph.compile()
        result = await compiled_langgraph.ainvoke(state)
        response = result["messages"][-1]
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        
        return {"result": response, "call_trace": result["call_trace"]}


class SupervisorTask_SingleAgent(SupervisorTask_Base):
    _tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]]
    enforce_HITL_before_tool_call: bool = False
    callback_response: str = ""

    model_config = ConfigDict(arbitrary_types_allowed=True)  # Enable arbitrary types

    def __init__(self, name: str, prompt: str = "", prompt_id: str = "", tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]] = [], enforce_HITL_before_tool_call = False, model: BaseLanguageModel = None):
        super().__init__(name=name, prompt=prompt, prompt_id=prompt_id, model=model)
        self._tools = tools
        self.enforce_HITL_before_tool_call = enforce_HITL_before_tool_call
    
    def get_tools(self):
        return self._tools
        
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        used_model = self.model
        if self._tools:
            used_model = cast(BaseChatModel,cast(BaseChatModel, used_model).bind_tools(self._tools))
            tools_by_name = {tool.name: tool for tool in self._tools}
            tool_prompts = [
                f"{tool.name} prompt: {tool.description}."
                for tool in self._tools
            ]
            full_prompt = f"{self.get_prompt()}\n" + "\n".join(tool_prompts)
        else:
            full_prompt = f"{self.get_prompt()}\n"
            tools_by_name = []

        results = []
        call_trace = state.call_trace
        user = await ZairaUserManager.find_user(state.user_guid)
        async def call_my_tool(tool_call):
            tool = tools_by_name[tool_call["name"]]
            from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
            from managers.manager_users import ZairaUserManager

            self.callback_response = ""
            async def callback(task: LongRunningZairaRequest, response: str):
                self.callback_response = response

            call_trace.append(self.name + ": tool " + tool.name)
            if "state" not in tool_call["args"] or not tool_call["args"]["state"]:
                # Get the correct chat session for logging
                chat_session = await self.get_chat_session_from_state(state)
                LogFire.log("TASK", "tool_call", f"Adding state to tool {tool.name}. user_guid: {getattr(state, 'user_guid', 'NOT_FOUND')}, scheduled_guid: {getattr(state, 'scheduled_guid', 'NOT_FOUND')}", chat=chat_session)
                tool_call["args"]["state"] = state

            async def handle_tool_response():
                observation = await tool.ainvoke(input=tool_call["args"])
                results.append(ToolMessage(content=observation, tool_call_id=tool_call["id"]))
            
            if self.enforce_HITL_before_tool_call == True:
                # Get user's active task to request human input
                await user.my_requests[state.scheduled_guid].request_human_in_the_loop(f"About to call tool: {tool.name}({tool.description}).\n Proceed? (yes/no)", callback, True)
                if "yes" in self.callback_response.lower():
                    await handle_tool_response()
            else:
                await handle_tool_response()
        
        #if len(self.tools) == 1:
            # We somehow need to create the tool_args manually for this optimisation
            #await call_my_tool({"name": self.tools[0].name, "args": {"query": state.original_input, "state": state}})
        #else:
        from datetime import datetime, timezone
        user_info = {"name": getattr(user, 'username', getattr(user, 'user_guid', 'unknown_user')), "time": datetime.now(timezone.utc)}
        chat_history = user.get_chat_history()
        messages = chat_history
        my_prompt = self.get_prompt(True)
        messages.insert(0, SystemMessage(content=f"You are an agent tasked with answering a question using the following tools: {tools_by_name}."
                                                " Your tools are sorted in order of relevance and importance. If a tool earlier in the list could potentially solve the issue, try that one first."
                                                " Given the following original_input,"
                                                " determine which tools need to be called to create the best result. Each tool can only be called once."
                                                " If your tool list is empty, respond to the best of your abilities with an answer to the original_input."
                                                " Consider the above as your logic. "
                                                f"My prompt: {my_prompt}"
                                                " User info: " + " ".join(user_info) + ". "
                                                " Consider the following prompts as the individual task's logic: "
                                                f"\n{full_prompt}"))
        # Context is already available through chat history and system prompts
        result = await used_model.ainvoke(input=messages)
        if self._tools:
            # Check if LLM decided to call any tools
            if result.tool_calls:
                # Update request's call trace when supervisor call trace is modified
                await state.update_request_call_trace(call_trace, f"Activating {result.tool_calls}")
                # Tools were called - execute them
                for tool_call in result.tool_calls:
                    await call_my_tool(tool_call)
                
                # Send debug message summarizing tool calls
                try:
                    if user:
                        tool_names = [tc["name"] for tc in result.tool_calls]
                        # Use specific session if provided in state, otherwise fall back to user's active session
                        session_for_debug = state.chat_session_guid if hasattr(state, 'chat_session_guid') and state.chat_session_guid else user.session_guid
                        user.receive_debug_message(f"[{self.name}] SingleAgent executed tools: {', '.join(tool_names)}", session_for_debug, call_trace)
                except Exception:
                    pass  # Silently fail to avoid disrupting flow
            else:
                # LLM has tools available but chose not to use any - respond directly
                await state.update_request_call_trace(call_trace, f"Agent response: {result}")
                results = result
                call_trace.append(f"{self.name}: llm_call")
                
                # Send debug message summarizing direct response
                try:
                    if user:
                        # Use specific session if provided in state, otherwise fall back to user's active session
                        session_for_debug = state.chat_session_guid if hasattr(state, 'chat_session_guid') and state.chat_session_guid else user.session_guid
                        user.receive_debug_message(f"[{self.name}] SingleAgent responded directly: {result.content}", session_for_debug, call_trace)
                except Exception:
                    pass  # Silently fail to avoid disrupting flow
        else:
            # No tools available - respond directly
            results = result
            call_trace.append(f"{self.name}: llm_call")
            
            # Send debug message summarizing no-tool response
            try:
                if user:
                    # Use specific session if provided in state, otherwise fall back to user's active session
                    session_for_debug = state.chat_session_guid if hasattr(state, 'chat_session_guid') and state.chat_session_guid else user.session_guid
                    user.receive_debug_message(f"[{self.name}] SingleAgent (no tools) responded: {result.content}", session_for_debug, call_trace)
            except Exception:
                pass  # Silently fail to avoid disrupting flow
            
        return Command(update={"call_trace": call_trace, "messages": results, "completed_tasks": state.completed_tasks + [self.name]})
    
    def compile_default(self) -> "SupervisorTask_Base":
        if self.enforce_HITL_before_tool_call == False:
            super().compile_default()
        else:
            self.langgraph.add_node("llm_call", self.llm_call_wrapper)
            self.langgraph.add_edge(START, "llm_call")
            self.langgraph.add_edge("llm_call", END)
            self.compiled_langgraph = self.langgraph.compile()
            return self



class SupervisorTask_Create_agent(SupervisorTask_Base):
    _tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]]
    enforce_HITL_before_tool_call: bool = False

    model_config = ConfigDict(arbitrary_types_allowed=True)  # Enable arbitrary types

    def __init__(self, name: str, prompt: str = "", prompt_id:str = "", tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]] = [], enforce_HITL_before_tool_call = False, model: BaseLanguageModel = None):
        super().__init__(name=name, prompt=prompt, prompt_id=prompt_id, model=model)
        self._tools = tools
        self.enforce_HITL_before_tool_call = enforce_HITL_before_tool_call
    
    def get_tools(self):
        return self._tools
        
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task using create_react_agent with SingleAgent state parsing"""
        try:
            user = await ZairaUserManager.find_user(state.user_guid)
            user_info = {"name": getattr(user, 'username', getattr(user, 'user_guid', 'unknown_user'))}
            chat_history = user.get_chat_history()
            
            # Wrap tools to inject state automatically
            wrapped_tools = []
            for tool in self._tools:
                # Create a wrapper class that preserves tool properties
                class StateInjectingTool(BaseTool):
                    name: str = tool.name
                    description: str = tool.description
                    original_tool: Any = None
                    captured_state: Any = None
                    
                    def __init__(self, original_tool, captured_state):
                        super().__init__(
                            name=tool.name, 
                            description=tool.description,
                            original_tool=original_tool,
                            captured_state=captured_state
                        )
                        # Copy any additional attributes from the original tool
                        for attr in ['args_schema', 'return_direct', 'verbose', 'callbacks', 'callback_manager', 'metadata']:
                            if hasattr(original_tool, attr):
                                setattr(self, attr, getattr(original_tool, attr))
                    
                    def _run(self, *args, **kwargs):
                        # Not used for async tools
                        raise NotImplementedError("Use async version")
                    
                    async def _arun(self, *args, **kwargs):
                        # Inject state if not present
                        if 'state' not in kwargs:
                            kwargs['state'] = self.captured_state
                            # Get the correct chat session for logging
                            chat_session = await SupervisorTask_Base.get_chat_session_from_state(self.captured_state)
                            LogFire.log("TASK", "tool_call", f"Injecting state to tool {self.name}. user_guid: {getattr(self.captured_state, 'user_guid', 'NOT_FOUND')}, scheduled_guid: {getattr(self.captured_state, 'scheduled_guid', 'NOT_FOUND')}", chat=chat_session)
                        # Call the original tool
                        return await self.original_tool._arun(*args, **kwargs)
                
                # Create wrapped tool instance
                wrapped_tool = StateInjectingTool(tool, state)
                wrapped_tools.append(wrapped_tool)
            
            # Create the ReAct agent using LangGraph's create_react_agent with wrapped tools
            agent_executor = create_react_agent(self.model, wrapped_tools)
            
            # Use same state parsing mechanism as SupervisorTask_SingleAgent
            tools_by_name = {tool.name: tool for tool in self._tools} if self._tools else {}
            tool_prompts = [
                f"{tool.name} prompt: {tool.description}."
                for tool in self._tools
            ] if self._tools else []
            full_prompt = f"{self.get_prompt()}\n" + "\n".join(tool_prompts)
            
            # Build messages using SingleAgent pattern
            messages = chat_history.copy()
            messages.insert(0, SystemMessage(content=f"You are an agent tasked with answering a question using the following tools: {tools_by_name}."
                                                    " Your tools are sorted in order of relevance and importance. If a tool earlier in the list could potentially solve the issue, try that one first."
                                                    " Given the following original_input,"
                                                    " determine which tools need to be called to create the best result. Each tool can only be called once."
                                                    " If your tool list is empty, respond to the best of your abilities with an answer to the original_input."
                                                    " Consider the above as your logic. "
                                                    f"My prompt: {self.get_prompt(True)}"
                                                    " User info: " + str(user_info) + ". "
                                                    " Consider the following prompts as the individual task's logic: "
                                                    f"\n{full_prompt}"))
            
            # Add conversation history if available
            if hasattr(state, 'conversation_history') and state.conversation_history:
                messages.extend(state.conversation_history)
            
            # Context is already available through chat history and system prompts
            
            # Execute the user input with full conversation context
            result = await agent_executor.ainvoke({
                "messages": messages
            })
            
            # Extract the final message from the response
            updated_call_trace = state.call_trace + [f"{self.name}: create_react_agent"]
            # Update request's call trace when supervisor call trace is modified
            await state.update_request_call_trace(updated_call_trace)
            
            # Send debug message summarizing the create_react_agent execution
            try:
                user_for_debug = await ZairaUserManager.find_user(state.user_guid)
                if user_for_debug:
                    if "messages" in result and result["messages"]:
                        final_message = result["messages"][-1]
                        if hasattr(final_message, "content"):
                            response_preview = final_message.content[:100] + "..." if len(final_message.content) > 100 else final_message.content
                        elif isinstance(final_message, dict) and "content" in final_message:
                            response_preview = final_message["content"][:100] + "..." if len(final_message["content"]) > 100 else final_message["content"]
                        else:
                            response_preview = str(final_message)[:100] + "..."
                    else:
                        response_preview = str(result)[:100] + "..."
                    
                    # Use specific session if provided in state, otherwise fall back to user's active session
                    session_for_debug = state.chat_session_guid if hasattr(state, 'chat_session_guid') and state.chat_session_guid else user.session_guid
                    user_for_debug.receive_debug_message(f"[{self.name}] CreateAgent completed: {response_preview}", session_for_debug, updated_call_trace)
            except Exception:
                pass  # Silently fail to avoid disrupting flow
            
            if "messages" in result and result["messages"]:
                final_message = result["messages"][-1]
                if hasattr(final_message, "content"):
                    return Command(update={"call_trace": updated_call_trace, "messages": final_message, "completed_tasks": state.completed_tasks + [self.name]})
                elif isinstance(final_message, dict) and "content" in final_message:
                    return Command(update={"call_trace": updated_call_trace, "messages": final_message, "completed_tasks": state.completed_tasks + [self.name]})
            
            return Command(update={"call_trace": updated_call_trace, "messages": str(result), "completed_tasks": state.completed_tasks + [self.name]})
            
        except Exception as e:
            # Get the correct chat session for logging
            chat_session = await self.get_chat_session_from_state(state)
            LogFire.log("ERROR", f"Error in SupervisorTask_Create_agent.llm_call: {str(e)}, scheduled_guid: {getattr(state, 'scheduled_guid', 'NOT_FOUND')}", chat=chat_session)
            error_call_trace = state.call_trace + [f"{self.name}: error"]
            # Update request's call trace when supervisor call trace is modified
            await state.update_request_call_trace(error_call_trace)
            
            # Send debug message about the error
            try:
                if user_for_debug:
                    # Use specific session if provided in state, otherwise fall back to user's active session
                    session_for_debug = state.chat_session_guid if hasattr(state, 'chat_session_guid') and state.chat_session_guid else user_for_debug.session_guid
                    user_for_debug.receive_debug_message(f"[{self.name}] CreateAgent error: {str(e)}", session_for_debug, error_call_trace)
            except Exception:
                pass  # Silently fail to avoid disrupting flow
            
            return Command(update={"call_trace": error_call_trace, "messages": f"Error processing request with create_react_agent: {str(e)}", "completed_tasks": state.completed_tasks + [self.name]})
    
    def compile_default(self) -> "SupervisorTask_Base":
        self.langgraph.add_node("llm_call", self.llm_call_wrapper)
        self.langgraph.add_edge(START, "llm_call")
        self.langgraph.add_edge("llm_call", END)
        self.compiled_langgraph = self.langgraph.compile()
        return self


class SupervisorTask_ChainOfThought(SupervisorTask_Base):
    """
    Enhanced supervisor task that implements explicit chain of thought reasoning.
    This class captures and tracks reasoning steps for transparent decision making.
    """
    _cot_prompt_suffix: str = ""
    enable_reasoning_trace: bool = True
    
    def __init__(self, name: str, prompt: str = "", prompt_id: str = "", cot_prompt_suffix: str = "", model: BaseLanguageModel = None):
        super().__init__(name=name, prompt=prompt, prompt_id=prompt_id, model=model)
        self._cot_prompt_suffix = cot_prompt_suffix
    
    def get_cot_prompt(self, prefix_zaira_personality: bool = False):
        """Get the chain of thought enhanced prompt"""
        base_prompt = self.get_prompt(prefix_zaira_personality)
        
        if self._cot_prompt_suffix:
            return f"{base_prompt}\n\n{self._cot_prompt_suffix}"
        
        # Default CoT structure if no custom suffix provided
        cot_suffix = (
            "\n\nUse step-by-step reasoning:\n"
            "1. ANALYSIS: What is being asked?\n"
            "2. CONTEXT: What relevant information do I have?\n"
            "3. APPROACH: How should I handle this request?\n"
            "4. ACTION: What specific action should I take?\n"
            "5. VERIFICATION: Does this approach make sense?\n\n"
            "Think through each step systematically before responding."
        )
        return f"{base_prompt}{cot_suffix}"
    
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task with chain of thought reasoning"""
        user = await ZairaUserManager.find_user(state.user_guid)
        user_info = {"name": getattr(user, 'username', getattr(user, 'user_guid', 'unknown_user'))}
        chat_history = user.get_chat_history()
        
        # Build messages with CoT prompt
        messages = chat_history.copy()
        messages.insert(0, SystemMessage(content=self.get_cot_prompt(True)))
        messages.append(HumanMessage(content=f"User info: {user_info}. Original request: {state.original_input}"))
        
        chat_session = await self.get_chat_session_from_state(state)
        LogFire.log("DEBUG", f"[CoT TASK] {self.name} thinking step-by-step...", chat=chat_session, severity="debug")
        
        # Get LLM response with reasoning
        result = await self.model.ainvoke(input=messages)
        
        # Display the reasoning if it contains CoT structure
        if hasattr(result, 'content') and result.content:
            content = result.content
            LogFire.log("DEBUG", f"[CoT RESPONSE] {self.name} reasoning:\n{'─' * 50}\n{content}\n{'─' * 50}", chat=chat_session, severity="debug")
            
            # Look for numbered reasoning steps
            import re
            steps = re.findall(r'\d+\.\s*[A-Z]+:\s*([^\n]+)', content)
            reasoning_steps = [f"{self.name}: {step}" for step in steps]
            
            if reasoning_steps:
                steps_text = "\n".join(f"   {i}. {step}" for i, step in enumerate(reasoning_steps, 1))
                LogFire.log("DEBUG", f"[CoT STEPS] Extracted {len(reasoning_steps)} reasoning steps:\n{steps_text}", chat=chat_session, severity="debug")
        else:
            reasoning_steps = []
        
        updated_call_trace = state.call_trace + [f"{self.name}: llm_call_cot"]
        # Update request's call trace when supervisor call trace is modified
        await state.update_request_call_trace(updated_call_trace)
        
        # Send debug message summarizing the CoT task execution
        try:
            if user:
                if hasattr(result, 'content') and result.content:
                    response_preview = result.content
                else:
                    response_preview = str(result)
                # Use specific session if provided in state, otherwise fall back to user's active session
                session_for_debug = state.chat_session_guid if hasattr(state, 'chat_session_guid') and state.chat_session_guid else user.session_guid
                user.receive_debug_message(f"CoT Task {self.name} completed with {len(reasoning_steps)} reasoning steps: {response_preview}", session_for_debug, updated_call_trace)
        except Exception:
            pass  # Silently fail to avoid disrupting flow
        
        return Command(update={
            "call_trace": updated_call_trace, 
            "messages": result,
            "reasoning_steps": reasoning_steps,
            "completed_tasks": state.completed_tasks + [self.name]
        })



class SupervisorSupervisor(SupervisorTask_Base):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.ZairaUser import ZairaUser
    _tasks: list[Tuple[int, SupervisorTask_Base]] = []
    _task_map: Dict[str, Tuple[int, SupervisorTask_Base]] = {}
    _lock: Lock = None
    
    def __init__(self, name: str, prompt: str = "", prompt_id = "", tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]] = [], model: BaseLanguageModel = None):
        if model is None:
            model = SupervisorManager.get_instance().default_model
        super().__init__(name=name, prompt=prompt, prompt_id=prompt_id, model=model)

        self.name = name
        self._tasks: list[Tuple[int, SupervisorTask_Base]] = []
        self._task_map: Dict[str, Tuple[int, SupervisorTask_Base]] = {}
        self._lock = Lock()
        self.thread_config.update({"recursion_limit": 2500})
    
    def get_tools(self):
        return []

    class Route(BaseModel):
        step: str = Field(
            None, description="The next step in the routing process"
        )

    async def llm_call_router(self, state: SupervisorTaskState):
        """Route the input to the appropriate node"""
        user = await ZairaUserManager.find_user(state.user_guid)

        # Run the augmented LLM with structured output to serve as routing logic
        tasks: list[str] = []
        for _, task in self._tasks:
            if not task.always_call_FIRST and not task.always_call_LAST:
                tasks.append(F"{task.name}")
        # Build agent-specific prompts
        with self._lock:
            task_prompts = []
            for _, task in self._tasks:
                if not task.always_call_FIRST and not task.always_call_LAST:
                    task_prompts.append(f"Task {task.name}'s prompt: {task.get_prompt()}.")
            previous_tasks = []
            for task_name in state.completed_tasks:
                task = self.get_task(task_name)
                if task:
                    previous_tasks.append(task_name)

        supervisor_brain = "\n".join(task_prompts)
        user_info = {"name": getattr(user, 'username', getattr(user, 'user_guid', 'unknown_user'))}
        chat_history = user.get_chat_history()
        messages = chat_history
        env_prompt = PromptManager.get_prompt("Global_Supervisor_Prompt") # Ends with what to do with the previous_tasks
        messages.insert(0, SystemMessage(env_prompt + ", ".join(previous_tasks) + ". End of previous_tasks." 
                         " Consider the above as your restraints." \
                        f" My prompt: {self.get_prompt(True)}\n" \
                         " User info: " + " ".join(user_info) + "." \
                         " Consider the following prompts as the individual task's logic: " \
                        f"\n{supervisor_brain}"))
        # Context is already available through chat history and system prompts
        decision: SupervisorRouteState = await self.model.with_structured_output(SupervisorRouteState).ainvoke(input=messages)

        # Handle transition to always_call tasks when ending conditional routing
        if decision.step == "END":
            # Check if we have always_call tasks to execute
            always_call_tasks = [task for _, task in self._tasks if task.always_call_LAST == True]
            if always_call_tasks:
                # Transition to first always_call task instead of END
                first_always_task = always_call_tasks[0]
                chat_session = await self.get_chat_session_from_state(state)
                LogFire.log("DEBUG", f"{self.name} transitioning from END to always_call task: {first_always_task.name} [scheduled_guid: {getattr(state, 'scheduled_guid', 'NOT_FOUND')}]", chat=chat_session, severity="debug")
                updated_call_trace = state.call_trace + [f"{self.name}: goto {first_always_task.name} (always_call)"]
                # Update request's call trace when supervisor call trace is modified
                await state.update_request_call_trace(updated_call_trace)
                return Command(update={"call_trace": updated_call_trace, "completed_tasks": [self.name]}, goto=first_always_task.name)
            else:
                chat_session = await self.get_chat_session_from_state(state)
                LogFire.log("DEBUG", f"{self.name} no always_call tasks found, going to END [scheduled_guid: {getattr(state, 'scheduled_guid', 'NOT_FOUND')}]", chat=chat_session, severity="debug")
                updated_call_trace = state.call_trace + [f"{self.name}: goto END"]
                # Update request's call trace when supervisor call trace is modified
                await state.update_request_call_trace(updated_call_trace)
                return Command(update={"call_trace": updated_call_trace, "completed_tasks": [self.name]}, goto="END")

        updated_call_trace = state.call_trace + [f"{self.name}: goto {decision.step}"]
        # Update request's call trace when supervisor call trace is modified
        await state.update_request_call_trace(updated_call_trace)
        return Command(update={"call_trace": updated_call_trace, "completed_tasks": [self.name]}, goto=decision.step)
    
    async def llm_call_router_wrapper(self, state: SupervisorTaskState):
        if Globals.is_debug_values():
            # Get the correct chat session for logging
            chat_session = await self.get_chat_session_from_state(state)
            LogFire.log("TASK", f"Supervisor {self.name} [scheduled_guid: {getattr(state, 'scheduled_guid', 'NOT_FOUND')}] ", state.messages[-1].content.strip(), chat=chat_session)
        result = await self.llm_call_router(state)
        if result is None:
            result = "END"
        if isinstance(result, Command):
            return result
        # If string or similar was returned, wrap it neatly with the langgraph Command class
        ret_val = {}
        updated_call_trace = state.call_trace + [f"{self.name}: llm_call_router"]
        ret_val.update({"call_trace": updated_call_trace})
        # Update request's call trace when supervisor call trace is modified
        await state.update_request_call_trace(updated_call_trace)
        ret_val.update({"completed_tasks": state.completed_tasks + [self.name]})
            
        return Command(update=ret_val, goto=result)
        
    def compile(self) -> "SupervisorSupervisor":
        for _, task in self._tasks:
            if task.compiled_langgraph is None:
                task.compile_default()

        self.langgraph = StateGraph(state_schema=SupervisorTaskState, config_schema=SupervisorTaskConfig)
        
        # Separate conditional and always_call tasks (original logic)
        conditional_tasks = [task for _, task in self._tasks if task.always_call_LAST == False and task.always_call_FIRST == False]
        always_call_FIRST_tasks = [task for _, task in self._tasks if task.always_call_FIRST == True]
        always_call_LAST_tasks = [task for _, task in self._tasks if task.always_call_LAST == True]
        
        # Add the supervisor router node and task nodes
        self.langgraph.add_node("llm_call_router", self.llm_call_router_wrapper)
        for _, task in self._tasks:
            self.langgraph.add_node(task.name, task.llm_call_internal)
        
        def end_func(state):
            return Command(update="")
        self.langgraph.add_node("END", end_func)

        current_task = START
        if always_call_FIRST_tasks:
            # Chain always_call_FIRST tasks in sequence
            for task in always_call_FIRST_tasks:
                self.langgraph.add_edge(current_task, task.name)
                current_task = task.name
        
        # Handle conditional routing tasks
        if conditional_tasks:
            # Start with router for conditional tasks  
            self.langgraph.add_edge(current_task, "llm_call_router")
            # Conditional tasks return to router after completion
            for task in conditional_tasks:
                self.langgraph.add_edge(task.name, "llm_call_router")
        
        # Handle always_call_LAST tasks
        if always_call_LAST_tasks:
            first_always_task = always_call_LAST_tasks[0]
            
            if conditional_tasks:
                # Router can transition to first always_call task when it decides to END
                pass  # No direct edge needed - router uses goto
            else:
                # No conditional tasks, start directly with first always_call task
                self.langgraph.add_edge(current_task, first_always_task.name)
            
            # Chain always_call_LAST tasks in sequence
            for i in range(len(always_call_LAST_tasks) - 1):
                current_task_last = always_call_LAST_tasks[i]
                next_task = always_call_LAST_tasks[i + 1]
                self.langgraph.add_edge(current_task_last.name, next_task.name)
            
            # Last always_call task goes to END
            last_always_task = always_call_LAST_tasks[-1]
            self.langgraph.add_edge(last_always_task.name, END)
        
        # Always ensure END node routes to LangGraph END for router goto commands
        self.langgraph.add_edge("END", END)
        
        # Special case: no tasks at all, go directly to END
        if not conditional_tasks and not always_call_FIRST_tasks and not always_call_LAST_tasks:
            self.langgraph.add_edge(START, END)
        
        self.compiled_langgraph = self.langgraph.compile(name=self.name)
        
        return self
    
    async def llm_call_internal(self, state: SupervisorTaskState):
        output: list = []
        #state.call_trace = []
        #state.completed_sections = []
        #state.completed_tasks = []
        #state.call_trace.append(f"{self.name}: enter")
        async for stream_response in self.compiled_langgraph.astream(
            input=state, 
            config=self.thread_config,
            stream_mode="updates",
        ):
            # When Human-in-the-loop is called, we have no response just yet
            if stream_response is None:
                return Command()
            state.call_trace = stream_response["call_trace"]
            output.append(stream_response)
            if Globals.is_debug():
                chat_session = await self.get_chat_session_from_state(state)
                LogFire.log("DEBUG", "----", chat=chat_session, severity="debug")
                debug_print = ""
                if "messages" in stream_response:
                    response = stream_response["messages"][-1]
                    if isinstance(response, etc.helper_functions.get_any_message_as_type()):
                        response = response.content
                    if len(stream_response["call_trace"]) > 0:
                        debug_print = stream_response["call_trace"][-1] + " --> " + response
                else:
                    debug_print = stream_response["call_trace"][-1]
                chat_session = await self.get_chat_session_from_state(state)
                LogFire.log("DEBUG", f"{debug_print}\n----", chat=chat_session, severity="debug")
        
        #state.call_trace.append(f"{self.name}: exit")
        call_trace = state.call_trace
        
        # Display reasoning summary if this is a CoT supervisor
        if hasattr(self, 'enable_cot_routing') and self.enable_cot_routing and hasattr(state, 'reasoning_steps') and state.reasoning_steps:
            steps_summary = "\n".join(f"{i:2d}. {step}" for i, step in enumerate(state.reasoning_steps, 1))
            chat_session = await self.get_chat_session_from_state(state)
            LogFire.log("DEBUG", f"[CoT SUMMARY] {self.name} reasoning trail:\n{'=' * 60}\n{steps_summary}\n{'=' * 60}", chat=chat_session, severity="debug")
        
        response = ""
        # Update request's call trace when supervisor call trace is modified
        await state.update_request_call_trace(call_trace)
        if len(output) > 0:
            if "messages" in output[-1]:
                return Command(update={"call_trace": call_trace, "messages": output[-1]["messages"][-1], "completed_tasks": state.completed_tasks + [self.name]})
        return Command(update={"call_trace": call_trace, "completed_tasks": state.completed_tasks + [self.name]})
    
    async def call_supervisor(self, query: str, user: "ZairaUser", scheduled_guid: UUID, chat_session_guid: Optional[UUID] = None, original_source: Optional[str] = None):
        """Function to be called from outwith the Supervisors"""
        langgraph = StateGraph(SupervisorTaskState)
        langgraph.add_node("llm_call_internal", self.llm_call_internal)
        langgraph.add_edge(START, "llm_call_internal")
        langgraph.add_edge("llm_call_internal", END)
        
        user_guid = str(user.user_guid) if user else ""
        # Use provided chat_session_guid or fall back to user's active session
        session_guid = chat_session_guid if chat_session_guid else (user.session_guid if user else None)
        state = SupervisorTaskState(original_input=query, user_guid=user_guid, scheduled_guid=scheduled_guid, chat_session_guid=session_guid, messages=[HumanMessage(query)], call_trace=[f"{self.name}: start"], original_source=original_source)
        compiled_langgraph = langgraph.compile()
        result = await compiled_langgraph.ainvoke(state)

        response = result["messages"][-1]
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        
        # Store the latest call trace for access during execution (for scheduled tasks)
        self._last_call_trace = result["call_trace"]
        
        return {"result": response, "call_trace": result["call_trace"]}
    
    async def call_supervisor_with_state(self, state: SupervisorTaskState):
        """Function to be called from outwith the Supervisors"""
        langgraph = StateGraph(SupervisorTaskState)
        langgraph.add_node("llm_call_internal", self.llm_call_internal)
        langgraph.add_edge(START, "llm_call_internal")
        langgraph.add_edge("llm_call_internal", END)
        compiled_langgraph = langgraph.compile()
        result = await compiled_langgraph.ainvoke(state)

        response = result["messages"][-1]
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        
        return {"result": response, "call_trace": result["call_trace"]}
    
    def add_task(self, task: SupervisorTask_Base, priority: int = -1) -> "SupervisorSupervisor":
        if priority == -1:
            priority = 100 + len(self._tasks) # If no priority is specified, add it to the end of the list while also being behind any tasks that DO have a specific priority
        with self._lock:
            if task.name in self._task_map:
                # Remove existing task with same name first
                existing_entry = self._task_map[task.name]
                self._tasks.remove(existing_entry)
                self._task_map.pop(task.name)
            entry = (priority, task)
            bisect.insort(self._tasks, entry)
            self._task_map[task.name] = entry
        return self

    def remove_task(self, scheduled_guid: UUID) -> Optional[SupervisorTask_Base]:
        with self._lock:
            # Find the task by scheduled_guid first
            task_to_remove = None
            for task_name, entry in self._task_map.items():
                if entry[1].scheduled_guid == scheduled_guid:
                    task_to_remove = (task_name, entry)
                    break
            
            if task_to_remove:
                task_name, entry = task_to_remove
                self._task_map.pop(task_name, None)
                index = bisect.bisect_left(self._tasks, entry)
                if index < len(self._tasks) and self._tasks[index][1].scheduled_guid == scheduled_guid:
                    self._tasks.pop(index)
                return entry[1]
            return None

    def update_task_priority(self, scheduled_guid: UUID, new_priority: int):
        with self._lock:
            task = self.remove_task(scheduled_guid)
            if task:
                self.add_task(task, new_priority)

    def get_tasks(self) -> list[SupervisorTask_Base]:
        with self._lock:
            return [entry[1] for entry in self._tasks]

    def get_tasks_with_priorities(self) -> list[Tuple[int, SupervisorTask_Base]]:
        with self._lock:
            return list(self._tasks)

    def has_task(self, scheduled_guid: UUID) -> bool:
        with self._lock:
            for task_name, entry in self._task_map.items():
                if entry[1].scheduled_guid == scheduled_guid:
                    return True
        return False
    
    def get_task(self, task_name: str) -> Optional[SupervisorTask_Base]:
        """Returns the task object by its name."""
        for _, task in self._tasks:
            if task.name == task_name:
                return task
        return None

    def find_task(self, scheduled_guid: UUID) -> Optional[SupervisorTask_Base]:
        """Returns the task object by its scheduled_guid."""
        with self._lock:
            for task_name, entry in self._task_map.items():
                if entry[1].scheduled_guid == scheduled_guid:
                    return entry[1]
        return None

    def get_task_priority(self, scheduled_guid: UUID) -> Optional[int]:
        with self._lock:
            for task_name, entry in self._task_map.items():
                if entry[1].scheduled_guid == scheduled_guid:
                    return entry[0]
        return None

    def __repr__(self):
        return f"Supervisor({self.name}, tasks={[t[1].scheduled_guid for t in self._tasks]})"


class SupervisorSupervisor_ChainOfThought(SupervisorSupervisor):
    """
    Enhanced supervisor that implements chain of thought reasoning for task routing decisions.
    """
    enable_cot_routing: bool = True
    _last_reasoning_hash: Optional[str] = None  # Track last reasoning to prevent duplicate logs
    
    def __init__(self, name: str, prompt: str = "", prompt_id: str = "", tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]] = [], model: BaseLanguageModel = None):
        super().__init__(name=name, prompt=prompt, prompt_id=prompt_id, tools=tools, model=model)
    
    async def _perform_cot_reasoning(self, state: SupervisorTaskState, user, conditional_tasks, always_call_FIRST_tasks, always_call_LAST_tasks):
        """Perform the initial Chain of Thought reasoning to determine which task to execute"""
        # Build task information for reasoning
        all_tasks = []
        task_prompts = []
        
        with self._lock:
            # Include all tasks in the reasoning
            for _, task in self._tasks:
                if not task.always_call_FIRST and not task.always_call_LAST:
                    all_tasks.append(task.name)
                    task_prompts.append(f"Task {task.name}'s prompt: {task.get_prompt()}.")
        
        supervisor_brain = "\n".join(task_prompts)
        user_info = {"name": getattr(user, 'username', getattr(user, 'user_guid', 'unknown_user'))}
        chat_history = user.get_chat_history()
        messages = chat_history.copy()
        
        # Add the user's original input as a message if not already in chat history
        if state.original_input and not any(msg.content == state.original_input for msg in messages if hasattr(msg, 'content')):
            messages.insert(0, HumanMessage(content=state.original_input))
        
        # Use the CoT prompt for reasoning
        env_prompt = PromptManager.get_prompt("Top_Supervisor_CoT_Prompt")
        
        full_system_prompt = (
            env_prompt + "\n\n"
            "IMPORTANT: Based on the user's request, identify which task should be executed to fulfill their needs. "
            "Consider the task mapping and choose the most appropriate task. "
            "Your choice will be executed AFTER the always_call_FIRST tasks complete.\n\n"
            " User info: " + " ".join(user_info) + "." 
            " Consider the following prompts as the individual task's logic: " 
            f"\n{supervisor_brain}"
        )
        
        messages.insert(0, SystemMessage(full_system_prompt))
        
        # Get the reasoning and decision
        chat_session = await self.get_chat_session_from_state(state)
        LogFire.log("DEBUG", f"{self.name} performing CoT reasoning with original_input: {state.original_input} [scheduled_guid: {getattr(state, 'scheduled_guid', 'NOT_FOUND')}]", chat=chat_session, severity="debug")
        
        if self.enable_cot_routing:
            # Create a hash of current reasoning context to avoid duplicate logging
            import hashlib
            reasoning_context = f"{state.original_input}_{len(state.completed_tasks)}"
            current_reasoning_hash = hashlib.md5(reasoning_context.encode()).hexdigest()
            
            # Only log if this is a new reasoning cycle
            is_new_reasoning = current_reasoning_hash != self._last_reasoning_hash
            
            if is_new_reasoning:
                chat_session = await self.get_chat_session_from_state(state)
                LogFire.log("DEBUG", f"[CoT REASONING] {self.name} starting chain of thought for task selection...", chat=chat_session, severity="debug")
            
            try:
                # First get the raw reasoning response
                raw_response = await self.model.ainvoke(input=messages)
                if hasattr(raw_response, 'content'):
                    # Only log thinking if it's actually different from previous
                    if is_new_reasoning:
                        LogFire.log("DEBUG", f"[CoT THINKING] {self.name} task selection reasoning:\n{'-' * 60}\n{raw_response.content}\n{'-' * 60}", chat=chat_session, severity="debug")
                        self._last_reasoning_hash = current_reasoning_hash
                    
                    # Always ask for structured output after reasoning to ensure reliability
                    messages_with_reasoning = messages.copy()
                    messages_with_reasoning.append(raw_response)
                    messages_with_reasoning.append(SystemMessage(content=f"Based on your reasoning above, which ONE task should be executed? Choose from: {', '.join(all_tasks)} or END. Respond with ONLY the exact task name."))
                    
                    decision_response = await self.model.ainvoke(input=messages_with_reasoning)
                    if hasattr(decision_response, 'content'):
                        chosen_task = decision_response.content.strip()
                        
                        # Send debug message summarizing the CoT reasoning and decision
                        try:
                            user_for_debug = await ZairaUserManager.find_user(state.user_guid)
                            if user_for_debug:
                                # Use specific session if provided in state, otherwise fall back to user's active session
                                session_for_debug = state.chat_session_guid if hasattr(state, 'chat_session_guid') and state.chat_session_guid else user.session_guid
                                user_for_debug.receive_debug_message(f"[{self.name}] {raw_response.content} >>>>>> {chosen_task}", session_for_debug, state.call_trace + [f"{self.name}: CoT reasoning"])
                        except Exception:
                            pass  # Silently fail to avoid disrupting flow
                        
                        # Validate the chosen task
                        if chosen_task in all_tasks:
                            return chosen_task
                        elif chosen_task.upper() == "END":
                            return "END"
                        else:
                            # Try to find a partial match
                            for task_name in all_tasks:
                                if task_name.lower() in chosen_task.lower() or chosen_task.lower() in task_name.lower():
                                    LogFire.log("DEBUG", f"[{self.name}] CoT found partial match: {chosen_task} -> {task_name}", chat=chat_session, severity="debug")
                                    return task_name
                            
                            LogFire.log("DEBUG", f"[{self.name}] CoT could not match '{chosen_task}' to any task, defaulting to END", chat=chat_session, severity="debug")
                            return "END"
                    
                    return "END"
                    
            except Exception as e:
                LogFire.log("ERROR", f"[CoT ERROR] {self.name} reasoning failed: {e}", chat=chat_session, severity="error")
                # Send debug message about CoT failure
                try:
                    user_for_debug = await ZairaUserManager.find_user(state.user_guid)
                    if user_for_debug:
                        # Use specific session if provided in state, otherwise fall back to user's active session
                        session_for_debug = state.chat_session_guid if hasattr(state, 'chat_session_guid') and state.chat_session_guid else user_for_debug.session_guid
                        user_for_debug.receive_debug_message(f"[{self.name}] CoT Supervisor reasoning failed: {str(e)}", session_for_debug, state.call_trace + [f"{self.name}: CoT error"])
                except Exception:
                    pass  # Silently fail to avoid disrupting flow
                return None
        
        return None
    
    def compile(self) -> "SupervisorSupervisor":
        """Override compile to route all tasks through CoT reasoning"""
        for _, task in self._tasks:
            if task.compiled_langgraph is None:
                task.compile_default()

        self.langgraph = StateGraph(state_schema=SupervisorTaskState, config_schema=SupervisorTaskConfig)
        
        # All tasks are routed through CoT reasoning - no automatic execution
        all_tasks = [task for _, task in self._tasks]
        
        # Add the supervisor router node and task nodes
        self.langgraph.add_node("llm_call_router", self.llm_call_router_wrapper)
        for _, task in self._tasks:
            self.langgraph.add_node(task.name, task.llm_call_internal)
        
        def end_func(state):
            return Command(update="")
        self.langgraph.add_node("END", end_func)

        # All execution starts with CoT router - no bypassing for always_call_FIRST
        self.langgraph.add_edge(START, "llm_call_router")
        
        # All tasks return to router after completion for continued CoT reasoning
        for task in all_tasks:
            self.langgraph.add_edge(task.name, "llm_call_router")
        
        # Router handles END transitions via goto commands in CoT logic
        self.langgraph.add_edge("END", END)
        
        self.compiled_langgraph = self.langgraph.compile(name=self.name)
        
        return self
    
    async def llm_call_router(self, state: SupervisorTaskState):
        """Route the input to the appropriate node using chain of thought reasoning"""
        user = await ZairaUserManager.find_user(state.user_guid)
        
        # Separate tasks by priority
        always_call_FIRST_tasks = [task for _, task in self._tasks if task.always_call_FIRST == True]
        always_call_LAST_tasks = [task for _, task in self._tasks if task.always_call_LAST == True]
        conditional_tasks = [task for _, task in self._tasks if task.always_call_FIRST == False and task.always_call_LAST == False]
        
        # Handle workflow progression based on current stage
        chat_session = await self.get_chat_session_from_state(state)
        LogFire.log("DEBUG", f"{self.name} (CoT) workflow stage: {state.cot_workflow_stage} [scheduled_guid: {getattr(state, 'scheduled_guid', 'NOT_FOUND')}]", chat=chat_session, severity="debug")
        
        if state.cot_workflow_stage == "initial":
            # First time through - do the reasoning to choose the appropriate task
            LogFire.log("DEBUG", f"{self.name} (CoT) performing initial reasoning to choose task", chat=chat_session, severity="debug")
            
            # Build task information and perform reasoning
            chosen_task = await self._perform_cot_reasoning(state, user, conditional_tasks, always_call_FIRST_tasks, always_call_LAST_tasks)
            
            # Store the chosen task in persistent state
            state.cot_chosen_task = chosen_task
            LogFire.log("DEBUG", f"{self.name} (CoT) chose task: {chosen_task} [scheduled_guid: {getattr(state, 'scheduled_guid', 'NOT_FOUND')}]", chat=chat_session, severity="debug")
            
            # Progress to next stage
            if always_call_FIRST_tasks:
                state.cot_workflow_stage = "always_first"
            elif chosen_task and chosen_task != "END":
                state.cot_workflow_stage = "chosen_task"
            elif always_call_LAST_tasks:
                state.cot_workflow_stage = "always_last"
            else:
                state.cot_workflow_stage = "complete"
        
        # Execute always_call_FIRST tasks
        if state.cot_workflow_stage == "always_first":
            unexecuted_always_call_FIRST = [task for task in always_call_FIRST_tasks 
                                           if task.name not in state.completed_tasks]
            
            if unexecuted_always_call_FIRST:
                first_task = unexecuted_always_call_FIRST[0]
                LogFire.log("DEBUG", f"{self.name} (CoT) executing next always_call_FIRST task: {first_task.name} (no re-reasoning)", chat=chat_session, severity="debug")
                reasoning_steps = []
                if self.enable_cot_routing:
                    reasoning_steps = [f"{self.name}: CoT routing to {first_task.name} (always_call_FIRST)"]
                updated_call_trace = state.call_trace + [f"{self.name}: goto {first_task.name} (always_call_FIRST)"]
                # Update request's call trace when supervisor call trace is modified
                await state.update_request_call_trace(updated_call_trace)
                return Command(update={
                    "call_trace": updated_call_trace, 
                    "reasoning_steps": reasoning_steps,
                    "completed_tasks": state.completed_tasks + [self.name],
                    "cot_workflow_stage": state.cot_workflow_stage,  # Maintain stage
                    "cot_chosen_task": state.cot_chosen_task  # Maintain chosen task
                }, goto=first_task.name)
            else:
                # All always_call_FIRST tasks completed, move to next stage
                if state.cot_chosen_task and state.cot_chosen_task != "END":
                    state.cot_workflow_stage = "chosen_task"
                elif always_call_LAST_tasks:
                    state.cot_workflow_stage = "always_last"
                else:
                    state.cot_workflow_stage = "complete"

        # Execute the chosen task
        if state.cot_workflow_stage == "chosen_task":
            chosen_task = state.cot_chosen_task
            if chosen_task and chosen_task not in state.completed_tasks and chosen_task != "END":
                # Find the task object
                task_obj = None
                for _, task in self._tasks:
                    if task.name == chosen_task:
                        task_obj = task
                        break
                
                if task_obj and not task_obj.always_call_LAST:  # Don't execute always_call_LAST tasks here
                    chat_session = await self.get_chat_session_from_state(state)
                    LogFire.log("DEBUG", f"{self.name} (CoT) executing chosen task: {chosen_task}", chat=chat_session, severity="debug")
                    reasoning_steps = []
                    if self.enable_cot_routing:
                        reasoning_steps = [f"{self.name}: CoT routing to chosen task {chosen_task}"]
                    updated_call_trace = state.call_trace + [f"{self.name}: goto {chosen_task} (chosen task)"]
                    # Update request's call trace when supervisor call trace is modified
                    await state.update_request_call_trace(updated_call_trace, f"Activating {chosen_task}")
                    
                    # Progress to next stage after chosen task execution
                    next_stage = "always_last" if always_call_LAST_tasks else "complete"
                    return Command(update={
                        "call_trace": updated_call_trace,
                        "reasoning_steps": reasoning_steps,
                        "completed_tasks": state.completed_tasks + [self.name],
                        "cot_workflow_stage": next_stage,
                        "cot_chosen_task": state.cot_chosen_task
                    }, goto=chosen_task)
            
            # Chosen task was completed or is invalid, move to next stage
            if always_call_LAST_tasks:
                state.cot_workflow_stage = "always_last"
            else:
                state.cot_workflow_stage = "complete"
        
        # Execute always_call_LAST tasks
        if state.cot_workflow_stage == "always_last":
            unexecuted_always_call_LAST = [task for _, task in self._tasks 
                                          if task.always_call_LAST == True and task.name not in state.completed_tasks]
            
            if unexecuted_always_call_LAST:
                first_always_task = unexecuted_always_call_LAST[0]
                chat_session = await self.get_chat_session_from_state(state)
                LogFire.log("DEBUG", f"{self.name} (CoT) executing next always_call_LAST task: {first_always_task.name}", chat=chat_session, severity="debug")
                reasoning_steps = []
                if self.enable_cot_routing:
                    reasoning_steps = [f"{self.name}: CoT routing to {first_always_task.name} (always_call_LAST)"]
                updated_call_trace = state.call_trace + [f"{self.name}: goto {first_always_task.name} (always_call_LAST)"]
                # Update request's call trace when supervisor call trace is modified
                await state.update_request_call_trace(updated_call_trace)
                return Command(update={
                    "call_trace": updated_call_trace,
                    "reasoning_steps": reasoning_steps,
                    "completed_tasks": state.completed_tasks + [self.name],
                    "cot_workflow_stage": state.cot_workflow_stage,  # Maintain stage  
                    "cot_chosen_task": state.cot_chosen_task  # Maintain chosen task
                }, goto=first_always_task.name)
            else:
                # All always_call_LAST tasks completed
                state.cot_workflow_stage = "complete"
        
        # Workflow complete - end execution
        LogFire.log("DEBUG", f"{self.name} (CoT) workflow complete, going to END", chat=chat_session, severity="debug")
        reasoning_steps = []
        if self.enable_cot_routing:
            reasoning_steps = [f"{self.name}: CoT workflow complete"]
        updated_call_trace = state.call_trace + [f"{self.name}: goto END"]
        # Update request's call trace when supervisor call trace is modified
        await state.update_request_call_trace(updated_call_trace)
        return Command(update={
            "call_trace": updated_call_trace,
            "reasoning_steps": reasoning_steps,
            "completed_tasks": state.completed_tasks + [self.name],
            "cot_workflow_stage": "complete",
            "cot_chosen_task": state.cot_chosen_task
        }, goto="END")


class _SupervisorManagerMeta(type):
    _instance = None
    _lock: Lock = None

    def __call__(cls, *args, **kwargs):
        if cls._lock is None:
            cls._lock = Lock()

        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__call__(*args, **kwargs)
        return cls._instance

class SupervisorManager(metaclass=_SupervisorManagerMeta):
    # Initialize model
    _instance = None
    _initialized = False

    default_model: BaseLanguageModel = None
    _supervisors: Dict[str, SupervisorSupervisor] = {}
    _all_tasks: Dict[str, SupervisorTask_Base] = {}
    _lock: Lock = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls) -> "SupervisorManager":
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        instance.default_model = ZairaSettings.llm
        instance._supervisors = {}
        instance._all_tasks = {}
        instance._lock = Lock()

        instance._initialized = True

    @classmethod
    def register_task(cls, task: SupervisorTask_Base) -> SupervisorTask_Base:
        instance = cls.get_instance()
        with instance._lock:
            instance._all_tasks[task.name] = task
        return task

    @classmethod
    def register_supervisor(cls, supervisor: SupervisorSupervisor) -> SupervisorSupervisor:
        instance = cls.get_instance()
        with instance._lock:
            instance._supervisors[supervisor.name] = supervisor
            #instance._all_tasks[supervisor.scheduled_guid] = supervisor # All supervisors can also be used as a task, not sure if we should add them to the list
        return supervisor

    @classmethod
    def get_supervisor(cls, name: str) -> Optional[SupervisorSupervisor]:
        instance = cls.get_instance()
        return instance._supervisors.get(name)

    @classmethod
    def add_task_to_supervisor(cls, task: SupervisorTask_Base, supervisor_name: str, priority: int):
        cls.register_task(task)
        supervisor = cls.get_supervisor(supervisor_name)
        if not supervisor:
            raise ValueError(f"Supervisor '{supervisor_name}' not found.")
        supervisor.add_task(task, priority)

    @classmethod
    def transfer_task(cls, scheduled_guid: UUID, from_supervisor: str, to_supervisor: str, new_priority: Optional[int] = None):
        src = cls.get_supervisor(from_supervisor)
        dst = cls.get_supervisor(to_supervisor)
        if not src or not dst:
            raise ValueError("One or both supervisors not found.")
        task = src.remove_task(scheduled_guid)
        if task:
            priority = new_priority if new_priority is not None else 0
            dst.add_task(task, priority)
        else:
            raise ValueError(f"Task {scheduled_guid} not found in {from_supervisor}.")

    @classmethod
    def find_task(cls, scheduled_guid: UUID) -> Optional[SupervisorTask_Base]:
        instance = cls.get_instance()
        with instance._lock:
            for task_name, task in instance._all_tasks.items():
                if task.scheduled_guid == scheduled_guid:
                    return task
        return None
    
    @classmethod
    def get_task(cls, task_name: str) -> Optional[SupervisorTask_Base]:
        instance = cls.get_instance()
        with instance._lock:
            return instance._all_tasks.get(task_name)

    @classmethod
    def get_all_tasks(cls) -> list[SupervisorTask_Base]:
        instance = cls.get_instance()
        with instance._lock:
            return list(instance._all_tasks.values())

    @classmethod
    def __repr__(cls):
        instance = cls.get_instance()
        return f"Manager(supervisors={list(instance._supervisors.keys())}, all_tasks={list(instance._all_tasks.keys())})"
    
#from userprofiles.ZairaUser import ZairaUser
#SupervisorTaskState.model_rebuild()
