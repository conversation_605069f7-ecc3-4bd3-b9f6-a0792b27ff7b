from imports import *

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from abc import ABC, abstractmethod

class ProcessingOutputDataBase(BaseModel, ABC):
    """Base class for data passed from processing tasks to output tasks"""
    
    # Common fields for all processing → output workflows  
    processed_at: datetime = Field(default_factory=datetime.now)
    user_approved: bool = Field(default=False)
    output_sent: bool = Field(default=False)
    sent_at: Optional[datetime] = Field(default=None)
    processing_metadata: Dict[str, Any] = Field(default_factory=dict)
    
    @abstractmethod
    def get_output_type(self) -> str:
        """Return the output type for routing (e.g., 'email', 'agenda')"""
        pass
    
    @abstractmethod  
    def get_preview_text(self) -> str:
        """Return human-readable preview for approval"""
        pass
        
    def mark_approved(self) -> None:
        """Mark this data as user-approved"""
        self.user_approved = True
        
    def mark_sent(self) -> None:
        """Mark this data as successfully sent"""
        self.output_sent = True
        self.sent_at = datetime.now()

    class Config:
        validate_assignment = True