"""
Comprehensive integration tests for WhatsApp bot functionality
"""
import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from aiohttp import web
from imports import *

@pytest.mark.integration
@pytest.mark.asyncio
class TestWhatsAppBotIntegration:
    """Test WhatsApp bot integration workflows"""
    
    async def test_whatsapp_bot_initialization(self, mock_external_services, mock_whatsapp_api):
        """Test WhatsApp bot initialization and setup"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from endpoints.api_endpoint import APIEndpoint
        
        # Mock API endpoint setup
        with patch.object(APIEndpoint, 'get_instance') as mock_api:
            mock_api_instance = AsyncMock()
            mock_api_instance.aio_app = MagicMock()
            mock_api_instance.aio_app.add_routes = MagicMock()
            mock_api.return_value = mock_api_instance
            
            # Test bot setup
            bot = MyWhatsappBot()
            await MyWhatsappBot.setup()
            
            # Verify routes were added
            mock_api_instance.aio_app.add_routes.assert_called_once()
            
            # Verify bot is initialized
            assert MyWhatsappBot._initialized is True
            assert bot.bot_generic is not None

    async def test_webhook_verification_flow(self, mock_external_services):
        """Test WhatsApp webhook verification process"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        
        # Mock verification request
        mock_request = MagicMock()
        mock_request.remote = "127.0.0.1"
        mock_request.query_string = "hub.mode=subscribe&hub.verify_token=12346&hub.challenge=test_challenge"
        
        # Test verification
        response = await bot.whatsapp_verify(mock_request)
        
        # Should return the challenge for valid token
        assert response.status == 200
        assert response.text == "test_challenge"

    async def test_webhook_message_processing(self, mock_external_services, sample_whatsapp_webhook_data, sample_whatsapp_user_data):
        """Test complete webhook message processing workflow"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Mock user manager
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user, \
             patch.object(ZairaUserManager, 'add_user') as mock_add_user:
            
            # Mock user creation for new users
            mock_user = AsyncMock()
            mock_user.on_message = AsyncMock()
            mock_add_user.return_value = mock_user
            mock_get_user.return_value = None  # Simulate new user
            
            # Mock request
            mock_request = AsyncMock()
            mock_request.content_type = 'application/json'
            mock_request.json.return_value = sample_whatsapp_webhook_data
            
            bot = MyWhatsappBot()
            
            # Process webhook
            response = await bot.whatsapp_webhook(mock_request)
            
            # Verify response
            assert response.status == 200
            
            # Verify user creation was attempted
            mock_add_user.assert_called_once()
            
            # Verify message processing was triggered
            await asyncio.sleep(0.1)  # Allow async processing
            mock_user.on_message.assert_called_once()

    async def test_message_sending_flow(self, mock_whatsapp_api):
        """Test WhatsApp message sending functionality"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Test successful message sending
        result = await MyWhatsappBot.send_a_whatsapp_message(
            to_number="31611239487",
            message="Test message from bot"
        )
        
        # Verify API call was made
        mock_whatsapp_api['client'].post.assert_called_once()
        
        # Verify call parameters
        call_args = mock_whatsapp_api['client'].post.call_args
        assert "graph.facebook.com" in call_args[1]['url']
        assert call_args[1]['json']['messaging_product'] == 'whatsapp'
        assert call_args[1]['json']['to'] == '31611239487'
        assert call_args[1]['json']['text']['body'] == 'Test message from bot'
        
        # Should return True for successful send
        assert result is True

    async def test_message_sending_error_handling(self, mock_whatsapp_api):
        """Test error handling in message sending"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Mock failed API response
        mock_whatsapp_api['response'].status_code = 400
        mock_whatsapp_api['response'].text = '{"error": {"message": "Invalid phone number"}}'
        
        # Test failed message sending
        result = await MyWhatsappBot.send_a_whatsapp_message(
            to_number="invalid_number",
            message="Test message"
        )
        
        # Should return False for failed send
        assert result is False

    async def test_rag_integration_workflow(self, mock_external_services, mock_rag_components, sample_whatsapp_user_data):
        """Test WhatsApp bot integration with RAG system"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        # Mock user with RAG capabilities
        mock_user = AsyncMock()
        mock_user.on_message = AsyncMock()
        
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            # Simulate message processing with RAG
            await MyWhatsappBot.on_message(
                text="What is your purpose?",
                sender_id="31611239487",
                original_message="31611239487"
            )
            
            # Verify user message processing was called
            mock_user.on_message.assert_called_once()
            
            # Verify RAG components were used
            call_args = mock_user.on_message.call_args
            assert call_args[1]['complete_message'] == "What is your purpose?"
            assert call_args[1]['calling_bot'] is not None

@pytest.mark.integration
@pytest.mark.asyncio
class TestWhatsAppOAuthIntegration:
    """Test WhatsApp OAuth configuration integration"""
    
    async def test_oauth_configuration_loading(self, mock_external_services):
        """Test loading WhatsApp configuration from OAuth"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from endpoints.oauth._verifier_ import OAuth2Verifier
        
        # Mock OAuth configuration
        mock_config = {
            "str1": "test_phone_number_id",
            "str3": "test_verify_token", 
            "str4": "test_access_token"
        }
        
        with patch.object(OAuth2Verifier, 'get_full_token') as mock_oauth:
            mock_oauth.return_value = mock_config
            
            # Test configuration loading during setup
            await MyWhatsappBot.setup()
            
            # Verify OAuth was queried
            mock_oauth.assert_called_with("whatsapp")

    async def test_oauth_fallback_to_config(self, mock_external_services):
        """Test fallback to config.py when OAuth is unavailable"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from endpoints.oauth._verifier_ import OAuth2Verifier
        
        with patch.object(OAuth2Verifier, 'get_full_token') as mock_oauth:
            # Mock OAuth failure
            mock_oauth.side_effect = Exception("OAuth not available")
            
            # Test configuration loading during setup
            await MyWhatsappBot.setup()
            
            # Should not raise exception and fall back to config.py values
            assert MyWhatsappBot._initialized is True

@pytest.mark.integration
@pytest.mark.asyncio
class TestWhatsAppErrorHandling:
    """Test WhatsApp bot error handling scenarios"""
    
    async def test_malformed_webhook_data(self, mock_external_services):
        """Test handling of malformed webhook data"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        
        # Test with invalid JSON
        mock_request = AsyncMock()
        mock_request.content_type = 'application/json'
        mock_request.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
        
        response = await bot.whatsapp_webhook(mock_request)
        assert response.status == 400

    async def test_missing_message_data(self, mock_external_services):
        """Test handling of webhook data without message content"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        bot = MyWhatsappBot()
        
        # Test with empty webhook data
        mock_request = AsyncMock()
        mock_request.content_type = 'application/json'
        mock_request.json.return_value = {"object": "whatsapp_business_account", "entry": []}
        
        response = await bot.whatsapp_webhook(mock_request)
        assert response.status == 200  # Should handle gracefully

    async def test_user_creation_failure(self, mock_external_services, sample_whatsapp_webhook_data):
        """Test handling of user creation failures"""
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        from managers.manager_users import ZairaUserManager
        
        with patch.object(ZairaUserManager, 'get_user') as mock_get_user, \
             patch.object(ZairaUserManager, 'add_user') as mock_add_user:
            
            # Mock user creation failure
            mock_get_user.return_value = None
            mock_add_user.side_effect = Exception("Database error")
            
            # Should handle gracefully without crashing
            await MyWhatsappBot.process_webhook(sample_whatsapp_webhook_data)
            
            # Verify error was handled (no exception raised)
            assert True  # Test passes if no exception is raised
