"""
Unit tests for SystemUserManager
Tests SYSTEM user creation and environment-specific scheduled task management
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from uuid import UUID
from datetime import datetime, timedelta

from managers.manager_system_user import SystemUserManager
from managers.manager_users import ZairaUserManager
from managers.manager_scheduled_tasks import get_persistence_manager
from userprofiles.permission_levels import PERMISSION_LEVELS
from userprofiles.ZairaUser import ZairaUser


class TestSystemUserManager:
    """Unit tests for the SystemUserManager"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Reset singleton instances for testing
        SystemUserManager._instance = None
        SystemUserManager._initialized = False
        
        # Mock environment variables
        self.original_debug = getattr(Globals, '_debug', False)
        
    def teardown_method(self):
        """Clean up after tests"""
        # Restore original state
        if hasattr(Globals, '_debug'):
            Globals._debug = self.original_debug
    
    def test_system_user_manager_singleton(self):
        """Test SystemUserManager singleton pattern"""
        manager1 = SystemUserManager.get_instance()
        manager2 = SystemUserManager.get_instance()
        
        assert manager1 is manager2
        assert isinstance(manager1, SystemUserManager)
        
        # Test fixed GUIDs
        assert manager1.SYSTEM_USER_GUID == UUID("00000000-0000-0000-0000-000000000001")
        assert manager1.SYSTEM_DEVICE_GUID == UUID("00000000-0000-0000-0000-000000000002")
        
        print("✓ SystemUserManager singleton pattern test passed")
    
    @pytest.mark.asyncio
    async def test_system_user_creation(self):
        """Test SYSTEM user creation"""
        
        # Mock dependencies
        with patch('managers.manager_users.ZairaUserManager.add_user') as mock_add_user:
            with patch('managers.manager_scheduled_tasks.get_persistence_manager') as mock_persistence:
                
                # Create mock user
                mock_user = MagicMock(spec=ZairaUser)
                mock_user.user_guid = str(SystemUserManager.SYSTEM_USER_GUID)
                mock_add_user.return_value = mock_user
                
                # Create mock persistence manager
                mock_persistence_manager = AsyncMock()
                mock_persistence.return_value = mock_persistence_manager
                
                # Mock environment detection
                with patch('etc.helper_functions.is_claude_environment', return_value=True):
                    with patch.object(Globals, 'is_docker', return_value=False):
                        with patch.object(Globals, 'is_debug', return_value=False):
                            
                            # Setup system user manager
                            await SystemUserManager.setup()
                            
                            # Verify SYSTEM user was created
                            mock_add_user.assert_called_once()
                            call_args = mock_add_user.call_args[1]
                            
                            assert call_args['username'] == "SYSTEM"
                            assert call_args['rank'] == PERMISSION_LEVELS.ADMIN
                            assert call_args['guid'] == SystemUserManager.SYSTEM_USER_GUID
                            assert call_args['device_guid'] == SystemUserManager.SYSTEM_DEVICE_GUID
                            
                            # Verify user properties were set
                            assert mock_user.email == "<EMAIL>"
                            assert mock_user.platform == "SYSTEM"
                            assert mock_user.is_system_user == True
                            
                            print("✓ SYSTEM user creation test passed")
    
    @pytest.mark.asyncio
    async def test_environment_detection_claude(self):
        """Test environment detection for Claude environment"""
        
        with patch('managers.manager_users.ZairaUserManager.add_user') as mock_add_user:
            with patch('managers.manager_scheduled_tasks.get_persistence_manager') as mock_persistence:
                
                mock_user = MagicMock(spec=ZairaUser)
                mock_add_user.return_value = mock_user
                
                mock_persistence_manager = AsyncMock()
                mock_persistence.return_value = mock_persistence_manager
                
                # Mock Claude environment
                with patch('etc.helper_functions.is_claude_environment', return_value=True):
                    with patch.object(Globals, 'is_docker', return_value=False):
                        with patch.object(Globals, 'is_debug', return_value=False):
                            
                            manager = SystemUserManager.get_instance()
                            await manager.setup()
                            
                            # In Claude environment, no scheduled tasks should be created
                            # We can't easily test this without more complex mocking,
                            # but we can verify the manager was initialized
                            assert manager._initialized == True
                            
                            print("✓ Claude environment detection test passed")
    
    def test_weekly_delay_calculation(self):
        """Test weekly schedule delay calculation"""
        
        manager = SystemUserManager.get_instance()
        
        # Test various schedule formats
        test_cases = [
            "weekly monday 08:00",
            "weekly tuesday 09:30", 
            "weekly friday 17:00",
            "weekly sunday 20:00"
        ]
        
        for schedule in test_cases:
            delay = manager._calculate_weekly_delay(schedule)
            
            # Delay should be positive and reasonable (within 1 week)
            assert delay > 0
            assert delay <= 7 * 24 * 60 * 60  # Max 1 week
            assert delay >= 60  # Minimum 1 minute
            
            print(f"✓ Schedule '{schedule}' -> {delay/3600:.1f} hours delay")
        
        # Test invalid format
        invalid_delay = manager._calculate_weekly_delay("invalid format")
        assert invalid_delay == 7 * 24 * 60 * 60  # Should default to 1 week
        
        print("✓ Weekly delay calculation test passed")

    @pytest.mark.asyncio
    async def test_system_user_cleanup(self):
        """Test cleanup of system tasks"""
        
        # Create a fresh manager instance to avoid interference  
        SystemUserManager._instance = None
        SystemUserManager._initialized = False
        manager = SystemUserManager.get_instance()
        
        # Mock tasks
        mock_task1 = MagicMock()
        mock_task1.task_guid = "task-1"
        mock_task2 = MagicMock()
        mock_task2.task_guid = "task-2"
        
        mock_persistence_manager = AsyncMock()
        mock_persistence_manager.get_active_tasks.return_value = [mock_task1, mock_task2]
        mock_persistence_manager.cancel_task.return_value = None
        
        # Patch the cleanup_system_tasks method's internal get_persistence_manager call
        with patch('managers.manager_system_user.get_persistence_manager', return_value=mock_persistence_manager):
            
            # Run cleanup
            await manager.cleanup_system_tasks()
            
            # Verify both tasks were cancelled
            assert mock_persistence_manager.cancel_task.call_count == 2
            mock_persistence_manager.cancel_task.assert_any_call("task-1", "System cleanup")
            mock_persistence_manager.cancel_task.assert_any_call("task-2", "System cleanup")
            
            print("✓ System task cleanup test passed")


if __name__ == "__main__":
    # Run tests if called directly
    pytest.main([__file__, "-v"])