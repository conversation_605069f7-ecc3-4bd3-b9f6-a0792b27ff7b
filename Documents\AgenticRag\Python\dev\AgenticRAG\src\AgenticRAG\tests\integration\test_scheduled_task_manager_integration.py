# tests/integration/test_scheduled_task_manager_integration.py
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
from tasks.inputs.task_scheduled_task_manager import SupervisorTask_ScheduledTaskManager
from managers.manager_supervisors import SupervisorTaskState
from userprofiles.ZairaUser import ZairaUser
from langchain_core.messages import HumanMessage
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4


class TestScheduledTaskManagerIntegration:
    """Integration tests for ScheduledTaskManager with full workflow"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.user_guid = str(uuid4())
        self.user = MagicMock(spec=ZairaUser)
        self.user.GUID = self.user_guid
        self.user.username = "test_user"
        self.user.get_chat_history.return_value = []
    
    @pytest.mark.asyncio
    async def test_create_task_workflow(self):
        """Test complete workflow for creating a scheduled task"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        # Mock the dependencies
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch('managers.manager_users.ZairaUserManager.find_user') as mock_find_user, \
             patch('userprofiles.ScheduledZairaTask.ScheduledZairaTask') as mock_task_class, \
             patch('asyncio.create_task') as mock_create_task:
            
            mock_find_user = AsyncMock(return_value=self.user)
            mock_task = MagicMock()
            mock_task.task_id = str(uuid4())
            mock_task.run_task = AsyncMock()  # Mock the async run_task method
            mock_task_class.return_value = mock_task
            
            # Test natural language request for creating a task
            state = SupervisorTaskState(
                original_input="Remind me to call John in 2 hours",
                user_guid=str(self.user.GUID),
                messages=[]
            )
            result = await manager.llm_call(state)
            
            # Verify the workflow executed
            assert isinstance(result, str)
    
    @pytest.mark.asyncio
    async def test_list_tasks_workflow(self):
        """Test complete workflow for listing scheduled tasks"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        mock_tasks = [
            {
                'task_id': str(uuid4()),
                'schedule_prompt': 'Send reminder in 30 minutes',
                'target_prompt': 'Send reminder',
                'next_execution': '2024-01-01 10:00:00',
                'created_at': '2024-01-01 09:30:00'
            }
        ]
        
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch.object(ScheduledTaskPersistenceManager, 'get_instance') as mock_get_instance:
            mock_persistence = MagicMock()
            mock_persistence.get_active_tasks = AsyncMock(return_value=mock_tasks)
            mock_get_instance.return_value = mock_persistence
            
            state = SupervisorTaskState(
                original_input="Show me all my scheduled tasks",
                user_guid=str(self.user.GUID),
                messages=[]
            )
            result = await manager.llm_call(state)
            
            assert isinstance(result, str)
    
    @pytest.mark.asyncio
    async def test_cancel_task_workflow(self):
        """Test complete workflow for canceling a scheduled task"""
        manager = SupervisorTask_ScheduledTaskManager()
        task_id = str(uuid4())
        
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch.object(ScheduledTaskPersistenceManager, 'get_instance') as mock_get_instance:
            mock_persistence = MagicMock()
            mock_persistence.cancel_task = AsyncMock(return_value=True)
            mock_get_instance.return_value = mock_persistence
            
            state = SupervisorTaskState(
                original_input=f"Cancel task {task_id}",
                user_guid=str(self.user.GUID),
                messages=[]
            )
            result = await manager.llm_call(state)
            
            assert isinstance(result, str)
    
    @pytest.mark.asyncio
    async def test_info_request_workflow(self):
        """Test complete workflow for getting scheduled task information"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch('managers.manager_users.ZairaUserManager.find_user') as mock_find_user:
            
            mock_find_user = AsyncMock(return_value=self.user)
            
            state = SupervisorTaskState(
                original_input="What can you do with scheduled tasks?",
                user_guid=str(self.user.GUID),
                messages=[]
            )
            result = await manager.llm_call(state)
            
            assert isinstance(result, str)
    
    @pytest.mark.asyncio
    async def test_intelligent_routing_without_hardcoded_patterns(self):
        """Test that routing works without hardcoded query patterns"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        # Test various phrasings that should route to the same task
        create_variations = [
            "I need a reminder in 30 minutes",
            "Set up a task for tomorrow",
            "Schedule something for next week",
            "Create a new automated task"
        ]
        
        list_variations = [
            "What tasks do I have?",
            "Display my scheduled items",
            "Show active automations",
            "What's on my schedule?"
        ]
        
        cancel_variations = [
            "Remove task 123",
            "Delete scheduled item 456",
            "Stop automation 789",
            "Disable task abc"
        ]
        
        info_variations = [
            "How do scheduled tasks work?",
            "Tell me about automation features",
            "What are the capabilities?",
            "Help with scheduling"
        ]
        
        # Test that manager can handle all variations
        # (We're not testing actual routing here, just that the manager accepts them)
        all_variations = create_variations + list_variations + cancel_variations + info_variations
        
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch('managers.manager_users.ZairaUserManager.find_user') as mock_find_user:
            
            mock_find_user = AsyncMock(return_value=self.user)
            
            for query in all_variations:
                try:
                    state = SupervisorTaskState(
                        original_input=query,
                        user_guid=str(self.user.GUID),
                        messages=[]
                    )
                    result = await manager.llm_call(state)
                    assert isinstance(result, str)
                except Exception as e:
                    pytest.fail(f"Manager failed to handle query '{query}': {e}")
    
    @pytest.mark.asyncio
    async def test_state_preservation_across_tasks(self):
        """Test that state is properly preserved across supervisor task execution"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch.object(ScheduledTaskPersistenceManager, 'get_instance') as mock_get_instance:
            mock_persistence = MagicMock()
            mock_persistence.get_active_tasks = AsyncMock(return_value=[])
            mock_get_instance.return_value = mock_persistence
            
            state = SupervisorTaskState(
                original_input="List my tasks",
                user_guid=str(self.user.GUID),
                messages=[]
            )
            result = await manager.llm_call(state)
            
            # Verify result structure
            assert isinstance(result, str)
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self):
        """Test error handling in complete integration workflow"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        # Test with various error conditions
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch.object(ScheduledTaskPersistenceManager, 'get_instance') as mock_get_instance:
            # Simulate database error
            mock_get_instance.side_effect = Exception("Database connection failed")
            
            state = SupervisorTaskState(
                original_input="List my tasks",
                user_guid=str(self.user.GUID),
                messages=[]
            )
            result = await manager.llm_call(state)
            
            assert isinstance(result, str)
            assert "error" in result.lower() or "failed" in result.lower()
    
    @pytest.mark.asyncio
    async def test_concurrent_request_handling(self):
        """Test that manager can handle concurrent requests"""
        manager = SupervisorTask_ScheduledTaskManager()
        
        # Create multiple users
        users = []
        for i in range(3):
            user = MagicMock(spec=ZairaUser)
            user.GUID = str(uuid4())
            user.username = f"test_user_{i}"
            user.get_chat_history.return_value = []
            users.append(user)
        
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch.object(ScheduledTaskPersistenceManager, 'get_instance') as mock_get_instance:
            mock_persistence = MagicMock()
            mock_persistence.get_active_tasks = AsyncMock(return_value=[])
            mock_get_instance.return_value = mock_persistence
            
            # Create concurrent requests
            tasks = []
            for i, user in enumerate(users):
                state = SupervisorTaskState(
                    original_input=f"List tasks for user {i}",
                    user_guid=str(user.GUID),
                    messages=[]
                )
                task = asyncio.create_task(
                    manager.llm_call(state)
                )
                tasks.append(task)
            
            # Wait for all requests to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Verify all requests completed successfully
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    pytest.fail(f"Request {i} failed with exception: {result}")
                assert isinstance(result, str)
    
    def test_manager_setup_integration(self):
        """Test that manager setup integrates properly with the application"""
        # Verify task manager can be instantiated
        task_manager = SupervisorTask_ScheduledTaskManager()
        persistence_manager = ScheduledTaskPersistenceManager.get_instance()
        
        assert task_manager is not None
        assert persistence_manager is not None
        assert hasattr(task_manager, '_tasks')
        assert hasattr(task_manager, '_task_map')
        assert hasattr(task_manager, '_lock')
    
    @pytest.mark.asyncio
    async def test_persistence_manager_integration(self):
        """Test integration with ScheduledTaskPersistenceManager"""
        # Verify that both managers work together
        task_manager = SupervisorTask_ScheduledTaskManager()
        persistence_manager = ScheduledTaskPersistenceManager.get_instance()
        
        assert task_manager is not None
        assert persistence_manager is not None
        
        # Verify persistence manager maintains singleton pattern
        assert persistence_manager is ScheduledTaskPersistenceManager.get_instance()
    
    @pytest.mark.asyncio
    async def test_full_lifecycle_simulation(self):
        """Simulate a full lifecycle of scheduled task operations"""
        manager = SupervisorTask_ScheduledTaskManager()
        task_id = str(uuid4())
        
        # Step 1: Create a task
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch('managers.manager_users.ZairaUserManager.find_user') as mock_find_user, \
             patch('userprofiles.ScheduledZairaTask.ScheduledZairaTask') as mock_task_class, \
             patch('asyncio.create_task'):
            
            mock_find_user = AsyncMock(return_value=self.user)
            mock_task = MagicMock()
            mock_task.task_id = task_id
            mock_task.run_task = AsyncMock()  # Mock the async run_task method
            mock_task_class.return_value = mock_task
            
            state = SupervisorTaskState(
                original_input="Remind me to check emails in 1 hour",
                user_guid=str(self.user.GUID),
                messages=[]
            )
            create_result = await manager.llm_call(state)
            
            assert isinstance(create_result, str)
        
        # Step 2: List tasks
        mock_tasks = [{
            'task_id': task_id,
            'schedule_prompt': 'Remind me to check emails in 1 hour',
            'target_prompt': 'Remind to check emails',
            'next_execution': '2024-01-01 11:00:00',
            'created_at': '2024-01-01 10:00:00'
        }]
        
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch.object(ScheduledTaskPersistenceManager, 'get_instance') as mock_get_instance:
            mock_persistence = MagicMock()
            mock_persistence.get_active_tasks = AsyncMock(return_value=mock_tasks)
            mock_get_instance.return_value = mock_persistence
            
            state = SupervisorTaskState(
                original_input="Show me my scheduled tasks",
                user_guid=str(self.user.GUID),
                messages=[]
            )
            list_result = await manager.llm_call(state)
            
            assert isinstance(list_result, str)
        
        # Step 3: Cancel the task
        with patch('etc.helper_functions.exit') as mock_exit, \
             patch.object(ScheduledTaskPersistenceManager, 'get_instance') as mock_get_instance:
            mock_persistence = MagicMock()
            mock_persistence.cancel_task = AsyncMock(return_value=True)
            mock_get_instance.return_value = mock_persistence
            
            state = SupervisorTaskState(
                original_input=f"Cancel task {task_id}",
                user_guid=str(self.user.GUID),
                messages=[]
            )
            cancel_result = await manager.llm_call(state)
            
            assert isinstance(cancel_result, str)
        
        # Verify all steps completed without errors
        assert all(isinstance(result, str) for result in [create_result, list_result, cancel_result])